<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
    <control note="控件">
        <variable note="控件">
            <item position="" bind="underlying_code" value="510300.SH" note="标的代码(如510300.SH)" name="标的代码" type="intput"/>
            <item position="" bind="signal_threshold" value="5" note="连续同方向tick数量(如5表示连续5个上涨或下跌触发信号)" name="连续信号阈值" type="intput"/>
            <item position="" bind="oscillation_period_size" value="5" note="每个震荡周期的tick数量" name="震荡周期大小" type="intput"/>
            <item position="" bind="oscillation_periods" value="3" note="需要的连续同方向周期数量" name="震荡周期数量" type="intput"/>
            <item position="" bind="min_days_to_expire" value="7" note="合约最少剩余天数(少于此天数选择下月合约)" name="最少剩余天数" type="intput"/>
            <item position="" bind="max_position_per_contract" value="1" note="单个合约最大持仓量(张)" name="单合约最大持仓" type="intput"/>
            <item position="" bind="order_timeout_seconds" value="5" note="委托超时时间(秒)" name="委托超时时间" type="intput"/>
            <item position="" bind="enable_real_trading" value="1" note="启用真实交易(1开0关,谨慎使用)" name="真实交易开关" type="intput"/>
        </variable>
    </control>
</TCStageLayout>
