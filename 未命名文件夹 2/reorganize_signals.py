#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新整理信号数据
在现有CSV基础上添加盈亏状态，并重新排列：盈利、亏损、卖出信号分组
"""

import pandas as pd
import numpy as np

def reorganize_signals():
    """重新整理信号数据"""
    
    # 读取现有的Excel文件
    input_file = '/Users/<USER>/Desktop/workspace/options/买入信号含价格跟踪数据.xlsx'
    df = pd.read_excel(input_file)
    
    print(f"📊 读取信号数据: {len(df)}条")
    
    # 提取5分钟后涨跌幅数值
    df['5分钟后涨跌数值'] = df['5分钟后涨跌幅'].str.replace('%', '').astype(float)
    
    # 添加盈亏状态
    conditions = [
        df['5分钟后涨跌数值'] > 2.0,   # 大幅盈利
        df['5分钟后涨跌数值'] > 0.5,   # 小幅盈利
        df['5分钟后涨跌数值'] >= -0.5,  # 基本持平
        df['5分钟后涨跌数值'] >= -2.0,  # 小幅亏损
        df['5分钟后涨跌数值'] < -2.0    # 大幅亏损
    ]
    
    choices = ['大幅盈利', '小幅盈利', '基本持平', '小幅亏损', '大幅亏损']
    df['盈亏状态'] = np.select(conditions, choices, default='未知')
    
    # 添加简单的盈亏标记
    def get_profit_status(row):
        if row['5分钟后涨跌数值'] > 0:
            return '盈利'
        elif row['5分钟后涨跌数值'] < 0:
            return '亏损'
        else:
            return '持平'
    
    df['盈亏'] = df.apply(get_profit_status, axis=1)
    
    # 分离买入和卖出信号（支持新的信号类型格式）
    buy_signals = df[df['信号类型'].str.contains('买入', na=False)].copy()
    sell_signals = df[df['信号类型'].str.contains('卖出', na=False)].copy()
    
    print(f"   买入信号: {len(buy_signals)}条")
    print(f"   卖出信号: {len(sell_signals)}条")
    
    # 买入信号按盈亏分类并排序
    buy_profit = buy_signals[buy_signals['盈亏'] == '盈利'].copy()
    buy_loss = buy_signals[buy_signals['盈亏'] == '亏损'].copy()
    buy_flat = buy_signals[buy_signals['盈亏'] == '持平'].copy()
    
    # 按5分钟后涨跌幅排序
    buy_profit = buy_profit.sort_values('5分钟后涨跌数值', ascending=False)  # 盈利从高到低
    buy_loss = buy_loss.sort_values('5分钟后涨跌数值', ascending=True)      # 亏损从大到小
    buy_flat = buy_flat.sort_values('序号')  # 持平按序号排序
    
    # 卖出信号按5分钟后涨跌幅排序（从高到低）
    sell_signals = sell_signals.sort_values('5分钟后涨跌数值', ascending=False)
    
    # 重新组合数据：盈利买入 + 亏损买入 + 持平买入 + 卖出信号
    reorganized_df = pd.concat([buy_profit, buy_loss, buy_flat, sell_signals], ignore_index=True)
    
    # 添加分组标识（可选，便于查看）
    group_labels = []
    group_labels.extend(['买入-盈利'] * len(buy_profit))
    group_labels.extend(['买入-亏损'] * len(buy_loss))
    group_labels.extend(['买入-持平'] * len(buy_flat))
    group_labels.extend(['卖出信号'] * len(sell_signals))
    
    reorganized_df['分组'] = group_labels
    
    # 保存重新整理后的文件
    output_file = '/Users/<USER>/Desktop/workspace/options/买入信号含价格跟踪数据_整理版.csv'
    reorganized_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    
    # 显示统计信息
    print(f"\n📈 买入信号统计:")
    print(f"   盈利: {len(buy_profit)}条 ({len(buy_profit)/len(buy_signals)*100:.1f}%)")
    print(f"   亏损: {len(buy_loss)}条 ({len(buy_loss)/len(buy_signals)*100:.1f}%)")
    print(f"   持平: {len(buy_flat)}条 ({len(buy_flat)/len(buy_signals)*100:.1f}%)")
    
    print(f"\n📉 卖出信号: {len(sell_signals)}条")
    sell_profit_count = len(sell_signals[sell_signals['盈亏'] == '盈利'])
    sell_loss_count = len(sell_signals[sell_signals['盈亏'] == '亏损'])
    sell_flat_count = len(sell_signals[sell_signals['盈亏'] == '持平'])
    
    print(f"   其中盈利: {sell_profit_count}条 ({sell_profit_count/len(sell_signals)*100:.1f}%)")
    print(f"   其中亏损: {sell_loss_count}条 ({sell_loss_count/len(sell_signals)*100:.1f}%)")
    print(f"   其中持平: {sell_flat_count}条 ({sell_flat_count/len(sell_signals)*100:.1f}%)")
    
    # 显示最佳和最差表现
    if len(buy_profit) > 0:
        best_buy = buy_profit.iloc[0]
        print(f"\n🚀 最佳买入信号: 第{best_buy['序号']}次 | {best_buy['模式分类']} | +{best_buy['5分钟后涨跌数值']:.2f}%")
    
    if len(buy_loss) > 0:
        worst_buy = buy_loss.iloc[0]
        print(f"❌ 最差买入信号: 第{worst_buy['序号']}次 | {worst_buy['模式分类']} | {worst_buy['5分钟后涨跌数值']:.2f}%")
    
    if len(sell_signals) > 0:
        best_sell = sell_signals.iloc[0]
        print(f"📊 最佳卖出信号: 第{best_sell['序号']}次 | {best_sell['模式分类']} | {best_sell['5分钟后涨跌数值']:+.2f}%")
    
    print(f"\n✅ 文件已保存: {output_file}")
    print(f"📋 数据排列顺序:")
    print(f"   1. 买入-盈利 ({len(buy_profit)}条) - 按收益率从高到低")
    print(f"   2. 买入-亏损 ({len(buy_loss)}条) - 按亏损幅度从大到小")
    print(f"   3. 买入-持平 ({len(buy_flat)}条) - 按序号排序")
    print(f"   4. 卖出信号 ({len(sell_signals)}条) - 按5分钟后涨跌幅从高到低")

def main():
    """主函数"""
    print("🚀 开始重新整理信号数据...")
    reorganize_signals()
    print("\n✅ 整理完成！")

if __name__ == "__main__":
    main()
