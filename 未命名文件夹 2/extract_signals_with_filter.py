#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取带过滤器信息的信号数据
基于现有的extract_signals.py，添加过滤器决策信息
"""

import re
import pandas as pd
from datetime import datetime

def extract_signals_with_filter_info(log_file='1.txt'):
    """提取信号数据并添加过滤器信息"""
    
    print("🔍 提取带过滤器信息的信号数据...")
    
    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        try:
            with open(log_file, 'r', encoding='gbk') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ 无法读取日志文件: {e}")
            return []
    
    signals = []
    
    # 分割信号块
    signal_blocks = re.split(r'第(\d+)次买入信号 - (买入|卖出)', content)[1:]  # 去掉第一个空元素
    
    for i in range(0, len(signal_blocks), 3):  # 每3个元素为一组：序号、类型、内容
        if i + 2 >= len(signal_blocks):
            break
            
        signal_num = int(signal_blocks[i])
        signal_action = signal_blocks[i + 1]
        block_content = signal_blocks[i + 2]
        
        # 只处理买入信号
        if signal_action != '买入':
            continue
        
        # 提取基本信息
        trigger_time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', block_content)
        signal_type_match = re.search(r'信号类型: (\w+)信号', block_content)
        buy_price_match = re.search(r'买入价格: ([\d.]+)', block_content)
        vwap_match = re.search(r'当时VWAP: ([\d.]+)', block_content)
        price_diff_match = re.search(r'价格差异: ([+-][\d.]+) \(([+-][\d.]+)%\)', block_content)
        
        if not all([trigger_time_match, buy_price_match]):
            continue
        
        trigger_time = trigger_time_match.group(1)
        signal_type = signal_type_match.group(1) if signal_type_match else "未知"
        buy_price = float(buy_price_match.group(1))
        current_vwap = float(vwap_match.group(1)) if vwap_match else 0.0
        price_diff = float(price_diff_match.group(1)) if price_diff_match else 0.0
        diff_pct = price_diff_match.group(2) if price_diff_match else "0.00%"
        
        # 提取VWAP模式分析
        pattern_type = "未知"
        quality = "未知"
        timing = "未知"
        category = "未知"
        
        # 查找VWAP模式分析部分
        vwap_analysis_match = re.search(r'VWAP模式分析:(.*?)(?=触发序列|$)', block_content, re.DOTALL)
        if vwap_analysis_match:
            vwap_analysis = vwap_analysis_match.group(1)
            
            pattern_match = re.search(r'- 模式类型: ([^\n-]+)', vwap_analysis)
            if pattern_match:
                pattern_type = pattern_match.group(1).strip()
            
            quality_match = re.search(r'- 买入质量: ([^\n-]+)', vwap_analysis)
            if quality_match:
                quality = quality_match.group(1).strip()
            
            timing_match = re.search(r'- 时机评估: ([^\n-]+)', vwap_analysis)
            if timing_match:
                timing = timing_match.group(1).strip()
            
            # 判断模式分类
            if "向上穿过VWAP" in pattern_type or "向上突破" in timing:
                category = "A类-向上突破"
            elif "高点回撤" in pattern_type or "回撤" in timing:
                category = "B类-高点回撤"
            elif "反弹" in pattern_type or "反弹" in timing:
                category = "C类-低点反弹"
        
        # 提取触发序列
        trigger_sequence = ""
        trigger_match = re.search(r'触发序列: ([^\n]+)', block_content)
        if trigger_match:
            trigger_sequence = trigger_match.group(1).strip()
        
        # 提取震荡周期信息
        oscillation_info = ""
        if "震荡周期信息" in block_content:
            osc_match = re.search(r'震荡周期信息:([^\n]+)', block_content)
            if osc_match:
                oscillation_info = osc_match.group(1).strip()
        
        # 提取过滤器决策
        filter_passed = False
        filter_reason = "未知"
        
        if "VWAP过滤器通过" in block_content:
            filter_passed = True
            reason_match = re.search(r'VWAP过滤器通过: (通过:.*?)(?=\n)', block_content)
            if reason_match:
                filter_reason = reason_match.group(1).strip()
            else:
                filter_reason = "通过"
        elif "VWAP过滤器拒绝买入" in block_content:
            filter_passed = False
            reason_match = re.search(r'VWAP过滤器拒绝买入: (.*?)(?=\n)', block_content)
            if reason_match:
                filter_reason = reason_match.group(1).strip()
            else:
                filter_reason = "拒绝"
        
        # 分析过滤条件
        try:
            diff_value = float(diff_pct.replace('%', '').replace('+', ''))
        except:
            diff_value = 0.0
        
        # A类条件：向上突破且在1%-5%范围内
        a_condition = (category == "A类-向上突破" and 1.0 <= diff_value <= 5.0)
        
        # D类条件：低点反弹
        d_condition = (category == "C类-低点反弹" and "反弹" in pattern_type)
        
        # E类条件：暂时设为False
        e_condition = False
        
        # B类排除：高点回撤≥2%
        b_exclusion = (category == "B类-高点回撤" and "回撤" in pattern_type)
        
        # 构建信号数据
        signal_data = {
            '序号': signal_num,
            '信号类型': f"{signal_type}买入",
            '触发时间': trigger_time,
            '买入价格': buy_price,
            '当时VWAP': current_vwap,
            '价格差异': price_diff,
            '差异百分比': diff_pct,
            '买入前持仓': f"{signal_num-1}张",
            '买入后持仓': f"{signal_num}张",
            '买入数量': "1张",
            'VWAP模式类型': pattern_type,
            '买入质量': quality,
            '时机评估': timing,
            '模式分类': category,
            '触发序列': trigger_sequence,
            '震荡周期信息': oscillation_info,
            # 价格跟踪数据（暂时为空，可以后续添加）
            '10tick后价格': 0.0,
            '10tick后涨跌': 0.0,
            '10tick后涨跌幅': "0.00%",
            '30tick后价格': 0.0,
            '30tick后涨跌': 0.0,
            '30tick后涨跌幅': "0.00%",
            '50tick后价格': 0.0,
            '50tick后涨跌': 0.0,
            '50tick后涨跌幅': "0.00%",
            '1分钟后价格': 0.0,
            '1分钟后涨跌': 0.0,
            '1分钟后涨跌幅': "0.00%",
            '3分钟后价格': 0.0,
            '3分钟后涨跌': 0.0,
            '3分钟后涨跌幅': "0.00%",
            '5分钟后价格': 0.0,
            '5分钟后涨跌': 0.0,
            '5分钟后涨跌幅': "0.00%",
            '最高价格': 0.0,
            '最高涨幅': "0.00%",
            '最低价格': 0.0,
            '最大回撤': "0.00%",
            '5分钟后涨跌数值': 0.0,
            '盈亏状态': "未知",
            '盈亏': "未知",
            '分组': "未知",
            # 过滤器相关信息
            '通过A类条件': a_condition,
            '通过D类条件': d_condition,
            '通过E类条件': e_condition,
            '被B类排除': b_exclusion,
            '最终通过': filter_passed,
            '过滤原因': filter_reason,
            '实际执行': filter_passed
        }
        
        signals.append(signal_data)
    
    print(f"📊 提取完成: {len(signals)}个买入信号")
    return signals

def calculate_profit_loss(buy_price, price_5min, highest_price, lowest_price):
    """计算盈亏状态"""
    try:
        buy_price = float(buy_price)
        price_5min = float(price_5min) if price_5min and price_5min != 0 else buy_price
        highest_price = float(highest_price) if highest_price and highest_price != 0 else buy_price
        lowest_price = float(lowest_price) if lowest_price and lowest_price != 0 else buy_price

        # 计算5分钟后收益
        profit_5min = ((price_5min - buy_price) / buy_price) * 100

        # 计算最高收益
        max_profit = ((highest_price - buy_price) / buy_price) * 100

        # 计算最大亏损
        max_loss = ((lowest_price - buy_price) / buy_price) * 100

        # 判断盈亏状态
        if profit_5min >= 3.0:
            status = "大幅盈利"
            result = "盈利"
        elif profit_5min >= 1.0:
            status = "小幅盈利"
            result = "盈利"
        elif profit_5min >= -1.0:
            status = "基本持平"
            result = "持平"
        elif profit_5min >= -3.0:
            status = "小幅亏损"
            result = "亏损"
        else:
            status = "大幅亏损"
            result = "亏损"

        # 分组
        if result == "盈利":
            group = "买入-盈利"
        elif result == "持平":
            group = "买入-持平"
        else:
            group = "买入-亏损"

        return status, result, group, profit_5min

    except:
        return "未知", "未知", "未知", 0.0

def merge_with_price_tracking_data(signals):
    """合并价格跟踪数据"""
    print("🔄 尝试合并价格跟踪数据...")

    # 尝试读取现有的价格跟踪数据
    price_tracking_files = [
        '买入信号含价格跟踪数据.xlsx',
        '买入信号含价格跟踪数据_整理版.csv',
        '平衡型配置_详细过滤结果.csv'
    ]

    price_data = None
    for file_name in price_tracking_files:
        try:
            if file_name.endswith('.xlsx'):
                price_data = pd.read_excel(file_name)
            else:
                price_data = pd.read_csv(file_name, encoding='utf-8-sig')
            print(f"✅ 成功读取价格跟踪数据: {file_name}")
            break
        except:
            continue

    if price_data is None:
        print("⚠️ 未找到价格跟踪数据文件，使用默认值")
        return signals

    # 合并数据
    merged_signals = []
    for signal in signals:
        signal_num = signal['序号']

        # 查找对应的价格跟踪数据
        matching_row = price_data[price_data['序号'] == signal_num]

        if not matching_row.empty:
            row = matching_row.iloc[0]

            # 更新价格跟踪相关字段
            price_tracking_fields = [
                '触发序列', '震荡周期信息',
                '10tick后价格', '10tick后涨跌', '10tick后涨跌幅',
                '30tick后价格', '30tick后涨跌', '30tick后涨跌幅',
                '50tick后价格', '50tick后涨跌', '50tick后涨跌幅',
                '1分钟后价格', '1分钟后涨跌', '1分钟后涨跌幅',
                '3分钟后价格', '3分钟后涨跌', '3分钟后涨跌幅',
                '5分钟后价格', '5分钟后涨跌', '5分钟后涨跌幅',
                '最高价格', '最高涨幅', '最低价格', '最大回撤',
                '5分钟后涨跌数值'
            ]

            for field in price_tracking_fields:
                if field in row:
                    signal[field] = row[field]

            # 计算盈亏状态
            buy_price = signal['买入价格']
            price_5min = row.get('5分钟后价格', 0)
            highest_price = row.get('最高价格', 0)
            lowest_price = row.get('最低价格', 0)

            status, result, group, profit_value = calculate_profit_loss(
                buy_price, price_5min, highest_price, lowest_price
            )

            signal['盈亏状态'] = status
            signal['盈亏'] = result
            signal['分组'] = group
            signal['5分钟后涨跌数值'] = profit_value

        merged_signals.append(signal)

    print(f"✅ 价格跟踪数据合并完成")
    return merged_signals

def main():
    """主函数"""
    print("🎯 提取带过滤器信息的信号数据")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

    # 提取信号数据
    signals = extract_signals_with_filter_info()

    if not signals:
        print("❌ 未提取到信号数据")
        return

    # 合并价格跟踪数据
    signals = merge_with_price_tracking_data(signals)

    # 转换为DataFrame
    df = pd.DataFrame(signals)

    # 保存为CSV文件
    output_file = '带过滤器的买入信号数据.csv'
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"📄 信号数据已保存: {output_file}")
    print(f"📊 总买入信号: {len(signals)}个")

    # 统计过滤结果
    passed_signals = df[df['最终通过'] == True]
    rejected_signals = df[df['最终通过'] == False]

    print(f"✅ 通过过滤器: {len(passed_signals)}个")
    print(f"❌ 被过滤器拒绝: {len(rejected_signals)}个")
    print(f"📈 通过率: {len(passed_signals)/len(signals)*100:.1f}%")

    # 显示过滤原因统计
    filter_reasons = df['过滤原因'].value_counts()
    print(f"\n🔍 过滤原因统计:")
    for reason, count in filter_reasons.items():
        print(f"   {reason}: {count}次")

    # 显示模式分类统计
    category_stats = df['模式分类'].value_counts()
    print(f"\n📊 模式分类统计:")
    for category, count in category_stats.items():
        print(f"   {category}: {count}次")

    print(f"\n🎉 CSV文件生成完成！")
    print(f"📁 文件格式与传统分析脚本相同，包含实际过滤结果和价格跟踪数据")

if __name__ == "__main__":
    main()
