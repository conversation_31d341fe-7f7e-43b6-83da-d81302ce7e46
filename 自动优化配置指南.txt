================================================================================
🚀 QMT期权策略自动优化配置指南
================================================================================

现在您的策略已启用自动优化模式，可以继续寻找更好的参数组合！

================================================================================
📋 当前优化配置状态
================================================================================
✅ 运行模式: OPTIMIZE (自动参数优化)
✅ 智能早停: 已启用
✅ 基础参数: 已更新为上次优化的最佳结果
✅ 优化目标: 总盈利最大化

================================================================================
🎯 优化配置选项
================================================================================

### 1. 早停条件配置 (第34-47行)

当前设置:
```python
'early_stopping': {
    'enabled': True,           # 启用早停机制
    'target_win_rate': 80.0,   # 目标胜率80%
    'target_profit': 100.0,    # 目标总盈利100元
    'min_trades': 20,          # 最少交易次数20笔
    'patience': 10,            # 连续10次试验无改进则停止
    'min_trials': 20           # 最少运行20次试验
}
```

**调整建议:**
- 提高目标: `target_win_rate: 90.0, target_profit: 200.0`
- 降低目标: `target_win_rate: 75.0, target_profit: 80.0`
- 更有耐心: `patience: 20, min_trials: 30`
- 更快停止: `patience: 5, min_trials: 10`

### 2. 优化目标配置 (第50行)

当前设置:
```python
OPTIMIZATION_METRIC = 'total_profit'  # 总盈利最大化
```

**可选目标:**
- `'total_profit'` - 总盈利最大化 (推荐)
- `'win_rate'` - 胜率最大化
- `'sharpe_ratio'` - 夏普比率最大化 (风险调整收益)

### 3. 最大试验次数 (第36行)

当前设置:
```python
'n_trials': 100,  # 最大优化次数
```

**调整建议:**
- 快速优化: `n_trials: 50`
- 深度优化: `n_trials: 200`
- 极限优化: `n_trials: 500`

### 4. 测试数据范围 (第22-28行)

当前设置:
```python
'backtest_start_date': '20250825',
'backtest_end_date': '20250825',
'backtest_contracts': ['10009534.SHO'],
```

**扩展测试范围:**
```python
# 多日优化 (更可靠)
'backtest_start_date': '20250819',
'backtest_end_date': '20250825',

# 多合约优化 (更通用)
'backtest_contracts': ['10009534.SHO', '10009543.SHO'],
```

================================================================================
🔧 优化参数空间配置
================================================================================

当前优化的参数范围 (第5478-5502行):

```python
'signal_threshold': trial.suggest_int('signal_threshold', 3, 8),
'vwap_a_min': trial.suggest_float('vwap_a_min', 0.2, 2.0, step=0.1),
'vwap_a_max': trial.suggest_float('vwap_a_max', 2.0, 8.0, step=0.2),
'vwap_b_threshold': trial.suggest_float('vwap_b_threshold', 1.0, 5.0, step=0.2),
'vwap_d_threshold': trial.suggest_float('vwap_d_threshold', 1.0, 5.0, step=0.2),
'a_time_stop_secs': trial.suggest_int('a_time_stop_secs', 120, 480, step=30),
'a_min_profit_for_protect': trial.suggest_float('a_min_profit_for_protect', 5.0, 20.0, step=1.0),
'high_max_loss_ratio': trial.suggest_float('high_max_loss_ratio', -12.0, -5.0, step=0.5),
```

**调整建议:**
- 缩小范围: 围绕已知最佳参数进行精细优化
- 扩大范围: 探索更广泛的参数空间
- 调整步长: 更细致的参数搜索

================================================================================
⚡ 快速优化配置示例
================================================================================

### 配置1: 快速验证优化 (推荐新手)
```python
RUN_MODE = 'OPTIMIZE'
'n_trials': 30,
'target_win_rate': 75.0,
'target_profit': 80.0,
'patience': 5,
'min_trials': 10,
```

### 配置2: 标准优化 (推荐一般使用)
```python
RUN_MODE = 'OPTIMIZE'
'n_trials': 100,
'target_win_rate': 80.0,
'target_profit': 100.0,
'patience': 10,
'min_trials': 20,
```

### 配置3: 深度优化 (追求极致)
```python
RUN_MODE = 'OPTIMIZE'
'n_trials': 200,
'target_win_rate': 85.0,
'target_profit': 150.0,
'patience': 15,
'min_trials': 30,
```

### 配置4: 多合约通用优化
```python
RUN_MODE = 'OPTIMIZE'
'backtest_contracts': ['10009534.SHO', '10009543.SHO'],
'backtest_start_date': '20250819',
'backtest_end_date': '20250825',
'target_win_rate': 80.0,
'target_profit': 200.0,  # 多合约总盈利
```

================================================================================
🎯 优化策略建议
================================================================================

### 渐进式优化策略:

**第1轮: 基础验证**
- 单合约单日
- 较低目标 (胜率75%, 盈利80元)
- 快速试验 (30次)

**第2轮: 扩展测试**
- 单合约多日
- 中等目标 (胜率80%, 盈利100元)
- 标准试验 (100次)

**第3轮: 深度优化**
- 多合约多日
- 高目标 (胜率85%, 盈利150元)
- 深度试验 (200次)

**第4轮: 极限挑战**
- 全面测试
- 极高目标 (胜率90%, 盈利200元)
- 极限试验 (500次)

================================================================================
📊 优化结果分析
================================================================================

优化完成后，关注以下指标:

**必须达标:**
- 胜率 ≥ 目标胜率
- 盈利 ≥ 目标盈利
- 交易数 ≥ 最少交易数

**额外关注:**
- 夏普比率 (风险调整收益)
- 最大回撤
- 参数稳定性
- 不同市场条件下的表现

================================================================================
⚠️ 优化注意事项
================================================================================

1. **过度拟合风险**
   - 避免在单一数据上过度优化
   - 使用多日期、多合约数据验证

2. **计算资源**
   - 深度优化需要较长时间
   - 建议在空闲时间运行

3. **参数合理性**
   - 检查优化后的参数是否合理
   - 避免极端参数值

4. **实盘验证**
   - 优化结果需要实盘验证
   - 从小资金开始测试

================================================================================
🚀 启动优化
================================================================================

现在您可以:

1. **直接运行** - 使用当前配置开始优化
2. **调整配置** - 根据需要修改优化参数
3. **选择策略** - 选择适合的优化策略
4. **监控进度** - 观察优化过程和结果

**启动命令:** 在QMT中运行策略即可开始自动优化

================================================================================
📞 优化过程中的输出
================================================================================

您会看到类似的输出:
```
🎯 早停机制已启用
   目标胜率: ≥80%
   目标盈利: ≥100元
   
✅ 第 15 次试验发现更好结果: 125.30
🎯 目标条件已达成:
   胜率: 83.2% (目标: ≥80%)
   盈利: 125.30元 (目标: ≥100元)
   交易数: 28笔 (目标: ≥20笔)
🎉 找到满足条件的最优参数！在第 15 次试验后停止优化
```

现在您可以开始新一轮的参数优化了！

================================================================================
