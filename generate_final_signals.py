#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键生成最终整理版信号数据的脚本
调用现有的两个脚本：extract_signals.py 和 reorganize_signals.py
"""

import subprocess
import sys
import os

def run_script(script_name, description):
    """运行指定的脚本"""
    print(f"🚀 {description}...")
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              cwd='/Users/<USER>/Desktop/workspace/options')
        
        if result.returncode == 0:
            print(f"✅ {description}完成")
            if result.stdout:
                print(result.stdout)
        else:
            print(f"❌ {description}失败")
            if result.stderr:
                print(f"错误信息: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 运行{script_name}时出错: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🎯 开始生成最终整理版信号数据...")
    print("=" * 60)
    
    # 检查工作目录
    work_dir = '/Users/<USER>/Desktop/workspace/options'
    if not os.path.exists(work_dir):
        print(f"❌ 工作目录不存在: {work_dir}")
        return
    
    # 检查必要文件
    required_files = [
        '1.txt',  # 日志文件
        'extract_signals.py',  # 提取脚本
        'reorganize_signals.py'  # 重组脚本
    ]
    
    for file_name in required_files:
        file_path = os.path.join(work_dir, file_name)
        if not os.path.exists(file_path):
            print(f"❌ 必要文件不存在: {file_name}")
            return
    
    print("📋 文件检查完成，开始处理...")
    print()
    
    # 第一步：提取信号数据
    if not run_script('extract_signals.py', '第一步：提取信号数据'):
        return
    
    print()
    
    # 第二步：重组信号数据
    if not run_script('reorganize_signals.py', '第二步：重组信号数据'):
        return
    
    print()
    print("🎉 所有步骤完成！")
    print("📄 最终文件: 买入信号含价格跟踪数据_整理版.csv")
    
    # 检查最终文件
    final_file = os.path.join(work_dir, '买入信号含价格跟踪数据_整理版.csv')
    if os.path.exists(final_file):
        file_size = os.path.getsize(final_file)
        print(f"📊 文件大小: {file_size:,} 字节")
        
        # 简单统计行数
        try:
            with open(final_file, 'r', encoding='utf-8-sig') as f:
                line_count = sum(1 for _ in f)
            print(f"📈 数据行数: {line_count-1} 条记录 (不含表头)")
        except Exception as e:
            print(f"⚠️  无法读取文件统计: {e}")
    else:
        print("❌ 最终文件未生成")

if __name__ == "__main__":
    main()
