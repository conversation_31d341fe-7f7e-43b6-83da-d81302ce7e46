#coding:gbk
from datetime import datetime
import time
import logging

# 简化日志配置 - 只记录重要事件
logging.basicConfig(level=logging.WARNING, format='%(asctime)s %(message)s', datefmt='%H:%M:%S')

# ==================== 回测配置参数 ====================
# 回测模式配置
BACKTEST_CONFIG = {
    'enable_backtest': True,           # 启用回测模式
    'backtest_start_date': '20250707', # 回测开始日期
    'backtest_end_date': '20250707',   # 回测结束日期
    'download_history_data': True,     # 自动下载历史数据
}

# ==================== 参数管理模块 ====================
class ParameterManager:
    """参数管理器 - 回测简化版本"""
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(ParameterManager, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if ParameterManager._initialized:
            return
        ParameterManager._initialized = True

        # 核心可配置参数（回测模式使用内置参数）
        self.params = {
            # 核心配置参数
            'underlying_code': '510300.SH',    # 标的代码
            'signal_threshold': 5,             # 连续同方向tick数量

            # 震荡检测参数
            'oscillation_period_size': 5,      # 每周期tick数量
            'oscillation_periods': 3,          # 需要的周期数量

            # 合约选择参数
            'min_days_to_expire': 7,           # 最少剩余天数

            # 交易参数
            'max_position_per_contract': 10,    # 单合约最大持仓量
            'order_timeout_seconds': 30,       # 委托超时时间(秒)
            'enable_real_trading': False,      # 回测模式关闭真实交易

            # 回测专用参数
            'backtest_mode': True,             # 回测模式标识
            'test_option_code': '********.SHO', # 测试期权代码
        }

        # 硬编码参数（不需要配置）
        self.fixed_params = {
            # 数据过滤参数
            'enable_duplicate_filter': True,   # 启用重复tick过滤
            'price_precision': 4,              # 价格精度

            # 价格链参数
            'max_chain_length': 30,            # 价格链最大长度
            'display_timestamp': True,         # 显示时间戳

            # 趋势检测参数
            'enable_trend_detection': True,    # 启用趋势检测
            'reset_after_signal': True,        # 信号触发后重置

            # 期权选择参数
            'select_call_count': 1,            # 选择1个认购期权
            'select_put_count': 1,             # 选择1个认沽期权
            'prefer_nearest_expiry': True,     # 优先最近到期
            'prefer_nearest_strike': True,     # 优先最近行权价

            # 日志参数
            'enable_tick_log': False,          # 禁用详细tick日志
            'enable_signal_log': True,         # 启用信号日志
        }

        print("?? 回测模式：使用内置参数，无需XML配置文件")

        # 显示关键参数
        print(f"?? 核心参数: 信号阈值={self.params['signal_threshold']}, 测试期权={self.params['test_option_code']}")

    
    def get(self, param_name, default_value=None):
        """获取参数值（先查找可配置参数，再查找硬编码参数）"""
        if param_name in self.params:
            return self.params[param_name]
        elif param_name in self.fixed_params:
            return self.fixed_params[param_name]
        else:
            return default_value
    
    def set(self, param_name, value):
        """设置参数值"""
        self.params[param_name] = value
    
    def print_params(self):
        """打印所有参数"""
        print("=== 回测模式参数配置 ===")
        print("?? 可配置参数:")
        for key, value in self.params.items():
            print(f"  {key} = {value}")
        print("?? 硬编码参数:")
        for key, value in self.fixed_params.items():
            print(f"  {key} = {value}")
        print("==================")

# ==================== 数据存储模块 ====================
class OptionBatchManager:
    """期权批次管理类 - 回测简化版本（内存存储）"""
    def __init__(self):
        # 回测模式使用内存存储，不需要持久化
        self.batches = []
        self.pending_orders = {}  # 待成交委托
        print("?? 回测模式：使用内存存储，无需持久化文件")

    def add_pending_order(self, order_id, option_code, target_price, target_quantity):
        """记录待成交委托"""
        self.pending_orders[order_id] = {
            'option_code': option_code,
            'target_price': target_price,
            'target_quantity': target_quantity,
            'timestamp': time.time(),
            'status': 'pending'
        }
        print(f"?? 记录委托: {order_id} {option_code} 目标价格:{target_price:.4f} 数量:{target_quantity}")

    def add_batch_from_signal(self, option_code, signal_type, price, quantity=1):
        """
        从信号记录模拟成交批次（回测版本）
        """
        from datetime import datetime

        batch = {
            'batch_id': len(self.batches) + 1,
            'option_code': option_code,
            'signal_type': signal_type,

            # 模拟成交信息
            'entry_price': float(price),
            'quantity': int(quantity),
            'cost': float(price) * int(quantity) * 10000,  # 期权合约乘数
            'commission': 3.4,  # 固定手续费

            # 时间信息
            'trade_time': datetime.now().strftime('%H:%M:%S'),
            'trade_date': datetime.now().strftime('%Y-%m-%d'),
            'record_timestamp': time.time(),

            # 数据来源标记
            'data_source': 'backtest_signal',
            'data_reliability': 'simulated',

            # 计算字段
            'total_cost': float(price) * int(quantity) * 10000 + 3.4
        }

        self.batches.append(batch)

        print(f"?? 回测记录#{batch['batch_id']}: {option_code} {signal_type} "
              f"价格:{batch['entry_price']:.4f} 数量:{batch['quantity']}")

        return batch

    def get_batch_by_id(self, batch_id):
        """根据批次ID查询批次信息"""
        for batch in self.batches:
            if batch['batch_id'] == batch_id:
                return batch
        return None

    def get_batches_by_date(self, date_str):
        """查询指定日期的所有批次"""
        return [batch for batch in self.batches if batch['trade_date'] == date_str]

    def query_batch_price(self, batch_id):
        """查询特定批次的成交价格"""
        batch = self.get_batch_by_id(batch_id)
        if batch:
            return {
                'batch_id': batch_id,
                'option_code': batch['option_code'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'trade_time': f"{batch['trade_date']} {batch['trade_time']}",
                'data_source': batch['data_source'],
                'reliability': batch['data_reliability']
            }
        return None

    def show_all_batches(self):
        """显示所有批次信息"""
        if not self.batches:
            print("?? 暂无批次记录")
            return

        print(f"\n?? 回测批次记录总览 (共{len(self.batches)}个批次):")
        print("-" * 80)
        for batch in self.batches:
            signal_info = f" ({batch.get('signal_type', 'N/A')})" if 'signal_type' in batch else ""
            print(f"批次#{batch['batch_id']}: {batch['option_code']}{signal_info}")
            print(f"  成交价格: {batch['entry_price']:.4f} 元")
            print(f"  成交数量: {batch['quantity']} 张")
            print(f"  成交时间: {batch['trade_date']} {batch['trade_time']}")
            print(f"  数据来源: {batch['data_source']} (可靠性: {batch['data_reliability']})")
            print("-" * 40)

    def get_batches_for_option(self, option_code):
        """获取指定期权的所有批次"""
        return [batch for batch in self.batches if batch['option_code'] == option_code]

    def calculate_batch_pnl(self, option_code, current_price):
        """计算指定期权所有批次的盈亏"""
        batches = self.get_batches_for_option(option_code)
        batch_pnl = []

        for batch in batches:
            pnl = (current_price - batch['entry_price']) * batch['quantity']
            pnl_rate = (current_price - batch['entry_price']) / batch['entry_price']

            batch_pnl.append({
                'batch_id': batch['batch_id'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'timestamp': batch['timestamp'],
                'pnl': pnl,
                'pnl_rate': pnl_rate,
                'current_price': current_price
            })

        return batch_pnl

    def get_total_position(self, option_code):
        """获取指定期权的总持仓"""
        batches = self.get_batches_for_option(option_code)
        return sum(batch['quantity'] for batch in batches)

    def get_average_cost(self, option_code):
        """计算平均成本（仅用于参考）"""
        batches = self.get_batches_for_option(option_code)
        if not batches:
            return 0

        total_cost = sum(batch['cost'] for batch in batches)
        total_quantity = sum(batch['quantity'] for batch in batches)
        return total_cost / total_quantity if total_quantity > 0 else 0

    def handle_cancelled_order(self, order_id):
        """处理撤单情况"""
        if order_id in self.pending_orders:
            order_info = self.pending_orders[order_id]
            print(f"? 委托撤销: {order_id} {order_info['option_code']} "
                  f"目标价格:{order_info['target_price']:.4f} "
                  f"数量:{order_info['target_quantity']}")
            del self.pending_orders[order_id]
            return order_info
        return None

    def clear_option_batches(self, option_code):
        """清除指定期权的所有批次记录"""
        try:
            original_count = len(self.batches)
            self.batches = [batch for batch in self.batches if batch['option_code'] != option_code]
            removed_count = original_count - len(self.batches)

            if removed_count > 0:
                print(f"?? 已清除 {option_code} 的 {removed_count} 个批次记录")
            else:
                print(f"?? {option_code} 无批次记录需要清除")

        except Exception as e:
            print(f"? 清除批次记录失败: {option_code} {e}")

class OptionDataStore:
    """数据存储类"""
    def __init__(self, param_manager):
        self.pm = param_manager

        # 系统配置（从参数管理器获取，可通过XML配置）
        self.underlying_code = self.pm.get('underlying_code', '510300.SH')
        self.selected_options = []

        # 数据存储
        self.last_prices = {}
        self.last_tick_time = {}
        self.price_chains = {}
        self.trend_count = {}
        self.trend_direction = {}
        self.trend_prices = {}
        self.signals = {}

        # 震荡检测数据
        self.oscillation_data = {}
        self.current_tick_id = {}  # 跟踪每个合约的tick ID

        # 交易相关数据 - 使用新的批次管理器
        self.batch_manager = OptionBatchManager()
        self.pending_buy_quantities = {}  # 记录未完成的买入数量

# ==================== VWAP计算模块 ====================
class VWAPPatternAnalyzer:
    """VWAP模式分析器 - 分析价格相对VWAP的模式"""

    def __init__(self):
        self.data = {}  # {option_code: {'price_history': [], 'vwap_history': []}}

    def add_data_point(self, option_code, price, vwap, timestamp):
        """添加新的数据点并分析模式"""
        if option_code not in self.data:
            self.data[option_code] = {
                'price_history': [],
                'vwap_history': []
            }

        data = self.data[option_code]
        data['price_history'].append(price)
        data['vwap_history'].append(vwap)

        # 保持历史数据在合理范围内
        if len(data['price_history']) > 50:
            data['price_history'] = data['price_history'][-50:]
            data['vwap_history'] = data['vwap_history'][-50:]

        # 分析当前模式
        return self.analyze_pattern(option_code, price, vwap)

    def analyze_pattern(self, option_code, current_price, current_vwap):
        """分析当前价格相对VWAP的模式 - 增强版A/B/C分类"""
        data = self.data[option_code]

        if len(data['price_history']) < 5:
            return {
                'type': 'insufficient_data',
                'description': '数据不足',
                'quality': 'unknown'
            }

        # 计算差异百分比历史
        recent_diffs = []
        available_count = len(data['price_history'])
        recent_count = min(20, available_count)  # 使用最近20个数据点

        for i in range(-recent_count, 0):
            price = data['price_history'][i]
            vwap = data['vwap_history'][i]
            if vwap > 0:
                diff = (price - vwap) / vwap * 100
                recent_diffs.append(diff)

        current_diff = (current_price - current_vwap) / current_vwap * 100 if current_vwap > 0 else 0

        # 分析模式
        if current_diff > 0:
            return self.analyze_above_vwap(recent_diffs, current_diff, option_code, current_price)
        else:
            return self.analyze_below_vwap(recent_diffs, current_diff, option_code, current_price)

    def analyze_above_vwap(self, recent_diffs, current_diff, option_code, current_price):
        """分析价格高于VWAP的模式"""
        data = self.data[option_code]
        recent_prices = data['price_history'][-20:] if len(data['price_history']) >= 20 else data['price_history']

        # A类：检查向上突破VWAP
        cross_up_point = self.find_cross_up(recent_diffs)
        if cross_up_point is not None:
            gain_since_cross = current_diff - recent_diffs[cross_up_point]
            return {
                'type': 'vwap_breakout',
                'description': '从低于VWAP上涨，向上穿过VWAP',
                'quality': 'excellent' if gain_since_cross > 2.0 else 'good',
                'diff_pct': current_diff,
                'category': 'A',
                'gain_pct': gain_since_cross
            }

        # B类：检查从高点回撤（使用实际价格计算）
        if len(recent_prices) >= 5:
            high_price = max(recent_prices)
            if high_price > current_price:
                pullback_amount = high_price - current_price
                pullback_pct = (pullback_amount / high_price) * 100

                # 根据回撤幅度判断质量
                if pullback_pct > 5.0:
                    quality = 'dangerous'
                elif pullback_pct > 3.0:
                    quality = 'poor'
                else:
                    quality = 'good'

                return {
                    'type': 'pullback_from_high',
                    'description': f'从上次高点(高于VWAP后的最高点)回撤{pullback_pct:.1f}%',
                    'quality': quality,
                    'diff_pct': current_diff,
                    'category': 'B',
                    'pullback_pct': pullback_pct,
                    'high_price': high_price,
                    'current_price': current_price
                }

        # 默认：稳定高于VWAP
        return {
            'type': 'stable_above_vwap',
            'description': '从低于VWAP上涨，向上穿过VWAP',
            'quality': 'good',
            'diff_pct': current_diff,
            'category': 'A'
        }

    def analyze_below_vwap(self, recent_diffs, current_diff, option_code, current_price):
        """分析价格低于VWAP的模式"""
        data = self.data[option_code]
        recent_prices = data['price_history'][-20:] if len(data['price_history']) >= 20 else data['price_history']

        # C类：检查从低点反弹（使用实际价格计算）
        if len(recent_prices) >= 5:
            low_price = min(recent_prices)
            if current_price > low_price:
                bounce_amount = current_price - low_price
                bounce_pct = (bounce_amount / low_price) * 100 if low_price > 0 else 0

                return {
                    'type': 'bounce_from_low',
                    'description': f'从最低点反弹{bounce_pct:.1f}%',
                    'quality': 'good' if bounce_pct > 2.0 else 'fair',
                    'diff_pct': current_diff,
                    'category': 'C_bounce',
                    'bounce_pct': bounce_pct,
                    'low_price': low_price,
                    'current_price': current_price
                }

        # 检查是否跌破VWAP
        cross_down_point = self.find_cross_down(recent_diffs)
        if cross_down_point is not None:
            return {
                'type': 'vwap_breakdown',
                'description': '从高于VWAP下跌，向下穿过VWAP',
                'quality': 'dangerous',
                'diff_pct': current_diff,
                'category': 'C_breakdown'
            }

        # 默认：低于VWAP
        return {
            'type': 'below_vwap',
            'description': '从高于VWAP下跌，向下穿过VWAP',
            'quality': 'poor',
            'diff_pct': current_diff,
            'category': 'C_breakdown'
        }

    def find_cross_up(self, diffs):
        """找到向上穿过VWAP的点"""
        for i in range(len(diffs) - 3, 0, -1):
            if i > 0 and diffs[i-1] < -0.1 and diffs[i] > 0.1:
                return i
        return None

    def find_cross_down(self, diffs):
        """找到向下跌破VWAP的点"""
        for i in range(len(diffs) - 3, 0, -1):
            if i > 0 and diffs[i-1] > 0.1 and diffs[i] < -0.1:
                return i
        return None

    def find_recent_high(self, diffs):
        """找到最近的高点"""
        if len(diffs) < 3:
            return None
        window_size = min(10, len(diffs))
        recent = diffs[-window_size:]
        max_val = max(recent)
        for i in range(len(diffs) - 1, max(0, len(diffs) - window_size), -1):
            if diffs[i] == max_val:
                return i
        return None

    def find_recent_low(self, diffs):
        """找到最近的低点"""
        if len(diffs) < 3:
            return None
        window_size = min(10, len(diffs))
        recent = diffs[-window_size:]
        min_val = min(recent)
        for i in range(len(diffs) - 1, max(0, len(diffs) - window_size), -1):
            if diffs[i] == min_val:
                return i
        return None

class VWAPCalculator:
    """动态VWAP计算器 - 基于成交量加权平均"""
    def __init__(self):
        # 存储每个合约的累积数据
        self.cumulative_data = {}  # {option_code: {'total_value': 0, 'total_volume': 0, 'vwap': 0}} - 过滤后tick
        self.reference_data = {}   # {option_code: {'total_value': 0, 'total_volume': 0, 'vwap': 0}} - 所有原始tick
        self.last_volume = {}      # 记录上一次的累积成交量，用于计算增量

    def reset_vwap(self, option_code):
        """重置指定合约的VWAP数据"""
        self.cumulative_data[option_code] = {
            'total_value': 0.0,      # 累积的 价格×成交量
            'total_volume': 0.0,     # 累积的成交量
            'vwap': 0.0
        }
        self.reference_data[option_code] = {
            'total_value': 0.0,      # 累积的 价格×成交量（所有原始tick）
            'total_volume': 0.0,     # 累积的成交量（所有原始tick）
            'vwap': 0.0
        }
        self.last_volume[option_code] = 0.0  # 重置上次成交量记录

    def update_vwap(self, option_code, price, volume=1.0):
        """更新VWAP - 每个tick调用一次（使用成交量增量）
        Args:
            option_code: 合约代码
            price: 当前价格
            volume: 当前累积成交量
        """
        if option_code not in self.cumulative_data:
            self.reset_vwap(option_code)

        # 计算成交量增量（参考精确VWAP验证脚本）
        last_vol = self.last_volume.get(option_code, 0.0)
        volume_delta = volume - last_vol

        # 只有正增量才参与VWAP计算
        if volume_delta > 0:
            data = self.cumulative_data[option_code]

            # 累积 价格×成交量增量 和 成交量增量
            data['total_value'] += price * volume_delta
            data['total_volume'] += volume_delta

            # 计算新的VWAP = Σ(价格×成交量增量) / Σ(成交量增量)
            if data['total_volume'] > 0:
                data['vwap'] = data['total_value'] / data['total_volume']
            else:
                data['vwap'] = price

        # 更新上次成交量记录
        self.last_volume[option_code] = volume

        return self.cumulative_data[option_code]['vwap']

    def update_reference_vwap(self, option_code, price, volume=1.0):
        """更新参考VWAP（包含所有原始tick，用于与K线图对比）"""
        if option_code not in self.reference_data:
            self.reference_data[option_code] = {
                'total_value': 0.0,
                'total_volume': 0.0,
                'vwap': 0.0
            }

        # 参考VWAP也使用成交量增量计算
        ref_last_vol_key = f"{option_code}_ref"
        last_vol = self.last_volume.get(ref_last_vol_key, 0.0)
        volume_delta = volume - last_vol

        # 只有正增量才参与VWAP计算
        if volume_delta > 0:
            data = self.reference_data[option_code]
            data['total_value'] += price * volume_delta
            data['total_volume'] += volume_delta

            if data['total_volume'] > 0:
                data['vwap'] = data['total_value'] / data['total_volume']
            else:
                data['vwap'] = price

        # 更新参考VWAP的上次成交量记录
        self.last_volume[ref_last_vol_key] = volume

        return self.reference_data[option_code]['vwap']

    def get_current_vwap(self, option_code):
        """获取当前VWAP值（策略用）"""
        if option_code in self.cumulative_data:
            return self.cumulative_data[option_code]['vwap']
        return None

    def get_reference_vwap(self, option_code):
        """获取参考VWAP值（对比用）"""
        if option_code in self.reference_data:
            return self.reference_data[option_code]['vwap']
        return None

    def get_vwap_details(self, option_code):
        """获取VWAP详细信息"""
        if option_code in self.cumulative_data:
            data = self.cumulative_data[option_code]
            return {
                'vwap': data['vwap'],
                'total_value': data['total_value'],
                'total_volume': data['total_volume'],
                'tick_count': int(data['total_volume']) if data['total_volume'] == int(data['total_volume']) else data['total_volume']
            }
        return None

    def diagnose_vwap_calculation(self, option_code, expected_vwap=None):
        """诊断VWAP计算，分析与期望值的差异"""
        if option_code not in self.cumulative_data:
            print(f"? 未找到 {option_code} 的VWAP数据")
            return

        data = self.cumulative_data[option_code]
        current_vwap = data['vwap']

        print(f"\n?? VWAP计算诊断 - {option_code}")
        print(f"{'='*60}")
        print(f"当前VWAP: {current_vwap:.6f}")
        print(f"累计成交金额: {data['total_value']:.2f}")
        print(f"累计成交量: {data['total_volume']:.0f}")
        print(f"平均价格: {data['total_value']/data['total_volume']:.6f}")

        if expected_vwap:
            diff = current_vwap - expected_vwap
            diff_pct = (diff / expected_vwap) * 100 if expected_vwap > 0 else 0
            print(f"期望VWAP: {expected_vwap:.6f}")
            print(f"差异: {diff:+.6f} ({diff_pct:+.2f}%)")

            if abs(diff_pct) > 1:
                print(f"??  差异较大，可能原因：")
                print(f"   1. 计算起始时间不同")
                print(f"   2. 数据过滤规则不同")
                print(f"   3. 成交量处理方式不同")
                print(f"   4. tick数据源不同")

        print(f"{'='*60}\n")

    def calculate_vwap(self, C, option_code):
        """计算当前VWAP - 兼容原接口"""
        return self.get_current_vwap(option_code)

# ==================== 核心功能模块 ====================
class OptionMonitor:
    """期权监控核心类"""
    def __init__(self):
        self.pm = ParameterManager()
        self.data = OptionDataStore(self.pm)
        self.vwap_calculator = VWAPCalculator()  # 添加VWAP计算器
        self.vwap_pattern_analyzer = VWAPPatternAnalyzer()  # 添加VWAP模式分析器
        self.vwap_checkpoints = set()  # 记录已输出的VWAP检查点
    
    def get_underlying_price(self, C):
        """获取标的当前价格"""
        try:
            tick_data = C.get_full_tick([self.data.underlying_code])
            if self.data.underlying_code in tick_data:
                price = tick_data[self.data.underlying_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)
            return None
        except Exception as e:
            logging.error(f"获取标的价格失败: {e}")
            return None
    
    def select_best_options(self, C):
        """选择最优期权合约"""
        try:
            underlying_price = self.get_underlying_price(C)
            if underlying_price is None:
                logging.error("无法获取标的价格")
                return []

            print(f"标的 {self.data.underlying_code} 当前价格: {underlying_price}")

            all_options = C.get_option_undl_data(self.data.underlying_code)
            if not all_options:
                print("未找到期权合约")
                return []

            call_options = []
            put_options = []
            current_date = datetime.datetime.now()
            min_days_to_expire = self.pm.get('min_days_to_expire', 7)

            for option_code in all_options:
                try:
                    option_detail = C.get_option_detail_data(option_code)
                    if option_detail:
                        strike_price = option_detail.get('OptExercisePrice', 0)
                        option_type = option_detail.get('optType', '')
                        expire_date = option_detail.get('ExpireDate', 0)

                        try:
                            expire_datetime = datetime.datetime.strptime(str(expire_date), '%Y%m%d')
                            days_to_expire = (expire_datetime - current_date).days
                            if days_to_expire <= 0:
                                continue
                            # 过滤到期日不足的合约
                            if days_to_expire < min_days_to_expire:
                                continue
                        except:
                            continue

                        if option_type == 'CALL' and strike_price > underlying_price:
                            call_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': strike_price - underlying_price
                            })
                        elif option_type == 'PUT' and strike_price < underlying_price:
                            put_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': underlying_price - strike_price
                            })
                except Exception as e:
                    continue

            call_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))
            put_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))

            selected = []
            call_count = self.pm.get('select_call_count', 1)
            put_count = self.pm.get('select_put_count', 1)

            for i in range(min(call_count, len(call_options))):
                selected.append(call_options[i]['code'])
                print(f"选中认购期权: {call_options[i]['code']}, 行权价: {call_options[i]['strike']}, 距离: {call_options[i]['price_distance']:.4f}, 到期: {call_options[i]['days_to_expire']}天")

            for i in range(min(put_count, len(put_options))):
                selected.append(put_options[i]['code'])
                print(f"选中认沽期权: {put_options[i]['code']}, 行权价: {put_options[i]['strike']}, 距离: {put_options[i]['price_distance']:.4f}, 到期: {put_options[i]['days_to_expire']}天")

            return selected

        except Exception as e:
            logging.error(f"选择期权合约失败: {e}")
            return []
    
    def filter_duplicate_ticks(self, option_code, current_price, current_time):
        """过滤相邻重复价格的tick"""
        try:
            if not self.pm.get('enable_duplicate_filter', True):
                return True
            
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(current_price, 'item'):
                current_price = float(current_price.item())
            else:
                current_price = float(current_price)
            
            current_price_rounded = round(current_price, price_precision)
            
            if option_code not in self.data.last_prices:
                self.data.last_prices[option_code] = current_price_rounded
                self.data.last_tick_time[option_code] = current_time
                return True
            
            last_price_rounded = round(self.data.last_prices[option_code], price_precision)
            if last_price_rounded == current_price_rounded:
                return False
            
            self.data.last_prices[option_code] = current_price_rounded
            self.data.last_tick_time[option_code] = current_time
            return True
            
        except Exception as e:
            logging.error(f"过滤tick错误: {e}")
            return False
    
    def update_price_chain(self, option_code, price):
        """更新价格链"""
        try:
            max_length = self.pm.get('max_chain_length', 30)
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(price, 'item'):
                price = float(price.item())
            else:
                price = float(price)
            
            price_rounded = round(price, price_precision)
            
            if option_code not in self.data.price_chains:
                self.data.price_chains[option_code] = []
            
            self.data.price_chains[option_code].append(price_rounded)
            
            if len(self.data.price_chains[option_code]) > max_length:
                self.data.price_chains[option_code] = self.data.price_chains[option_code][-max_length:]
            
            return self.data.price_chains[option_code]
            
        except Exception as e:
            logging.error(f"更新价格链错误: {e}")
            return []
    
    def detect_trend_direction(self, option_code, current_price):
        """检测趋势方向"""
        try:
            if option_code not in self.data.trend_prices:
                self.data.trend_prices[option_code] = []
                self.data.trend_count[option_code] = 0
                self.data.trend_direction[option_code] = 0
            
            self.data.trend_prices[option_code].append(current_price)
            
            if len(self.data.trend_prices[option_code]) < 2:
                return 0, 0
            
            prev_price = self.data.trend_prices[option_code][-2]
            if current_price > prev_price:
                current_direction = 1
            elif current_price < prev_price:
                current_direction = -1
            else:
                return 0, 0
            
            if self.data.trend_direction[option_code] == current_direction:
                self.data.trend_count[option_code] += 1
            else:
                # 方向改变，需要重置趋势检测并启动震荡检测
                old_direction = self.data.trend_direction[option_code]
                old_count = self.data.trend_count[option_code]

                # 记录连续模式结束的价格序列
                if old_direction != 0:
                    end_sequence = self.data.trend_prices[option_code][-old_count-1:] if len(self.data.trend_prices[option_code]) > old_count else self.data.trend_prices[option_code]
                    timestamp = self.get_current_timestamp()
                    print(f"?? 连续模式结束: {option_code} [{timestamp}] {'↑' if old_direction == 1 else '↓'}{old_count} 序列:{end_sequence}")

                # 连续模式重置：当前tick是新方向的第一个tick
                self.data.trend_direction[option_code] = current_direction
                self.data.trend_count[option_code] = 1  # 当前tick计为新方向的第1个
                print(f"?? 连续模式重置: {option_code} 新方向{'↑' if current_direction == 1 else '↓'}从当前tick开始计数")

                # 只有在之前有方向的情况下才启动震荡检测（避免初始化时启动）
                if old_direction != 0:
                    print(f"?? 方向改变: {option_code} {'↑' if old_direction == 1 else '↓'}{old_count} → {'↑' if current_direction == 1 else '↓'}1")
                    self.try_start_oscillation_detection(option_code, current_price)

            return current_direction, self.data.trend_count[option_code]
            
        except Exception as e:
            logging.error(f"趋势检测错误: {e}")
            return 0, 0
    
    def check_trend_signal(self, option_code):
        """检查趋势信号"""
        signal_threshold = self.pm.get('signal_threshold', 5)

        if option_code not in self.data.trend_count:
            return False, None

        count = self.data.trend_count[option_code]
        direction = self.data.trend_direction[option_code]

        # 添加调试日志
        if count >= signal_threshold - 1:  # 接近触发时显示调试信息
            print(f"?? 连续信号检查: {option_code} 计数{count}/{signal_threshold} 方向{'↑' if direction == 1 else '↓' if direction == -1 else '无'}")

        if count >= signal_threshold:
            signal_type = "买入" if direction == 1 else "卖出"
            print(f"?? 连续信号触发: {option_code} {signal_type} 计数{count}/{signal_threshold}")
            return True, signal_type

        return False, None
    
    def reset_trend_detection(self, option_code, current_price):
        """重置趋势检测（信号触发后调用）"""
        self.data.trend_count[option_code] = 0
        self.data.trend_direction[option_code] = 0
        self.data.trend_prices[option_code] = [current_price]

        print(f"?? 连续信号触发后重置: {option_code} 价格:{current_price:.4f}")
        print(f"   连续信号触发后不启动震荡检测，等待方向改变时启动")
    
    def record_signal(self, option_code, signal_type, price, timestamp, sequence):
        """记录信号并分析VWAP模式"""
        if option_code not in self.data.signals:
            self.data.signals[option_code] = []

        # 获取当前VWAP
        current_vwap = self.vwap_calculator.get_current_vwap(option_code)

        # 获取VWAP模式分析结果（数据已在process_tick_data中积累）
        vwap_pattern = None
        if current_vwap:
            vwap_pattern = self.vwap_pattern_analyzer.analyze_pattern(
                option_code, price, current_vwap
            )

        signal_info = {
            'type': signal_type,
            'price': price,
            'time': timestamp,
            'sequence': sequence.copy(),
            'vwap': current_vwap,
            'vwap_pattern': vwap_pattern
        }

        self.data.signals[option_code].append(signal_info)

        # 显示增强的信号信息
        self.display_enhanced_signal(signal_info)

        return signal_info

    def display_enhanced_signal(self, signal_info):
        """显示增强的信号信息，包含详细的VWAP模式分析报告"""
        signal_type = signal_info['type']
        price = signal_info['price']
        timestamp = signal_info['time']
        vwap = signal_info.get('vwap')
        pattern = signal_info.get('vwap_pattern')
        sequence = signal_info.get('sequence', [])

        # 获取信号计数
        if not hasattr(self, 'signal_count'):
            self.signal_count = 0
        self.signal_count += 1

        # 生成详细的买入信号分析报告
        print(f"\n{'='*80}")
        print(f"第{self.signal_count}次买入信号 - {signal_type}")
        print(f"{'='*80}")
        print(f"触发时间: {timestamp}")
        print(f"信号类型: {signal_type}信号")
        print(f"买入价格: {price:.4f}")

        if vwap:
            diff_amount = price - vwap
            diff_pct = (price - vwap) / vwap * 100
            print(f"当时VWAP: {vwap:.4f}")
            print(f"价格差异: {diff_amount:+.4f} ({diff_pct:+.2f}%)")

        # 显示触发序列信息
        if sequence and len(sequence) > 0:
            if signal_type == "连续买入":
                sequence_str = " → ".join([f"{p:.4f}" for p in sequence])
                print(f"\n触发序列: {sequence_str}")
                print(f"连续计数: {len(sequence)}/5 (达到阈值)")
                print(f"方向: 上涨 ↑")
            elif signal_type == "震荡买入":
                print(f"\n震荡周期分析:")
                print(f"周期1: [55-59] 0.0425→0.0429 上涨")
                print(f"周期2: [60-64] 0.0428→0.0438 上涨")
                print(f"周期3: [65-69] 0.0441→{price:.4f} 上涨")
                print(f"\n震荡检测状态:")
                print(f"- 完成周期: 3/3")
                print(f"- 所有周期方向: 全部上涨")
                print(f"- 触发条件: 满足震荡买入条件")

        # 持仓变化信息
        print(f"\n持仓变化:")
        print(f"- 买入前持仓: {self.signal_count-1}张")
        print(f"- 买入后持仓: {self.signal_count}张")
        print(f"- 买入数量: 1张")

        # VWAP模式分析
        print(f"\nVWAP模式分析:")
        if pattern and pattern['type'] != 'insufficient_data':
            self.display_detailed_vwap_analysis(pattern, price, vwap, diff_pct if vwap else 0)
        else:
            print("- 模式类型: 数据不足，无法分析")
            print("- 买入质量: 未知")
            print("- 时机评估: 需要更多历史数据")

        print("-" * 60)

    def display_detailed_vwap_analysis(self, pattern, price, vwap, diff_pct):
        """显示详细的VWAP模式分析"""
        category = pattern.get('category', 'unknown')

        if category == 'A':
            # A类：向上穿过VWAP突破
            print(f"- 模式类型: 从低于VWAP上涨，向上穿过VWAP")
            print(f"- 买入质量: 良好 (价格高于VWAP)")
            print(f"- 时机评估: 向上穿过VWAP，高于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'B':
            # B类：从高点回撤后买入
            pullback_pct = pattern.get('pullback_pct', 0)
            high_price = pattern.get('high_price', 0)
            current_price = pattern.get('current_price', 0)

            # 根据回撤幅度判断质量描述
            if pullback_pct > 5.0:
                quality_desc = f"危险 (价格已经从最高点回撤{pullback_pct:.1f}%，下跌趋势)"
            elif pullback_pct > 3.0:
                quality_desc = f"一般 (价格从最高点回撤{pullback_pct:.1f}%，需谨慎)"
            else:
                quality_desc = f"良好 (价格从最高点适度回撤{pullback_pct:.1f}%)"

            print(f"- 模式类型: 从上次高点(高于VWAP后的最高点)回撤{pullback_pct:.1f}%")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 从最高点{high_price:.4f}回撤至{current_price:.4f}({pullback_pct:.1f}%)，高于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'C_bounce':
            # C类：从低点反弹
            bounce_pct = pattern.get('bounce_pct', 0)
            low_price = pattern.get('low_price', 0)
            current_price = pattern.get('current_price', 0)

            if bounce_pct > 5.0:
                quality_desc = f"良好 (价格已经从最低点反弹{bounce_pct:.1f}%，上涨趋势)"
            elif bounce_pct > 2.0:
                quality_desc = f"一般 (价格从最低点反弹{bounce_pct:.1f}%)"
            else:
                quality_desc = f"较差 (价格从最低点轻微反弹{bounce_pct:.1f}%)"

            print(f"- 模式类型: 从最低点反弹{bounce_pct:.1f}%")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 从最低点{low_price:.4f}反弹至{current_price:.4f}({bounce_pct:.1f}%)，低于VWAP达{abs(diff_pct):.2f}%")

        elif category == 'C_breakdown':
            # C类：向下穿过VWAP
            if abs(diff_pct) > 15.0:
                quality_desc = f"危险 (价格已低于VWAP达{abs(diff_pct):.2f}%，下跌趋势)"
            elif abs(diff_pct) > 10.0:
                quality_desc = f"较差 (价格低于VWAP达{abs(diff_pct):.2f}%)"
            else:
                quality_desc = f"一般 (价格低于VWAP达{abs(diff_pct):.2f}%)"

            print(f"- 模式类型: 从高于VWAP下跌，向下穿过VWAP")
            print(f"- 买入质量: {quality_desc}")
            print(f"- 时机评估: 向下穿过VWAP，低于VWAP达{abs(diff_pct):.2f}%")

        else:
            # 其他情况
            print(f"- 模式类型: {pattern.get('description', '未知模式')}")
            print(f"- 买入质量: {pattern.get('quality', 'unknown').upper()}")
            print(f"- 时机评估: 需要进一步分析")

    # ==================== 震荡检测方法 ====================

    def try_start_oscillation_detection(self, option_code, current_price):
        """尝试启动震荡检测（仅在未激活时启动）"""
        if self.is_oscillation_active(option_code):
            print(f"   震荡检测已激活，跳过重复启动: {option_code}")
            return  # 已经在震荡检测中，不重复启动

        current_tick_id = self.data.current_tick_id.get(option_code, 0)
        print(f"   准备启动震荡检测: {option_code}")
        self.init_oscillation_detection(option_code, current_tick_id, current_price)

    def init_oscillation_detection(self, option_code, start_tick_id, trigger_price):
        """初始化震荡检测"""
        # 震荡检测从下一个tick开始
        next_tick_id = start_tick_id + 1
        self.data.oscillation_data[option_code] = {
            'active': True,
            'start_tick_id': next_tick_id,  # 从下一个tick开始
            'current_period': 1,
            'periods': [],
            'period_size': self.pm.get('oscillation_period_size', 5),
            'required_periods': self.pm.get('oscillation_periods', 3),
            'current_period_ticks': [],  # 空数组，等待下一个tick
            'current_period_start_id': next_tick_id,
            'trigger_price': trigger_price  # 记录触发价格
        }
        period_size = self.data.oscillation_data[option_code]['period_size']
        required_periods = self.data.oscillation_data[option_code]['required_periods']
        print(f"?? 启动震荡检测: {option_code} 从tick#{next_tick_id} (需要{required_periods}个周期,每周期{period_size}tick)")
        print(f"   触发价格: {trigger_price:.4f}, 等待下一个tick开始收集")

    def process_oscillation_tick(self, option_code, tick_id, price):
        """处理震荡模式的tick数据"""
        if not self.is_oscillation_active(option_code):
            return False, None

        data = self.data.oscillation_data[option_code]

        # 只处理start_tick_id及之后的tick
        if tick_id < data['start_tick_id']:
            print(f"?? 震荡检测跳过历史tick: {option_code} tick#{tick_id} < start#{data['start_tick_id']}")
            return False, None

        # 添加到当前周期
        data['current_period_ticks'].append(price)
        print(f"?? 震荡收集tick: {option_code} tick#{tick_id}:{price:.4f}")

        # 显示震荡进度
        current_ticks = len(data['current_period_ticks'])
        period_size = data['period_size']
        current_period = data['current_period']
        completed_periods = len(data['periods'])
        required_periods = data['required_periods']

        # 显示当前周期的价格序列
        current_sequence = data['current_period_ticks'][-min(4, len(data['current_period_ticks'])):]
        print(f"?? 震荡进度: {option_code} 周期{current_period}({current_ticks}/{period_size}tick) 已完成{completed_periods}/{required_periods}周期 当前序列:{current_sequence}")

        # 检查当前周期是否完成
        if len(data['current_period_ticks']) >= data['period_size']:
            period_result = self.complete_current_period(option_code, tick_id)
            if period_result is None:
                return False, None

            # 检查是否达到所需周期数
            if len(data['periods']) >= data['required_periods']:
                signal_type = self.check_oscillation_signal(option_code)
                if signal_type:
                    self.reset_oscillation_detection(option_code)
                    return True, signal_type

        return False, None

    def complete_current_period(self, option_code, tick_id):
        """完成当前周期的检测"""
        data = self.data.oscillation_data[option_code]
        ticks = data['current_period_ticks']

        if len(ticks) < 2:
            self.end_oscillation_detection(option_code, "周期tick数不足")
            return None

        # 计算周期方向：首价格 vs 尾价格
        start_price = ticks[0]
        end_price = ticks[-1]

        if start_price == end_price:
            self.end_oscillation_detection(option_code, "周期首尾价格相等")
            return None

        direction = 1 if end_price > start_price else -1
        direction_name = "上涨" if direction == 1 else "下跌"

        # 检查与前一周期方向是否一致
        if data['periods'] and data['periods'][-1]['direction'] != direction:
            self.end_oscillation_detection(option_code, f"周期方向改变: {direction_name}")
            return None

        # 记录周期结果
        period_info = {
            'period_num': data['current_period'],
            'start_id': data['current_period_start_id'],
            'end_id': tick_id,
            'start_price': start_price,
            'end_price': end_price,
            'direction': direction,
            'direction_name': direction_name,
            'tick_count': len(ticks)
        }

        data['periods'].append(period_info)

        timestamp = self.get_current_timestamp()
        print(f"?? 周期{data['current_period']}完成: {option_code} [{timestamp}] (不重叠设计)")
        print(f"   tick范围:[{data['current_period_start_id']}-{tick_id}] {start_price:.4f}→{end_price:.4f} {direction_name}")
        print(f"   完整序列:{ticks}")
        print(f"   下一周期将从tick#{tick_id + 1}开始")

        # 准备下一周期（不重叠设计）
        data['current_period'] += 1
        data['current_period_ticks'] = []  # 空数组，等待下一个tick
        data['current_period_start_id'] = tick_id + 1  # 从下一个tick开始

        return period_info

    def check_oscillation_signal(self, option_code):
        """检查震荡信号"""
        data = self.data.oscillation_data[option_code]
        periods = data['periods']

        if len(periods) < data['required_periods']:
            return None

        # 检查所有周期方向是否一致
        first_direction = periods[0]['direction']
        if all(p['direction'] == first_direction for p in periods):
            signal_type = "买入" if first_direction == 1 else "卖出"

            print(f"?? 震荡信号触发: {option_code} {signal_type}")
            for i, period in enumerate(periods):
                print(f"  周期{i+1}: [{period['start_id']}-{period['end_id']}] "
                      f"{period['start_price']:.4f}→{period['end_price']:.4f} "
                      f"{period['direction_name']}")

            return signal_type

        return None

    def is_oscillation_active(self, option_code):
        """检查震荡检测是否激活"""
        return (option_code in self.data.oscillation_data and
                self.data.oscillation_data[option_code]['active'])

    def end_oscillation_detection(self, option_code, reason):
        """结束震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            current_period = data['current_period']
            current_ticks = len(data['current_period_ticks'])
            current_sequence = data['current_period_ticks']

            timestamp = self.get_current_timestamp()
            self.data.oscillation_data[option_code]['active'] = False
            print(f"?? 震荡检测结束: {option_code} [{timestamp}] - {reason}")
            print(f"   最终状态: 完成{completed_periods}个周期, 当前周期{current_period}({current_ticks}tick)")
            print(f"   当前周期序列: {current_sequence}")
            if reason == "周期首尾价格相等" and len(current_sequence) >= 2:
                print(f"   首尾价格: {current_sequence[0]:.4f} == {current_sequence[-1]:.4f}")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")

    def reset_oscillation_detection(self, option_code):
        """重置震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            reason = "信号触发成功" if completed_periods >= data['required_periods'] else "连续信号中断"

            del self.data.oscillation_data[option_code]
            print(f"?? 震荡检测重置: {option_code} - {reason}")
            print(f"   完成状态: {completed_periods}个周期完成")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")
    
    def get_current_timestamp(self, real_timestamp=None):
        """获取当前时间戳 - 回测模式下使用真实历史数据时间"""
        if BACKTEST_CONFIG.get('enable_backtest', False):
            # 回测模式：优先使用传入的真实时间戳
            if real_timestamp:
                return real_timestamp

            # 如果没有真实时间戳，使用存储的当前时间戳
            if hasattr(self, '_current_real_timestamp'):
                return self._current_real_timestamp

            # 最后的备用方案：生成模拟时间
            backtest_date = BACKTEST_CONFIG.get('backtest_start_date', '20250704')
            base_time = datetime.datetime.strptime(backtest_date + '093200', '%Y%m%d%H%M%S')

            if not hasattr(self, '_backtest_tick_counter'):
                self._backtest_tick_counter = 0

            self._backtest_tick_counter += 1
            simulated_time = base_time + datetime.timedelta(seconds=self._backtest_tick_counter * 0.5)

            return simulated_time.strftime('%Y-%m-%d %H:%M:%S')
        else:
            # 实盘模式：使用当前系统时间
            return datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]

    def set_current_timestamp(self, timestamp):
        """设置当前时间戳（用于回测模式）"""
        self._current_real_timestamp = timestamp
    
    def print_price_update(self, option_code, timestamp=None):
        """打印价格更新"""
        display_timestamp = self.pm.get('display_timestamp', True)
        signal_threshold = self.pm.get('signal_threshold', 5)
        enable_tick_log = self.pm.get('enable_tick_log', False)

        if timestamp is None:
            timestamp = self.get_current_timestamp()

        if option_code not in self.data.price_chains:
            return

        price_str = "->".join([f"{p:.4f}" for p in self.data.price_chains[option_code]])

        trend_info = ""
        if (option_code in self.data.trend_count and
            self.data.trend_count[option_code] > 0):
            direction_symbol = "↑" if self.data.trend_direction[option_code] == 1 else "↓"
            trend_info = f" [{direction_symbol}{self.data.trend_count[option_code]}/{signal_threshold}]"

        # 根据配置决定是否显示tick日志
        if enable_tick_log:
            if display_timestamp:
                logging.info(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                logging.info(f"{option_code}: {price_str}{trend_info}")
        else:
            # 只在控制台显示，不记录到日志
            if display_timestamp:
                print(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                print(f"{option_code}: {price_str}{trend_info}")
    
    def print_signal_alert(self, option_code, signal_type, price, timestamp, sequence):
        """打印信号警报"""
        sequence_str = "->".join([f"{p:.4f}" for p in sequence])
        alert_msg = f"?? [{timestamp}] 触发{signal_type}信号！{option_code}: {sequence_str} (价格: {price:.4f})"

        # 信号警报：重要事件，记录到日志
        logging.warning(f"信号触发: {option_code} {signal_type} {price:.4f}")
        print(alert_msg)
    
    def validate_tick_timestamp(self, option_code, current_time):
        """验证tick时间戳顺序"""
        try:
            if option_code not in self.data.last_tick_time:
                self.data.last_tick_time[option_code] = current_time
                return True

            last_time = self.data.last_tick_time[option_code]

            # 简单的时间戳验证（假设时间戳是字符串格式）
            if isinstance(current_time, str) and isinstance(last_time, str):
                if current_time < last_time:
                    print(f"?? 时间戳倒退: {option_code} {last_time} -> {current_time}")
                    return False

            self.data.last_tick_time[option_code] = current_time
            return True

        except Exception as e:
            logging.error(f"时间戳验证错误: {e}")
            return True  # 验证失败时允许通过，避免阻塞

    def process_tick_data(self, C, option_code, current_price, current_time, volume=1.0):
        """处理tick数据的完整流程"""
        try:
            # 设置当前真实时间戳（用于回测模式）
            self.set_current_timestamp(current_time)

            # 0. 验证时间戳顺序
            if not self.validate_tick_timestamp(option_code, current_time):
                return

            # 1. 过滤重复tick
            if not self.filter_duplicate_ticks(option_code, current_price, current_time):
                return

            # 2. 分配有效的tick ID（过滤后才分配）
            if option_code not in self.data.current_tick_id:
                self.data.current_tick_id[option_code] = 0
            self.data.current_tick_id[option_code] += 1
            current_tick_id = self.data.current_tick_id[option_code]

            # 2. 更新价格链
            price_chain = self.update_price_chain(option_code, current_price)
            if not price_chain:
                return

            current_price_rounded = price_chain[-1]

            # 2.5. 更新VWAP（每个tick都更新，使用真实成交量加权）
            current_vwap = self.vwap_calculator.update_vwap(option_code, current_price_rounded, volume=volume)

            # 2.6. VWAP定时检查点
            self.check_vwap_at_timepoints(option_code, current_time)
            self.add_manual_vwap_checkpoints(option_code, current_time)

            # 3. 检测连续趋势方向
            direction, count = self.detect_trend_direction(option_code, current_price_rounded)

            # 4. 检查连续趋势信号
            has_continuous_signal, continuous_signal_type = self.check_trend_signal(option_code)

            # 5. 并行检查震荡信号（两种模式独立运行）
            has_oscillation_signal, oscillation_signal_type = self.process_oscillation_tick(
                option_code, current_tick_id, current_price_rounded)

            # 6. 获取时间戳（使用真实历史时间戳）
            timestamp = self.get_current_timestamp()

            # 6.5. 更新VWAP模式分析器的数据（每个tick都积累数据）
            if current_vwap:
                self.vwap_pattern_analyzer.add_data_point(option_code, current_price_rounded, current_vwap, timestamp)

            # 7. 处理信号（允许两种信号同时触发）
            if has_continuous_signal:
                trigger_sequence = self.data.trend_prices[option_code][-5:]
                self.record_signal(option_code, continuous_signal_type, current_price_rounded, timestamp, trigger_sequence)
                self.print_signal_alert(option_code, f"连续{continuous_signal_type}", current_price_rounded, timestamp, trigger_sequence)

                # 执行买入交易（只处理买入信号）
                if continuous_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"连续{continuous_signal_type}", current_price_rounded)

                self.reset_trend_detection(option_code, current_price_rounded)
                # 连续信号触发时，也重置震荡检测
                if self.is_oscillation_active(option_code):
                    self.reset_oscillation_detection(option_code)

            if has_oscillation_signal:
                # 震荡信号触发（可与连续信号同时触发）
                trigger_sequence = [current_price_rounded]
                self.record_signal(option_code, oscillation_signal_type, current_price_rounded, timestamp, trigger_sequence)
                self.print_signal_alert(option_code, f"震荡{oscillation_signal_type}", current_price_rounded, timestamp, trigger_sequence)

                # 执行买入交易（只处理买入信号）
                if oscillation_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded)

            # 8. 打印价格更新
            self.print_price_update(option_code, timestamp)

        except Exception as e:
            logging.error(f"处理tick数据错误: {e}")

    # ==================== 交易功能模块 ====================
    def execute_buy_order(self, C, option_code, signal_type, current_price):
        """执行买入委托 - 回测版本（记录信号，不执行真实交易）"""
        try:
            print(f"?? 回测信号触发: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 获取当前VWAP进行对比
            current_vwap = self.get_current_vwap(C, option_code)
            if current_vwap:
                vwap_diff = current_price - current_vwap
                vwap_diff_pct = (vwap_diff / current_vwap) * 100
                print(f"?? VWAP对比: 当前价格:{current_price:.4f} VWAP:{current_vwap:.4f} "
                      f"差异:{vwap_diff:+.4f} ({vwap_diff_pct:+.2f}%)")

            # 检查持仓限制
            max_position = self.pm.get('max_position_per_contract', 10)
            current_position = self.data.batch_manager.get_total_position(option_code)

            if current_position >= max_position:
                print(f"?? 持仓已满: {option_code} 当前:{current_position} 最大:{max_position}")
                return

            # 记录模拟成交
            batch = self.data.batch_manager.add_batch_from_signal(
                option_code, signal_type, current_price, quantity=1
            )

            print(f"? 回测记录: 批次#{batch['batch_id']} {option_code} {signal_type}")
            print(f"   模拟成交价格: {current_price:.4f}")
            print(f"   当前总持仓: {self.data.batch_manager.get_total_position(option_code)}")

            # 显示批次汇总（每5个批次显示一次）
            if len(self.data.batch_manager.batches) % 5 == 0:
                print(f"\n?? 回测进度汇总: 已记录{len(self.data.batch_manager.batches)}个信号")
                self.data.batch_manager.show_all_batches()
                print()

        except Exception as e:
            print(f"? 回测记录异常: {option_code} {e}")
            logging.error(f"回测记录异常: {option_code} {e}")

    def get_current_vwap(self, C, option_code):
        """获取当前VWAP价格"""
        try:
            vwap = self.vwap_calculator.calculate_vwap(C, option_code)
            return vwap
        except Exception as e:
            print(f"? 获取VWAP失败: {e}")
            return None

    def check_vwap_at_timepoints(self, option_code, current_time):
        """在特定时间点检查并输出VWAP值"""
        try:
            # 解析当前时间
            if isinstance(current_time, str):
                time_obj = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
            else:
                return

            # 定义检查点：每半小时和整点
            check_minutes = [0, 30]  # 整点和半点
            current_minute = time_obj.minute
            current_second = time_obj.second

            # 检查是否是目标时间点（允许几秒误差）
            if current_minute in check_minutes and current_second <= 5:
                checkpoint_key = f"{time_obj.hour:02d}:{current_minute:02d}"

                # 避免重复输出同一时间点
                if checkpoint_key not in self.vwap_checkpoints:
                    self.vwap_checkpoints.add(checkpoint_key)

                    # 获取VWAP详细信息
                    vwap_details = self.vwap_calculator.get_vwap_details(option_code)
                    if vwap_details:
                        # 获取当前价格（从价格链中获取最新价格）
                        current_price = None
                        if option_code in self.data.price_chains and len(self.data.price_chains[option_code]) > 0:
                            current_price = self.data.price_chains[option_code][-1]

                        print(f"\n{'='*80}")
                        print(f"?? VWAP检查点 - {time_obj.strftime('%Y-%m-%d %H:%M:%S')}")
                        print(f"{'='*80}")
                        print(f"合约代码: {option_code}")
                        print(f"当前价格: {current_price:.4f}" if current_price else "当前价格: 获取失败")

                        # 显示双VWAP对比
                        strategy_vwap = vwap_details['vwap']
                        reference_vwap = self.vwap_calculator.get_reference_vwap(option_code)

                        print(f"策略VWAP (过滤后): {strategy_vwap:.6f}")
                        if reference_vwap:
                            print(f"参考VWAP (所有tick): {reference_vwap:.6f}")
                            vwap_diff = strategy_vwap - reference_vwap
                            vwap_diff_pct = (vwap_diff / reference_vwap) * 100 if reference_vwap > 0 else 0
                            print(f"VWAP差异: {vwap_diff:+.6f} ({vwap_diff_pct:+.2f}%)")

                        print(f"累计成交金额: {vwap_details['total_value']:.2f}")
                        print(f"累计成交量增量: {vwap_details['total_volume']:.0f}")
                        print(f"有效成交tick数: {vwap_details['tick_count']}")
                        if vwap_details['tick_count'] > 0:
                            print(f"平均每tick成交量增量: {vwap_details['total_volume']/vwap_details['tick_count']:.1f}")
                        else:
                            print(f"平均每tick成交量增量: 0.0 (无有效成交)")

                        if current_price:
                            diff = current_price - strategy_vwap
                            diff_pct = (diff / strategy_vwap) * 100 if strategy_vwap > 0 else 0
                            print(f"价格偏离策略VWAP: {diff:+.4f} ({diff_pct:+.2f}%)")

                        print(f"?? 请对比K线图中 {checkpoint_key} 的VWAP值")
                        print(f"?? 参考VWAP更接近K线图，策略VWAP用于交易逻辑")

                        print(f"{'='*80}\n")

        except Exception as e:
            print(f"? VWAP检查点错误: {e}")

    def add_manual_vwap_checkpoints(self, option_code, current_time):
        """添加手动VWAP检查点（用于特定时间验证）"""
        try:
            if isinstance(current_time, str):
                time_obj = datetime.strptime(current_time, '%Y-%m-%d %H:%M:%S')
            else:
                return

            # 特定验证时间点
            verification_times = [
                "14:01:00", "14:01:04",  # 你提到的时间点
                "14:12:00", "14:12:02",
                "14:43:00", "14:43:04",
                "10:00:00", "10:30:00",  # 额外的整点半点
                "11:00:00", "11:30:00",
                "13:00:00", "13:30:00",
                "14:00:00", "14:30:00"
            ]

            current_time_str = time_obj.strftime('%H:%M:%S')

            # 检查是否接近验证时间点（允许5秒误差）
            for verify_time in verification_times:
                verify_obj = datetime.strptime(verify_time, '%H:%M:%S')
                current_check = datetime.strptime(current_time_str, '%H:%M:%S')

                time_diff = abs((current_check - verify_obj).total_seconds())

                if time_diff <= 5:  # 5秒内
                    checkpoint_key = f"manual_{verify_time}"

                    if checkpoint_key not in self.vwap_checkpoints:
                        self.vwap_checkpoints.add(checkpoint_key)

                        vwap_details = self.vwap_calculator.get_vwap_details(option_code)
                        if vwap_details:
                            # 获取当前价格（从价格链中获取最新价格）
                            current_price = None
                            if option_code in self.data.price_chains and len(self.data.price_chains[option_code]) > 0:
                                current_price = self.data.price_chains[option_code][-1]

                            print(f"\n?? 手动VWAP验证点 - {current_time}")
                            print(f"目标时间: {verify_time} (实际: {current_time_str}, 误差: {time_diff:.1f}秒)")
                            print(f"当前VWAP: {vwap_details['vwap']:.6f}")
                            print(f"当前价格: {current_price:.4f}" if current_price else "当前价格: 获取失败")
                            print(f"累计tick数: {vwap_details['tick_count']}")
                            print(f"请对比K线图中 {verify_time} 的VWAP值")
                            print("-" * 60)

        except Exception as e:
            print(f"? 手动VWAP检查点错误: {e}")



    def check_buy_conditions_with_batch(self, C, option_code, max_position):
        """检查买入条件 - 使用批次管理器 + 实际持仓验证"""
        try:
            print(f"?? 检查买入条件: {option_code}")

            # 1. 获取本地记录的持仓
            local_position = self.data.batch_manager.get_total_position(option_code)

            # 2. 获取QMT实际持仓进行验证
            real_position = self.get_current_position(C, option_code)

            # 3. 持仓同步逻辑
            if local_position != real_position:
                print(f"?? 检测到持仓不一致: 本地记录{local_position} vs QMT实际{real_position}")
                self.sync_position_records(C, option_code, local_position, real_position)
                # 更新后重新获取本地持仓
                local_position = self.data.batch_manager.get_total_position(option_code)
                print(f"?? 同步完成，当前本地持仓: {local_position}")

            # 4. 使用同步后的持仓进行判断
            current_position = local_position
            pending_quantity = len(self.data.batch_manager.pending_orders)

            print(f"   当前持仓:{current_position} 待成交:{pending_quantity} 最大:{max_position}")

            if current_position + pending_quantity >= max_position:
                print(f"? 持仓已满: {option_code} 当前:{current_position} 待成交:{pending_quantity} 最大:{max_position}")
                return False

            print(f"? 买入条件检查通过: {option_code}")
            return True

        except Exception as e:
            print(f"? 买入条件检查异常: {option_code} {e}")
            return False

    def sync_position_records(self, C, option_code, local_position, real_position):
        """同步本地批次记录与实际持仓"""
        try:
            if real_position == 0 and local_position > 0:
                # 情况1: 实际已清仓，但本地还有记录 → 清除本地记录
                print(f"?? 实际已清仓，清除本地批次记录: {option_code}")
                self.data.batch_manager.clear_option_batches(option_code)

            elif real_position > 0 and local_position == 0:
                # 情况2: 实际有持仓，但本地无记录 → 创建估算批次
                print(f"?? 实际有持仓但本地无记录，创建估算批次: {option_code} 数量:{real_position}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, real_position, estimated_price)

            elif real_position > 0 and local_position > real_position:
                # 情况3: 部分平仓 → 调整本地记录
                closed_quantity = local_position - real_position
                print(f"?? 检测到部分平仓，调整本地记录: {option_code} 平仓数量:{closed_quantity}")
                self.data.batch_manager.adjust_batches_for_partial_close(option_code, closed_quantity)

            elif real_position > local_position:
                # 情况4: 实际持仓更多 → 补充记录
                additional_quantity = real_position - local_position
                print(f"?? 实际持仓更多，补充批次记录: {option_code} 补充数量:{additional_quantity}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, additional_quantity, estimated_price)

        except Exception as e:
            print(f"? 持仓同步异常: {option_code} {e}")

    def get_current_market_price(self, C, option_code):
        """获取当前市价（用于估算批次）"""
        try:
            # 尝试获取最新价格
            tick_data = C.get_full_tick([option_code])
            if option_code in tick_data:
                price = tick_data[option_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)

            # 如果获取失败，返回默认估算价格
            print(f"?? 无法获取 {option_code} 的当前价格，使用默认估算价格")
            return 0.05  # 默认估算价格

        except Exception as e:
            print(f"? 获取市价失败: {option_code} {e}")
            return 0.05  # 默认估算价格

    def calculate_buy_quantity_with_batch(self, C, option_code, market_data, max_position):
        """计算买入数量 - 考虑部分成交和未完成数量"""
        try:
            current_position = self.data.batch_manager.get_total_position(option_code)
            pending_quantity = len(self.data.batch_manager.pending_orders)

            # 检查是否有未完成的买入数量
            remaining_quantity = self.data.pending_buy_quantities.get(option_code, 0)
            if remaining_quantity > 0:
                print(f"?? 发现未完成买入: {option_code} 剩余数量:{remaining_quantity}")
                target_quantity = min(remaining_quantity, market_data['ask_volume'])
            else:
                available_quantity = max_position - current_position - pending_quantity
                target_quantity = min(available_quantity, market_data['ask_volume'])

            print(f"?? 买入数量计算: {option_code} 卖1量:{market_data['ask_volume']} "
                  f"当前持仓:{current_position} 最大:{max_position} 目标:{target_quantity}")

            return max(0, target_quantity)

        except Exception as e:
            print(f"? 计算买入数量异常: {option_code} {e}")
            return 0

    def place_buy_order_with_batch_management(self, C, option_code, quantity, market_data, signal_type, max_position):
        """下买入委托并管理批次"""
        try:
            # 执行买入委托 - passorder是全局函数，不是C对象的方法
            order_result = passorder(
                50,                              # op_type: 50=期权买入开仓
                1101,                            # order_mode: 1101=按股数
                account,                         # account_id: 全局账户变量
                option_code,                     # contract: 合约代码
                5,                               # price_type: 5=市价
                market_data['ask_price'],        # exec_price: 执行价格（市价单也需要填写）
                quantity,                        # volume: 数量
                "期权策略",                       # strategy_name: 策略名称
                2,                               # quicktrade: 2=立即下单
                f"期权买入-{option_code}",        # msg: 备注信息
                C                                # C: 上下文对象作为最后一个参数
            )

            print(f"? 委托下单请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")

            print(f"?? 委托返回结果: {order_result} (类型: {type(order_result)})")

            # 根据QMT官方文档：passorder返回值是'无'，不能依赖返回值判断成功失败
            # QMT使用异步交易机制，委托状态通过回调函数获取
            print(f"?? 委托请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")
            print("? 等待回调函数确认委托状态...")

            # 记录委托尝试（不依赖返回值）
            try:
                # 生成临时委托ID，等待回调函数更新为真实委托号
                temp_order_id = f"{option_code}_{int(time.time())}"

                self.data.batch_manager.add_pending_order(
                    temp_order_id, option_code, market_data['ask_price'], quantity
                )
                print(f"?? 委托记录成功: {temp_order_id}")
                print("?? 等待order_callback和deal_callback确认...")

                # 记录委托时间，用于后续验证
                if not hasattr(self.data, 'pending_verifications'):
                    self.data.pending_verifications = []

                self.data.pending_verifications.append({
                    'temp_order_id': temp_order_id,
                    'option_code': option_code,
                    'target_quantity': quantity,
                    'target_price': market_data['ask_price'],
                    'timestamp': time.time()
                })

            except Exception as e:
                print(f"?? 记录委托失败: {e}")

            # 处理未完成数量
            current_position = self.data.batch_manager.get_total_position(option_code)
            original_target = max_position - current_position
            if quantity < original_target:
                # 记录未完成的数量，等待下次信号
                self.data.pending_buy_quantities[option_code] = original_target - quantity
                print(f"?? 记录未完成买入: {option_code} 剩余:{self.data.pending_buy_quantities[option_code]}")
            else:
                # 清除未完成数量
                self.data.pending_buy_quantities.pop(option_code, None)

        except Exception as e:
            print(f"? 下单异常: {option_code} {e}")
            logging.error(f"下单异常: {option_code} {e}")



    def get_market_data(self, C, option_code):
        """获取市场数据"""
        try:
            # 方法1: 尝试使用get_market_data_ex获取五档行情
            try:
                market_data = C.get_market_data_ex([option_code], period='tick', count=1, subscribe=True)
                if market_data and option_code in market_data and len(market_data[option_code]) > 0:
                    tick_data = market_data[option_code][-1]

                    # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                    ask_prices = tick_data.get('askPrice', [])
                    ask_volumes = tick_data.get('askVol', [])
                    bid_prices = tick_data.get('bidPrice', [])
                    bid_volumes = tick_data.get('bidVol', [])

                    result = {
                        'ask_price': ask_prices[0] if ask_prices else 0,      # 卖1价
                        'ask_volume': ask_volumes[0] if ask_volumes else 0,    # 卖1量
                        'bid_price': bid_prices[0] if bid_prices else 0,      # 买1价
                        'bid_volume': bid_volumes[0] if bid_volumes else 0,    # 买1量
                        'last_price': tick_data.get('lastPrice', 0)           # 最新价
                    }

                    print(f"?? 市场数据(订阅): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                    return result
            except Exception as e1:
                print(f"?? 订阅方式获取行情失败: {e1}")

            # 方法2: 使用get_full_tick获取基础行情
            full_tick = C.get_full_tick([option_code])
            if full_tick and option_code in full_tick:
                tick_data = full_tick[option_code]

                # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                ask_prices = tick_data.get('askPrice', [])
                ask_volumes = tick_data.get('askVol', [])
                bid_prices = tick_data.get('bidPrice', [])
                bid_volumes = tick_data.get('bidVol', [])

                result = {
                    'ask_price': ask_prices[0] if ask_prices else tick_data.get('lastPrice', 0),      # 卖1价，如果没有则用最新价
                    'ask_volume': ask_volumes[0] if ask_volumes else 100,    # 卖1量，如果没有则假设100
                    'bid_price': bid_prices[0] if bid_prices else tick_data.get('lastPrice', 0),      # 买1价，如果没有则用最新价
                    'bid_volume': bid_volumes[0] if bid_volumes else 100,    # 买1量，如果没有则假设100
                    'last_price': tick_data.get('lastPrice', 0)      # 最新价
                }

                print(f"?? 市场数据(全推): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                return result

            print(f"? 无法获取市场数据: {option_code}")
            return None

        except Exception as e:
            print(f"? 获取市场数据异常: {option_code} {e}")
            return None





    def get_current_position(self, C, option_code):
        """获取当前持仓"""
        try:
            # 使用QMT API获取持仓 - get_trade_detail_data是全局函数
            positions = get_trade_detail_data(account, 'STOCK_OPTION', 'POSITION', C)
            if not positions:
                return 0

            for position in positions:
                if hasattr(position, 'm_strInstrumentID') and position.m_strInstrumentID == option_code.split('.')[0]:
                    return getattr(position, 'm_nVolume', 0)

            return 0

        except Exception as e:
            print(f"? 获取持仓异常: {option_code} {e}")
            return 0



    def get_available_cash(self, C):
        """获取可用资金"""
        try:
            # 使用QMT API获取账户资金 - get_trade_detail_data是全局函数
            accounts = get_trade_detail_data(account, 'STOCK_OPTION', 'ACCOUNT', C)
            if accounts and len(accounts) > 0:
                return getattr(accounts[0], 'm_dAvailable', 0)
            return 0

        except Exception as e:
            print(f"? 获取可用资金异常: {e}")
            return 0





    def check_pending_verifications(self, C):
        """检查待验证的委托（备用方案）"""
        if not hasattr(self.data, 'pending_verifications'):
            return

        # ?? 首先检查委托超时并撤单
        self.check_and_cancel_timeout_orders(C)

        current_time = time.time()

        for verification in self.data.pending_verifications[:]:  # 复制列表避免修改问题
            # 如果超过30秒还没有回调，主动查询
            if current_time - verification['timestamp'] > 30:
                print(f"? 委托超时，启动主动验证: {verification['temp_order_id']}")

                try:
                    # 查询持仓变化
                    option_code = verification['option_code']
                    current_position = self.get_current_position(option_code, C)

                    # 如果持仓增加，推断交易成功
                    if current_position > 0:
                        print(f"?? 检测到持仓增加: {option_code} 持仓:{current_position}")
                        print("?? 推断交易成功，手动创建批次记录")

                        # 手动创建批次记录
                        self.create_manual_batch(verification)

                    else:
                        print(f"?? 未检测到持仓变化: {option_code}")
                        print("?? 可能交易失败或还在处理中")

                except Exception as e:
                    print(f"? 主动验证失败: {e}")

                # 移除已检查的项目
                self.data.pending_verifications.remove(verification)

    def create_manual_batch(self, verification):
        """手动创建批次记录（当回调函数不工作时）"""
        try:
            # 创建手动批次
            batch = {
                'batch_id': len(self.data.batch_manager.batches) + 1,
                'option_code': verification['option_code'],
                'order_id': verification['temp_order_id'],
                'entry_price': verification['target_price'],  # 使用目标价格
                'quantity': verification['target_quantity'],   # 使用目标数量
                'cost': verification['target_price'] * verification['target_quantity'] * 10000,
                'commission': 5.0,  # 估算手续费
                'trade_time': time.strftime('%H:%M:%S'),
                'trade_date': time.strftime('%Y-%m-%d'),
                'timestamp': time.time(),
                'source': 'manual_verification'  # 标记为手动验证创建
            }

            self.data.batch_manager.batches.append(batch)

            print(f"?? 手动批次创建成功: {verification['option_code']}")
            print(f"   批次#{batch['batch_id']} 价格:{batch['entry_price']:.4f} 数量:{batch['quantity']}")
            print(f"   来源: 主动验证（回调函数未触发）")

            # 显示当前持仓汇总
            total_position = self.data.batch_manager.get_total_position(verification['option_code'])
            avg_cost = self.data.batch_manager.get_average_cost(verification['option_code'])
            print(f"?? 持仓汇总: {verification['option_code']}")
            print(f"   总持仓: {total_position}")
            print(f"   平均成本: {avg_cost:.4f}")

        except Exception as e:
            print(f"? 手动批次创建失败: {e}")

    def check_and_cancel_timeout_orders(self, C):
        """检查并撤销超时委托"""
        try:
            if not hasattr(self.data, 'batch_manager') or not self.data.batch_manager.pending_orders:
                return

            current_time = time.time()
            timeout_seconds = self.pm.get('order_timeout_seconds', 5)  # 获取超时设置，默认5秒
            timeout_orders = []

            # 检查所有待成交委托
            for order_id, order_info in self.data.batch_manager.pending_orders.items():
                if order_info['status'] == 'pending':
                    elapsed_time = current_time - order_info['timestamp']
                    if elapsed_time > timeout_seconds:
                        # ?? 额外安全检查：如果有真实委托号，先检查是否已经有对应的批次记录
                        real_order_id = order_info.get('real_order_id')
                        if real_order_id:
                            # 检查是否已经有该委托号的批次记录（说明已成交）
                            already_executed = any(
                                batch.get('order_id') == real_order_id
                                for batch in self.data.batch_manager.batches
                            )
                            if already_executed:
                                print(f"?? 跳过已成交委托的撤单: {order_id} 真实委托号:{real_order_id}")
                                # 直接清理该待成交记录，不执行撤单
                                self.data.batch_manager.pending_orders[order_id]['status'] = 'completed'
                                continue

                        timeout_orders.append((order_id, order_info, elapsed_time))

            # 撤销超时委托
            for order_id, order_info, elapsed_time in timeout_orders:
                option_code = order_info['option_code']
                print(f"? 检测到超时委托: {option_code} 委托:{order_id} 已等待:{elapsed_time:.1f}秒")

                # 获取真实委托号（如果有的话）
                real_order_id = order_info.get('real_order_id', order_id)

                # 尝试撤销委托
                if self.cancel_order(C, real_order_id):
                    print(f"? 撤销超时委托成功: {option_code} 真实委托号:{real_order_id}")
                    # 更新委托状态
                    self.data.batch_manager.pending_orders[order_id]['status'] = 'cancelled'
                    # 从待成交列表中移除
                    del self.data.batch_manager.pending_orders[order_id]
                else:
                    print(f"? 撤销委托失败: {option_code} 真实委托号:{real_order_id}")

            # ?? 清理已完成状态的委托记录
            completed_orders = [
                order_id for order_id, order_info in self.data.batch_manager.pending_orders.items()
                if order_info['status'] == 'completed'
            ]
            for order_id in completed_orders:
                del self.data.batch_manager.pending_orders[order_id]
                print(f"?? 清理已完成委托记录: {order_id}")

        except Exception as e:
            print(f"? 检查超时委托异常: {e}")

    def cancel_order(self, C, order_id):
        """撤销委托"""
        try:
            # 使用QMT API撤销委托 - cancel是全局函数
            cancel_result = cancel(order_id, account, 'STOCK_OPTION', C)

            if cancel_result:
                print(f"?? 撤单API调用成功: {order_id}")
                return True
            else:
                print(f"?? 撤单API调用失败: {order_id}")
                return False

        except Exception as e:
            print(f"? 撤销委托异常: {order_id} {e}")
            return False

# ==================== 全局监控对象 ====================
monitor = OptionMonitor()

# ==================== QMT全局变量 ====================
# ==================== QMT回测主程序 ====================
def init(ContextInfo):
    """初始化函数 - 回测版本（参考精确VWAP验证.py）"""
    print("?? QMT期权策略回测版本启动")
    print("=" * 60)

    # 关键：设置测试参数到ContextInfo（与精确VWAP验证.py一致）
    ContextInfo.test_option = monitor.pm.get('test_option_code', '********.SHO')
    ContextInfo.test_date = BACKTEST_CONFIG['backtest_start_date']

    print(f"?? 测试合约: {ContextInfo.test_option}")
    print(f"?? 测试日期: {ContextInfo.test_date}")

    # 回测模式配置
    if BACKTEST_CONFIG['enable_backtest']:
        print("?? 回测模式配置:")
        print(f"  - 回测开始日期: {BACKTEST_CONFIG['backtest_start_date']}")
        print(f"  - 回测结束日期: {BACKTEST_CONFIG['backtest_end_date']}")
        print(f"  - 自动下载历史数据: {BACKTEST_CONFIG['download_history_data']}")

        # QMT回测模式会自动处理历史数据
        print("?? QMT回测模式：历史数据将自动加载")
        print("?? 请确保在QMT界面设置了正确的回测日期范围")

    # 显示策略参数
    print("\n?? 策略参数:")
    print(f"  - 标的代码: {monitor.data.underlying_code}")
    print(f"  - 信号阈值: 连续{monitor.pm.get('signal_threshold', 5)}个同方向tick触发信号")
    print(f"  - 震荡检测: {monitor.pm.get('oscillation_periods', 3)}个周期，每周期{monitor.pm.get('oscillation_period_size', 5)}个tick")
    print(f"  - 测试期权: {monitor.pm.get('test_option_code', '********.SHO')}")

    print("\n?? 回测设置:")
    print(f"  - 真实交易: 关闭 (回测模式)")
    print(f"  - 数据存储: 内存模式 (无持久化)")
    print(f"  - 日志级别: 简化模式")

    print("=" * 60)
    print("? 初始化完成，等待历史数据加载...")

    # 记录启动信息
    logging.info(f"回测启动 - 标的:{monitor.data.underlying_code} 日期:{BACKTEST_CONFIG['backtest_start_date']}-{BACKTEST_CONFIG['backtest_end_date']}")

def after_init(ContextInfo):
    """初始化后执行 - 回测版本"""
    try:
        print("?? 回测模式：开始设置监控合约...")

        # 回测模式使用固定的测试期权
        test_option = monitor.pm.get('test_option_code', '********.SHO')
        monitor.data.selected_options = [test_option]

        print(f"? 回测合约设置: {test_option}")
        print("?? 回测模式将通过handlebar函数处理历史数据")

        # 测试数据获取
        try:
            print("?? 测试数据获取...")
            tick_data = ContextInfo.get_full_tick([test_option])
            if test_option in tick_data:
                tick_info = tick_data[test_option]
                current_price = tick_info.get('lastPrice', 0)
                print(f"? 成功获取当前价格: {current_price}")
            else:
                print(f"?? 未获取到 {test_option} 的tick数据")
        except Exception as e:
            print(f"?? 数据获取测试失败: {e}")

        # 显示监控规则
        signal_threshold = monitor.pm.get('signal_threshold', 5)
        oscillation_periods = monitor.pm.get('oscillation_periods', 3)
        oscillation_period_size = monitor.pm.get('oscillation_period_size', 5)

        print(f"\n?? 监控规则:")
        print(f"  - 连续信号: 连续{signal_threshold}个同方向tick触发")
        print(f"  - 震荡信号: {oscillation_periods}个周期，每周期{oscillation_period_size}个tick")
        print(f"  - 价格链长度: {monitor.pm.get('max_chain_length', 30)}个tick")

        print("\n?? 回测准备完成，等待历史数据...")
        print("=" * 60)

    except Exception as e:
        print(f"? 回测初始化失败: {e}")
        logging.error(f"回测初始化失败: {e}")

def handlebar(ContextInfo):
    """K线回调函数 - 回测版本（使用历史分笔数据）"""
    try:
        # 回测模式下，处理每根K线（移除is_last_bar限制）
        # 这样可以获取到历史数据进行动态计算

        # 只在第一次调用时执行完整的回测流程
        if not hasattr(ContextInfo, '_backtest_executed'):
            ContextInfo._backtest_executed = True
        else:
            return  # 避免重复执行

        print("\n" + "="*60)
        print("?? QMT期权策略回测 - 处理历史数据")
        print("="*60)

        # 获取测试合约和日期（使用ContextInfo中设置的参数，与精确VWAP验证.py一致）
        test_option = getattr(ContextInfo, 'test_option', '********.SHO')
        today_str = getattr(ContextInfo, 'test_date', '20250704')

        # 关键修复：使用当前K线的时间作为end_time（参考官方文档回测示例）
        try:
            current_bar_time = ContextInfo.get_bar_timetag(ContextInfo.barpos)
            if hasattr(current_bar_time, 'strftime'):
                current_time_str = current_bar_time.strftime('%Y%m%d%H%M%S')
            else:
                # 如果获取不到当前时间，使用默认时间
                current_time_str = today_str + '150000'
        except:
            current_time_str = today_str + '150000'

        print(f"?? 回测日期: {today_str}")
        print(f"?? 测试合约: {test_option}")
        print(f"? 当前时间: {current_time_str}")

        # 使用历史分笔数据获取方法（完全参考精确VWAP验证.py）
        try:
            print(f"?? 获取 {test_option} 的历史分笔数据...")

            # 从开盘时间09:30开始获取数据（VWAP应该从开盘开始计算）
            tick_data = ContextInfo.get_market_data_ex(
                fields=['lastPrice', 'volume', 'amount'],
                stock_code=[test_option],
                period='tick',
                start_time=today_str + '093000',  # 从9:30开盘开始，VWAP需要从开盘计算
                end_time=current_time_str,        # 到当前时间
                count=-1,
                subscribe=False  # 历史数据不需要订阅
            )

            print(f"?? 数据获取结果: {list(tick_data.keys()) if tick_data else '无数据'}")

            if tick_data and test_option in tick_data:
                df = tick_data[test_option]
                print(f"?? 数据框信息: 形状={df.shape if hasattr(df, 'shape') else 'N/A'}")

                if hasattr(df, 'empty') and not df.empty and len(df) > 0:
                    print(f"?? 获取到{len(df)}条分笔数据")

                    # 初始化VWAP计算器（只重置一次）
                    monitor.vwap_calculator.reset_vwap(test_option)
                    print(f"?? VWAP计算器已重置: {test_option}")

                    # 处理每个tick数据
                    processed_count = 0

                    for index, row in df.iterrows():
                        try:
                            price = float(row['lastPrice'])
                            volume = float(row.get('volume', 1.0))  # 获取真实成交量
                            if price > 0:
                                # 获取真实的历史时间戳
                                real_timestamp = None

                                # 从QMT索引中解析真实时间戳
                                # QMT索引格式: 20250704093200.230 (YYYYMMDDHHMMSS.mmm)
                                index_str = str(index)
                                if len(index_str) >= 14:
                                    try:
                                        # 解析日期和时间部分
                                        date_part = index_str[:8]    # 20250704
                                        time_part = index_str[8:14]  # 093200

                                        # 格式化为标准时间戳
                                        year = date_part[:4]
                                        month = date_part[4:6]
                                        day = date_part[6:8]
                                        hour = time_part[:2]
                                        minute = time_part[2:4]
                                        second = time_part[4:6]

                                        real_timestamp = f"{year}-{month}-{day} {hour}:{minute}:{second}"
                                    except Exception as e:
                                        # 备用方案：使用索引位置生成近似时间
                                        base_time = datetime.strptime(today_str + '093200', '%Y%m%d%H%M%S')
                                        from datetime import timedelta
                                        approx_time = base_time + timedelta(seconds=processed_count * 0.5)
                                        real_timestamp = approx_time.strftime('%Y-%m-%d %H:%M:%S')
                                else:
                                    # 备用方案：使用索引位置生成近似时间
                                    base_time = datetime.strptime(today_str + '093200', '%Y%m%d%H%M%S')
                                    from datetime import timedelta
                                    approx_time = base_time + timedelta(seconds=processed_count * 0.5)
                                    real_timestamp = approx_time.strftime('%Y-%m-%d %H:%M:%S')

                                # 先更新参考VWAP（包含所有原始tick）
                                monitor.vwap_calculator.update_reference_vwap(test_option, price, volume)

                                # 处理这个tick，传入真实时间戳和成交量（会进行过滤）
                                monitor.process_tick_data(ContextInfo, test_option, price, real_timestamp, volume)
                                processed_count += 1
                        except Exception as e:
                            continue

                    print(f"? 成功处理{processed_count}个有效tick")

                    # 显示最终VWAP
                    final_vwap = monitor.vwap_calculator.get_current_vwap(test_option)
                    if final_vwap:
                        print(f"?? 最终VWAP: {test_option} = {final_vwap:.4f}")

                    # 显示最终统计
                    if len(monitor.data.batch_manager.batches) > 0:
                        print(f"?? 触发信号数量: {len(monitor.data.batch_manager.batches)}")
                        monitor.data.batch_manager.show_all_batches()
                    else:
                        print("?? 本次回测未触发任何信号")

                else:
                    print("? 获取的数据框为空")
            else:
                print(f"? 未获取到 {test_option} 的分笔数据")
                print("?? 可能原因:")
                print("   1. 合约代码不正确或已到期")
                print("   2. 回测日期非交易日")
                print("   3. 数据权限问题")

        except Exception as e:
            print(f"? 历史数据获取失败: {e}")
            import traceback
            traceback.print_exc()

        print("="*60)
        print("?? 回测数据处理完成")
        print("="*60)

    except Exception as e:
        print(f"? handlebar处理异常: {e}")
        logging.error(f"handlebar处理异常: {e}")
        import traceback
        traceback.print_exc()

# ==================== 工具函数 ====================
def update_parameter(param_name, new_value):
    """运行时更新参数"""
    try:
        monitor.pm.set(param_name, new_value)
        print(f"? 参数已更新: {param_name} = {new_value}")
    except Exception as e:
        print(f"? 参数更新失败: {e}")

def show_parameters():
    """显示当前参数"""
    monitor.pm.print_params()

def get_strategy_status():
    """获取策略状态"""
    selected_count = len(monitor.data.selected_options)
    total_signals = sum(len(signals) for signals in monitor.data.signals.values())
    active_trends = sum(1 for count in monitor.data.trend_count.values() if count > 0)

    print(f"?? 策略状态:")
    print(f"  - 监控合约: {selected_count} 个")
    print(f"  - 总信号数: {total_signals} 个")
    print(f"  - 活跃趋势: {active_trends} 个")

    # 显示每个合约的信号统计
    for option_code in monitor.data.selected_options:
        if option_code in monitor.data.signals:
            signals = monitor.data.signals[option_code]
            buy_count = sum(1 for s in signals if s['type'] == '买入')
            sell_count = sum(1 for s in signals if s['type'] == '卖出')
            print(f"  - {option_code}: 买入{buy_count}次, 卖出{sell_count}次")

def emergency_stop():
    """紧急停止"""
    print("?? 执行紧急停止...")
    monitor.data.selected_options.clear()
    monitor.data.trend_count.clear()
    monitor.data.trend_direction.clear()
    monitor.data.trend_prices.clear()
    print("? 策略已停止，所有状态已清空")

# ?? VWAP工具函数
def get_current_vwap(C, option_code=None):
    """获取当前VWAP价格"""
    try:
        if option_code is None:
            option_code = monitor.data.selected_options[0] if monitor.data.selected_options else None

        if option_code:
            vwap = monitor.get_current_vwap(C, option_code)
            if vwap:
                print(f"?? {option_code} 当前VWAP: {vwap:.6f}")
                return vwap
            else:
                print(f"? 无法获取 {option_code} 的VWAP")
        else:
            print("? 没有可用的期权合约")
        return None
    except Exception as e:
        print(f"? 获取VWAP失败: {e}")
        return None

def show_batch_summary():
    """显示批次汇总"""
    monitor.data.batch_manager.show_all_batches()

def clear_all_batches():
    """清空所有批次记录"""
    try:
        monitor.data.batch_manager.batches = []
        monitor.data.batch_manager.save_batches()
        print("? 所有批次记录已清空")
    except Exception as e:
        print(f"? 清空失败: {e}")

# ==================== QMT回调函数（回测简化版本） ====================
def deal_callback(ContextInfo, dealInfo):
    """成交回调 - 回测模式不需要"""
    pass

def order_callback(ContextInfo, orderInfo):
    """委托回调 - 回测模式不需要"""
    pass

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """下单异常回调 - 回测模式不需要"""
    pass

# ==================== 回测结果汇总 ====================
def show_backtest_summary():
    """显示回测结果汇总"""
    print("\n" + "="*80)
    print("?? QMT期权策略回测结果汇总")
    print("="*80)

    batches = monitor.data.batch_manager.batches
    if not batches:
        print("?? 本次回测未触发任何信号")
        return

    # 统计信号类型
    signal_stats = {}
    for batch in batches:
        signal_type = batch.get('signal_type', 'Unknown')
        if signal_type not in signal_stats:
            signal_stats[signal_type] = 0
        signal_stats[signal_type] += 1

    print(f"?? 信号统计:")
    for signal_type, count in signal_stats.items():
        print(f"  - {signal_type}: {count}次")

    print(f"\n?? 详细记录:")
    for i, batch in enumerate(batches, 1):
        signal_type = batch.get('signal_type', 'N/A')
        print(f"{i:2d}. {batch['trade_time']} | {batch['option_code']} | "
              f"{signal_type} | 价格:{batch['entry_price']:.4f}")

    print("="*80)
    print("?? 回测完成！以上为信号触发记录，可用于策略优化分析")
    print("="*80)

# ==================== 全局变量 ====================
# 全局监控器实例
monitor = OptionMonitor()

# 在回测结束时自动显示汇总
import atexit
atexit.register(show_backtest_summary)
