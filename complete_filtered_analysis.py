#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的带过滤器策略分析流程
一键生成包含价格跟踪和过滤器信息的完整CSV文件
"""

import subprocess
import sys
import os
from datetime import datetime

def run_script(script_name, description):
    """运行脚本并返回结果"""
    print(f"\n🚀 {description}")
    print(f"📄 运行: {script_name}")
    print("-" * 50)
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, 
                              text=True, 
                              timeout=120)
        
        if result.returncode == 0:
            print(f"✅ {description} - 完成")
            # 显示关键输出
            if result.stdout:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    if any(keyword in line for keyword in ['✅', '📊', '📄', '总信号', '通过率']):
                        print(f"   {line}")
            return True
        else:
            print(f"❌ {description} - 失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"⏰ {description} - 超时")
        return False
    except Exception as e:
        print(f"❌ {description} - 异常: {e}")
        return False

def check_files():
    """检查必要文件是否存在"""
    required_files = {
        '1.txt': '日志文件',
        'extract_signals.py': '信号提取脚本',
        'extract_signals_with_filter.py': '过滤器信号提取脚本'
    }
    
    missing_files = []
    for file_name, description in required_files.items():
        if not os.path.exists(file_name):
            missing_files.append(f"{file_name} ({description})")
    
    if missing_files:
        print("❌ 缺少必要文件:")
        for file_name in missing_files:
            print(f"   - {file_name}")
        return False
    
    print("✅ 所有必要文件都存在")
    return True

def analyze_results():
    """分析生成的结果文件"""
    print(f"\n📊 分析结果文件:")
    print("-" * 50)
    
    result_files = {
        '买入信号含价格跟踪数据.xlsx': '原始信号数据（包含价格跟踪）',
        '带过滤器的买入信号数据.csv': '完整分析结果（包含过滤器信息）'
    }
    
    for file_name, description in result_files.items():
        if os.path.exists(file_name):
            file_size = os.path.getsize(file_name)
            print(f"✅ {file_name}")
            print(f"   {description}")
            print(f"   文件大小: {file_size:,} bytes")
        else:
            print(f"❌ {file_name} - 未生成")
    
    # 读取并分析最终结果
    try:
        import pandas as pd
        df = pd.read_csv('带过滤器的买入信号数据.csv', encoding='utf-8-sig')
        
        print(f"\n📈 详细统计:")
        print(f"   总买入信号: {len(df)}个")
        
        passed_df = df[df['最终通过'] == True]
        rejected_df = df[df['最终通过'] == False]
        
        print(f"   通过过滤器: {len(passed_df)}个 ({len(passed_df)/len(df)*100:.1f}%)")
        print(f"   被过滤器拒绝: {len(rejected_df)}个 ({len(rejected_df)/len(df)*100:.1f}%)")
        
        # 过滤原因统计
        filter_reasons = df['过滤原因'].value_counts()
        print(f"\n🔍 过滤原因分布:")
        for reason, count in filter_reasons.items():
            percentage = count / len(df) * 100
            print(f"   {reason}: {count}次 ({percentage:.1f}%)")
        
        # 模式分类统计
        category_stats = df['模式分类'].value_counts()
        print(f"\n📊 VWAP模式分布:")
        for category, count in category_stats.items():
            percentage = count / len(df) * 100
            print(f"   {category}: {count}次 ({percentage:.1f}%)")
        
        # 检查价格跟踪数据完整性
        price_tracking_columns = ['10tick后价格', '30tick后价格', '5分钟后价格', '触发序列']
        missing_data = 0
        for col in price_tracking_columns:
            if col in df.columns:
                null_count = df[col].isnull().sum()
                if null_count > 0:
                    missing_data += null_count
        
        if missing_data == 0:
            print(f"\n✅ 价格跟踪数据完整")
        else:
            print(f"\n⚠️ 部分价格跟踪数据缺失: {missing_data}个字段")
        
    except Exception as e:
        print(f"❌ 无法分析结果文件: {e}")

def main():
    """主函数"""
    print("🎯 完整的带过滤器策略分析")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 检查文件
    if not check_files():
        print("\n❌ 分析终止：缺少必要文件")
        return False
    
    # 执行分析流程
    analysis_steps = [
        ('extract_signals.py', '步骤1: 提取原始信号和价格跟踪数据'),
        ('extract_signals_with_filter.py', '步骤2: 合并过滤器信息生成完整数据')
    ]
    
    success_count = 0
    for script, description in analysis_steps:
        if run_script(script, description):
            success_count += 1
        else:
            print(f"❌ 关键步骤失败: {description}")
            break
    
    # 分析结果
    if success_count == len(analysis_steps):
        analyze_results()
        
        print(f"\n🎉 完整分析流程成功完成！")
        print("="*80)
        print("📁 生成的关键文件:")
        print("   📄 带过滤器的买入信号数据.csv - 完整分析结果")
        print("   📄 买入信号含价格跟踪数据.xlsx - 原始信号数据")
        print("\n💡 使用建议:")
        print("   1. 打开 '带过滤器的买入信号数据.csv' 查看完整分析结果")
        print("   2. 重点关注 '最终通过' 和 '过滤原因' 列")
        print("   3. 分析价格跟踪数据评估信号质量")
        print("   4. 根据结果调整VWAP过滤器参数")
        
        return True
    else:
        print(f"\n❌ 分析流程失败")
        print(f"完成步骤: {success_count}/{len(analysis_steps)}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
