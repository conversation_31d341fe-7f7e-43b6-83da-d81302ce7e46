import pandas as pd
import sys

def calculate_zigzag(prices, deviation_percentage):
    if not prices:
        return []

    zigzag_points = []
    
    # Initial point
    zigzag_points.append((0, prices[0]))
    last_pivot_idx = 0
    last_pivot_price = prices[0]
    
    trend = 0 # 0: unknown, 1: uptrend, -1: downtrend

    for i in range(1, len(prices)):
        current_price = prices[i]

        if trend == 0: # Determine initial trend
            if current_price >= last_pivot_price * (1 + deviation_percentage):
                trend = 1 # Established uptrend
                last_pivot_idx = i
                last_pivot_price = current_price
                zigzag_points.append((i, current_price)) # Add the first peak
            elif current_price <= last_pivot_price * (1 - deviation_percentage):
                trend = -1 # Established downtrend
                last_pivot_idx = i
                last_pivot_price = current_price
                zigzag_points.append((i, current_price)) # Add the first trough

        elif trend == 1: # Currently in uptrend, looking for a peak or reversal to downtrend
            if current_price > last_pivot_price:
                last_pivot_idx = i
                last_pivot_price = current_price
                zigzag_points[-1] = (i, current_price)
            elif current_price <= last_pivot_price * (1 - deviation_percentage):
                trend = -1
                zigzag_points.append((i, current_price))
                last_pivot_idx = i
                last_pivot_price = current_price

        elif trend == -1: # Currently in downtrend, looking for a trough or reversal to uptrend
            if current_price < last_pivot_price:
                last_pivot_idx = i
                last_pivot_price = current_price
                zigzag_points[-1] = (i, current_price)
            elif current_price >= last_pivot_price * (1 + deviation_percentage):
                trend = 1
                zigzag_points.append((i, current_price))
                last_pivot_idx = i
                last_pivot_price = current_price
    
    if zigzag_points and zigzag_points[-1][0] != len(prices) - 1:
        zigzag_points.append((len(prices) - 1, prices[-1]))

    return zigzag_points

def run_backtest(file_path, deviation):
    df = pd.read_csv(file_path)
    prices = df['价格'].tolist()

    zigzag_points = calculate_zigzag(prices, deviation)

    total_patterns = 0
    winning_trades = 0
    losing_trades = 0
    total_profit = 0.0
    total_loss = 0.0
    ticks_to_win_list = []
    ticks_to_loss_list = []
    winning_percentage_gains = [] # To store actual gains for winning trades

    for i in range(len(zigzag_points) - 2):
        point1_idx, point1_price = zigzag_points[i]
        point2_idx, point2_price = zigzag_points[i+1]
        point3_idx, point3_price = zigzag_points[i+2]

        if point1_price > point2_price and point2_price < point3_price:
            total_patterns += 1

            buy_price = point3_price
            profit_target_price = buy_price + 0.001 # Profit target: 0.001
            stop_loss_price = buy_price - 0.0005 # Stop loss: 0.0005

            trade_outcome = "NONE" # Initialize trade outcome
            ticks_taken = 0

            # Simulate trade after P2
            for j in range(point3_idx + 1, len(prices)):
                current_price_after_p2 = prices[j]
                ticks_taken = j - point3_idx

                if current_price_after_p2 >= profit_target_price:
                    trade_outcome = "WIN"
                    winning_trades += 1
                    total_profit += (profit_target_price - buy_price) # Actual profit
                    ticks_to_win_list.append(ticks_taken)
                    winning_percentage_gains.append(((profit_target_price - buy_price) / buy_price) * 100) # Store target gain
                    break
                elif current_price_after_p2 <= stop_loss_price:
                    trade_outcome = "LOSS"
                    losing_trades += 1
                    total_loss += (buy_price - stop_loss_price) # Actual loss
                    ticks_to_loss_list.append(ticks_taken)
                    break
            
            # If loop finishes without hitting target or stop-loss, it's a loss
            if trade_outcome == "NONE":
                losing_trades += 1
                total_loss += (buy_price - prices[-1]) # Loss is difference from buy to last price
                ticks_to_loss_list.append(ticks_taken)

    avg_winning_gain_for_file = sum(winning_percentage_gains) / len(winning_percentage_gains) if winning_percentage_gains else 0.0

    return total_patterns, winning_trades, losing_trades, total_profit, total_loss, ticks_to_win_list, ticks_to_loss_list, avg_winning_gain_for_file

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python optimize_zigzag.py <file_path> <deviation>")
        sys.exit(1)

    file_path = sys.argv[1]
    deviation = float(sys.argv[2])

    total_patterns, winning_trades, losing_trades, total_profit, total_loss, ticks_to_win_list, ticks_to_loss_list, avg_winning_gain = run_backtest(file_path, deviation)
    
    # Format ticks lists for output
    ticks_to_win_str = '-'.join(map(str, ticks_to_win_list))
    ticks_to_loss_str = '-'.join(map(str, ticks_to_loss_list))

    print(f"{total_patterns},{winning_trades},{losing_trades},{total_profit:.6f},{total_loss:.6f},{ticks_to_win_str},{ticks_to_loss_str},{avg_winning_gain:.6f}")