# coding: gbk
import time
from datetime import datetime, timedelta
import xml.etree.ElementTree as ET
import os
import pandas as pd
import random
import traceback

# 全局订单跟踪字典
order_tracker = {
    'orders': {},
    'positions': {}
}

# 1. 策略状态类
class StrategyState:
    def __init__(self, C):
        """策略状态初始化"""
        self.C = C  # 存储C
        self.subID = 0
        self.undl_code = '510300.SH'  # 标的代码
        self.monitors = {}
        self.account_manager = None
        self.last_check_time = 0

# 全局策略状态实例
strategy_state = None

# 工具函数
def show_data(data):
    """
    收集对象的关键数据
    @param data: 数据对象
    @return: dict 包含关键字段的字典
    """
    key_fields = {
        'm_strAccountID': '账户ID',
        'm_dAvailable': '可用资金',
        'm_dBalance': '账户余额',
        'm_dInstrumentValue': '持仓市值',
        'm_nOrderStatus': '订单状态',
        'm_strInstrumentID': '合约代码',
        'm_strInstrumentName': '合约名称',
        'm_dLimitPrice': '委托价格',
        'm_nVolumeTotal': '委托数量',
        'm_strOrderSysID': '系统委托号',
        'm_strErrorMsg': '错误信息'
    }
    
    result = {}
    for field, _ in key_fields.items():
        try:
            if hasattr(data, field):
                result[field] = getattr(data, field)
            else:
                result[field] = '<未知>'
        except Exception:
            result[field] = '<未知>'
    return result

# 2. 回调函数组
def account_callback(ContextInfo, accountInfo):
    """账户状态变化回调"""
    if not hasattr(ContextInfo, 'account_manager'):
        return
        
    try:
        # 让AccountManager处理更新
        ContextInfo.account_manager.update(ContextInfo)
    except Exception as e:
        print(f"账户回调处理异常: {str(e)}")

def order_callback(ContextInfo, orderInfo):
    data = show_data(orderInfo)
    key_info = f"订单号: {data['m_strOrderSysID']}, 合约: {data['m_strInstrumentID']}, 状态: {data['m_nOrderStatus']}"
    print(f"订单回调触发: {key_info}")
    try:
        if hasattr(ContextInfo, 'monitors'):
            contract_code = getattr(orderInfo, 'm_strInstrumentID', '')
            if not contract_code.endswith('.SHO'):
                contract_code += '.SHO'  # 统一添加 .SHO 后缀
            if contract_code in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract_code]
                monitor.update_order_status(orderInfo)
            else:
                print(f"未找到合约 {contract_code} 的监控器")
    except Exception as e:
        print(f"订单回调处理异常: {str(e)}")

def deal_callback(ContextInfo, dealInfo):
    try:
        trade_id = getattr(dealInfo, 'm_strTradeID', '')
        order_id = getattr(dealInfo, 'm_strOrderSysID', '')
        contract = getattr(dealInfo, 'm_strInstrumentID', '')
        if not contract.endswith('.SHO'):
            contract += '.SHO'
        direction = '买入' if getattr(dealInfo, 'm_nOffsetFlag', 0) == 48 else '卖出'
        volume = getattr(dealInfo, 'm_nVolume', 0)
        price = getattr(dealInfo, 'm_dPrice', 0.0)
        trade_time = getattr(dealInfo, 'm_strTradeTime', '')
        
        print(f"\n>>> 新成交 <<< 合约: {contract}, 方向: {direction}, 数量: {volume}, 价格: {price:.4f}, 时间: {trade_time}")
        
        if hasattr(ContextInfo, 'account_manager'):
            ContextInfo.account_manager.update(ContextInfo)
            if hasattr(ContextInfo, 'monitors') and contract in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract]
                offset_flag = getattr(dealInfo, 'm_nOffsetFlag', 0)
                print(f"【{contract}】–>成交回调: OffsetFlag={offset_flag}")
                
                if direction == '买入':
                    monitor.on_order_filled(price, volume, 50)
                    monitor.position += volume
                    monitor.in_position = True
                    if not monitor.entry_price:
                        monitor.entry_price = price
                        monitor.original_entry_price = price
                        monitor.entry_batches = [{'price': price, 'volume': volume}]
                    else:
                        monitor.entry_batches.append({'price': price, 'volume': volume})
                    print(f"【{contract}】–>成交同步: 持仓={monitor.position}, 开仓价={monitor.entry_price:.4f}")
                elif direction == '卖出':
                    monitor.on_order_filled(price, volume, 51)
                    monitor.position -= volume
                    monitor.in_position = monitor.position > 0
                    if contract in ContextInfo.account_manager.positions:
                        if ContextInfo.account_manager.positions[contract]['Volume'] <= volume:
                            del ContextInfo.account_manager.positions[contract]
                            print(f"【{contract}】–>持仓全部平仓")
                        else:
                            ContextInfo.account_manager.positions[contract]['Volume'] -= volume
                            ContextInfo.account_manager.positions[contract]['PositionValue'] = (
                                ContextInfo.account_manager.positions[contract]['Volume'] * 
                                ContextInfo.account_manager.positions[contract]['OpenPrice'] * 10000
                            )
                            print(f"【{contract}】–>更新持仓量: {ContextInfo.account_manager.positions[contract]['Volume']}")
                    
                    if monitor.pending_close and monitor.position <= 0:
                        monitor.pending_close = False
                        monitor.total_cost = 0
                        monitor.total_volume = 0
                        monitor.entry_price = None
                        monitor.price_history = []
                        monitor.highest_price = 0.0
                        if hasattr(monitor, 'batches_to_close'):
                            print(f"【{contract}】–>清理 batches_to_close")
                            delattr(monitor, 'batches_to_close')
                        print(f"【{contract}】–>平仓完成，持仓清零")
                    # 修改：添加成交后持仓日志
                    print(f"【{contract}】–>成交后持仓同步: 当前持仓={monitor.position}")
    
    except Exception as e:
        print(f"成交回调异常: {str(e)}")
        traceback.print_exc()

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """委托异常回调"""
    try:
        print(f"委托错误: {errMsg}")
        order_id = getattr(orderArgs, 'm_strOrderSysID', '')
        if order_id and hasattr(ContextInfo, 'monitors'):
            for monitor in ContextInfo.monitors.values():
                if order_id in monitor.active_orders:
                    print(f"【{monitor.contract_code}】–>移除被拒订单–>ID: {order_id}")
                    is_close = monitor.active_orders[order_id]['is_close']
                    del monitor.active_orders[order_id]
                    if is_close:
                        print(f"【{monitor.contract_code}】–>平仓订单被拒，重新尝试平仓")
                        monitor.exit_position("订单被拒触发平仓")
                    break
    except Exception as e:
        print(f"订单错误回调异常: {str(e)}")

# 4. 合约监控类
class ContractMonitor:
    def __init__(self, C, contract_code, account_manager, max_position=30):
        """初始化合约监控器"""
        # 基础参数
        self.C = C
        self.contract_code = contract_code if contract_code.endswith('.SHO') else f"{contract_code}.SHO"
        self.account_manager = account_manager
        
        # 从 XML 加载参数，包括 max_position
        self.max_position = max_position  # 初始默认值，将被 load_parameters 覆盖
        #self.load_parameters()  # 调用加载参数方法以覆盖 max_position
        
        # 新参数
        self.stop_loss = 0.03
        self.drawdown = 0.03
        self.trend_sensitivity = 3
        self.breakout_confirm = 1
        self.reversal_threshold = 0.05
        self.monitor_periods = 3
        self.commission_per_lot = 1.7
        self.trend_stop_enabled = 1
        self.trend_offset = 0  # 下跌趋势参数偏移量，默认为 0
        
        # 价格相关
        self.current_price = 0.0  # 明确初始化为 0.0
        self.current_tick = None
        self.price_history = []
        self.initial_price = None
        self.daily_high = None
        self.daily_low = None
        self._price_initialized = False
        self.price_trend_points = []
        self.last_price_direction = None
        self.trend_direction = None
        
        # 持仓相关
        self.position = 0
        self.entry_price = 0.0
        self.original_entry_price = 0.0  # 明确初始化为 0.0
        self.entry_time = None
        self.in_position = False
        self.is_legacy_position = False
        self.highest_price = 0.0  # 明确初始化为 0.0
        self.lowest_price = 0.0   # 明确初始化为 0.0
        self.highest_since_entry = None
        self.lowest_since_entry = None
            
        # 订单相关
        self.active_orders = {}
        self.filled_orders = {}
        self.last_order_id = None
        self.last_order_check_time = time.time()
        self.order_timeout = 5  # 修改：超时时间从 10 秒改为 5 秒
        
        # 交易状态
        self.pending_close = False
        self.close_reason = None
        self.trade_history = []
        
        # 趋势和动量指标（保留但不直接使用）
        self.trend = 0.0
        self.momentum = []
        self.volatility = 0.0
        
        # 添加已订阅合约集合（类变量）
        if not hasattr(ContractMonitor, '_subscribed_contracts'):
            ContractMonitor._subscribed_contracts = set()
        
        # 修改：初始化 effective_ticks
        self.effective_ticks = []  # 确保 calculate_trend 不会因未初始化而出错

    # 修改后的趋势计算逻辑，仅在连续上涨或下跌时返回明确方向
    def calculate_trend(self):
        """根据连续和震荡趋势计算趋势方向"""
        if not hasattr(self, 'oscillation_ended'):
            self.oscillation_ended = False
        if not hasattr(self, 'oscillation_active'):
            self.oscillation_active = False
        if not hasattr(self, 'period_trends'):
            self.period_trends = []
        if not hasattr(self, 'oscillation_start_id'):
            self.oscillation_start_id = 0
        if not hasattr(self, 'last_oscillation_log_time'):
            self.last_oscillation_log_time = 0
        if not hasattr(self, 'last_trend'):
            self.last_trend = None
        if not hasattr(self, 'last_trend_sequence'):
            self.last_trend_sequence = None
            self.current_price = None

        current_time = time.time()
        if self.oscillation_ended and not self.oscillation_active:
            return None, "震荡监测已结束"

        prices = [p['price'] for p in self.effective_ticks]
        times = [p['time'] for p in self.effective_ticks]
        ids = [p['id'] for p in self.effective_ticks]

        def get_valid_changes(price_list):
            changes = []
            for i in range(1, len(price_list)):
                if price_list[i] > price_list[i-1]:
                    changes.append('up')
                elif price_list[i] < price_list[i-1]:
                    changes.append('down')
            return changes

        effective_prices = prices
        required_changes_up = self.trend_sensitivity + self.breakout_confirm
        required_changes_down = max(1, self.trend_sensitivity + self.breakout_confirm + self.trend_offset)
        min_ticks = max(required_changes_up, required_changes_down) + 1
        continuous_result = None
        if len(effective_prices) >= min_ticks:
            recent_prices = effective_prices[-min_ticks:]
            recent_ids = ids[-min_ticks:]
            changes = get_valid_changes(recent_prices)
            
            reset_triggered = False
            reset_idx = -1
            for i in range(1, len(changes)):
                if changes[i] != changes[i-1]:
                    reset_triggered = True
                    reset_idx = i + 1
                    break

            if not reset_triggered:
                all_positive = all(c == 'up' for c in changes[-required_changes_up:])
                all_negative = all(c == 'down' for c in changes[-required_changes_down:])
                if all_positive:
                    trend_sequence = [f'{p:.4f}' for p in recent_prices[-required_changes_up-1:]]
                    if self.last_trend != "up" or self.last_trend_sequence != trend_sequence:
                        print(f"[{self.contract_code}]-->趋势触发: 连续上涨 {required_changes_up} 次, 序列: {trend_sequence}")
                        self.last_trend = "up"
                        self.last_trend_sequence = trend_sequence
                    continuous_result = ("up", "continuous")
                    if self.oscillation_active:
                        self.oscillation_active = False
                        self.oscillation_start_id = 0
                        self.period_trends.clear()
                        self.effective_ticks = self.effective_ticks[-min_ticks:]
                        print(f"[{self.contract_code}]-->连续tick触发趋势，震荡监测结束，重置tick")
                elif all_negative:
                    trend_sequence = [f'{p:.4f}' for p in recent_prices[-required_changes_down-1:]]
                    if self.last_trend != "down" or self.last_trend_sequence != trend_sequence:
                        print(f"[{self.contract_code}]-->趋势触发: 连续下跌 {required_changes_down} 次, 序列: {trend_sequence}")
                        self.last_trend = "down"
                        self.last_trend_sequence = trend_sequence
                    continuous_result = ("down", "continuous")
                    if self.oscillation_active:
                        self.oscillation_active = False
                        self.oscillation_start_id = 0
                        self.period_trends.clear()
                        self.effective_ticks = self.effective_ticks[-min_ticks:]
                        print(f"[{self.contract_code}]-->连续tick触发趋势，震荡监测结束，重置tick")

            if reset_triggered and not self.oscillation_active:
                self.oscillation_active = True
                self.oscillation_start_id = recent_ids[reset_idx]
                print(f"[{self.contract_code}]-->进入震荡监测，起点tick ID: {self.oscillation_start_id}, 价格: {recent_prices[reset_idx]:.4f}")

        oscillation_result = None
        if self.oscillation_active:
            oscillation_ticks = [t for t in self.effective_ticks if t['id'] >= self.oscillation_start_id]
            oscillation_prices = [t['price'] for t in oscillation_ticks]
            current_idx = 0
            period_trends = self.period_trends
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
            log_line = f"[{timestamp}][期权策略][SH510300][1分钟] [{self.contract_code}]"
            cycle_count = 0

            if len(oscillation_prices) < min_ticks:
                return None, "等待更多震荡tick数据以形成完整周期"

            while current_idx < len(oscillation_prices) and len(period_trends) < max(self.monitor_periods, self.monitor_periods + self.trend_offset):
                if len(oscillation_prices[current_idx:]) < min_ticks:
                    return None, "等待更多震荡tick数据以形成完整周期"
                period_prices = oscillation_prices[current_idx:current_idx + min_ticks]
                period_changes = get_valid_changes(period_prices)
                
                cycle_count += 1
                start_price = period_prices[0]
                end_price = period_prices[-1]
                if end_price > start_price:
                    period_direction = "up"
                elif end_price < start_price:
                    period_direction = "down"
                else:
                    period_direction = "震荡"
                
                cycle_info = f"-->震荡周期 {cycle_count}: 方向 {period_direction}, 序列: {[f'{p:.4f}' for p in period_prices]}"
                log_line += cycle_info
                
                if period_direction == "震荡":
                    log_line += f" -->结束震荡监测：周期 {cycle_count} 为震荡"
                    if current_time - self.last_oscillation_log_time >= 60:
                        print(log_line)
                        self.last_oscillation_log_time = current_time
                    period_trends.clear()
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_start_id = 0
                    break
                
                if period_trends and period_trends[-1] != period_direction:
                    log_line += f" -->结束震荡监测：周期 {cycle_count} 方向改变 ({period_trends[-1]} -> {period_direction})"
                    if current_time - self.last_oscillation_log_time >= 60:
                        print(log_line)
                        self.last_oscillation_log_time = current_time
                    period_trends.clear()
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_start_id = 0
                    break
                
                period_trends.append(period_direction.lower())
                current_idx += min_ticks
                
                if len(period_trends) >= self.monitor_periods and all(t == "up" for t in period_trends[-self.monitor_periods:]):
                    log_line += f" -->趋势触发: 震荡上涨 {self.monitor_periods}周期"
                    if current_time - self.last_oscillation_log_time >= 60:
                        print(log_line)
                        self.last_oscillation_log_time = current_time
                    oscillation_result = ("up", "oscillation")
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_start_id = 0
                    self.effective_ticks = self.effective_ticks[-current_idx:]
                    print(f"[{self.contract_code}]-->震荡监测触发趋势，连续tick监测重置")
                    break
                elif len(period_trends) >= max(1, self.monitor_periods + self.trend_offset) and all(t == "down" for t in period_trends[-max(1, self.monitor_periods + self.trend_offset):]):
                    log_line += f" -->趋势触发: 震荡下跌 {max(1, self.monitor_periods + self.trend_offset)}周期"
                    if current_time - self.last_oscillation_log_time >= 60:
                        print(log_line)
                        self.last_oscillation_log_time = current_time
                    oscillation_result = ("down", "oscillation")
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_start_id = 0
                    self.effective_ticks = self.effective_ticks[-current_idx:]
                    print(f"[{self.contract_code}]-->震荡监测触发趋势，连续tick监测重置")
                    break

        if continuous_result:
            return continuous_result
        if oscillation_result:
            return oscillation_result
        return None, "等待更多有效tick变化"

    def update_order_status(self, order_info):
        try:
            current_time = time.time()
            
            if isinstance(order_info, str):
                order_id = order_info
                order_info = get_value_by_order_id(
                    order_id,
                    self.account_manager.account_id,
                    'STOCK_OPTION',
                    'ORDER'
                )
                if not order_info:
                    print(f"未找到订单 {order_id} 的信息")
                    if order_id in self.active_orders:
                        del self.active_orders[order_id]
                    return
            
            order_id = getattr(order_info, 'm_strOrderSysID', '')
            contract = getattr(order_info, 'm_strInstrumentID', '')
            order_status = getattr(order_info, 'm_nOrderStatus', -1)
            direction = getattr(order_info, 'm_nOffsetFlag', 0)
            volume = getattr(order_info, 'm_nVolumeTotalOriginal', 0)
            traded = getattr(order_info, 'm_nVolumeTraded', 0)
            price = float(getattr(order_info, 'm_dLimitPrice', 0.0))
            
            if order_id not in self.active_orders:
                return
                
            self.account_manager.update(self.C)
            
            order_info_dict = self.active_orders[order_id]
            is_close_order = order_info_dict.get('is_close', False)
            timeout = 5  # 修改：超时时间从 30 秒改为 5 秒
            
            # 使用完整状态枚举
            status_desc = {49: '待报', 50: '已报', 51: '已报待撤', 52: '部成待撤', 
                        53: '部撤', 54: '已撤', 55: '部成', 56: '已成', 57: '废单'}
            print(f"【{self.contract_code}】–>订单状态更新–>ID: {order_id}, 方向: {'买入开仓' if direction == 50 else '卖出平仓'}")
            print(f"数量: {traded}/{volume}, 价格: {price}, 状态: {order_status} ({status_desc.get(order_status, '未知')})")
            
            if order_status in [49, 50, 51]:  # 未成交状态
                pass  # 等待成交
            elif order_status in [54, 57]:  # 已撤或废单
                print(f"【{self.contract_code}】–>订单已撤或废单–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print(f"【{self.contract_code}】–>平仓订单已撤–>立即重新提交")
                    time.sleep(0.5)  # 短暂延迟，避免过快重试
                    self.exit_position("订单状态更新触发平仓")
            elif order_status in [55, 56]:  # 部分或全部成交
                self.active_orders[order_id]['traded'] = traded
                if traded == volume:
                    print(f"【{self.contract_code}】–>订单全部成交–>ID: {order_id}")
                    del self.active_orders[order_id]
                    # 修改：同步持仓
                    self.account_manager.update(self.C)
                    positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                    for pos in positions:
                        if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract:
                            self.position = getattr(pos, 'm_nVolume', 0)
                            self.in_position = self.position > 0
                            break
            else:
                print(f"【{self.contract_code}】–>未知订单状态–>ID: {order_id}, 状态: {order_status}")
            
            order_time = order_info_dict['time']
            if current_time - order_time > timeout:
                print(f"【{self.contract_code}】–>订单超时–>ID: {order_id}, 平仓单: {is_close_order}")
                if self.cancel_order(self.C, order_id):
                    print(f"【{self.contract_code}】–>订单超时撤单成功–>ID: {order_id}")
                    del self.active_orders[order_id]
                    if is_close_order:
                        print(f"【{self.contract_code}】–>平仓订单超时–>立即重新提交")
                        time.sleep(0.5)  # 短暂延迟，避免过快重试
                        self.exit_position("订单超时触发平仓")
                else:
                    print(f"【{self.contract_code}】–>订单超时撤单失败–>ID: {order_id}")
                    # 检查最新状态并移除无效订单
                    orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                    order_found = False
                    if orders and isinstance(orders, list):
                        for order in orders:
                            if getattr(order, 'm_strOrderSysID', '') == order_id:
                                status = int(getattr(order, 'm_nOrderStatus', -1))
                                if status not in [49, 50, 51, 52, 55]:  # 非活动状态
                                    print(f"【{self.contract_code}】–>订单已非活动状态，移除–>ID: {order_id}, 状态: {status}")
                                    del self.active_orders[order_id]
                                order_found = True
                                break
                    if not order_found and order_id in self.active_orders:
                        print(f"【{self.contract_code}】–>订单未在最新数据中找到，移除–>ID: {order_id}")
                        del self.active_orders[order_id]
            else:
                remaining_time = timeout - (current_time - order_time)
                print(f"【{self.contract_code}】–>订单等待中–>剩余时间: {remaining_time:.1f}秒")
                if traded > 0:
                    print(f"【{self.contract_code}】–>订单部分成交–>{traded}/{volume}")
        except Exception as e:
            print(f"【{self.contract_code}】–>更新订单状态异常: {str(e)}")
            traceback.print_exc()

    def cancel_order(self, C, order_sys_id=None, contract_code=None):
        """撤销委托订单"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                print(f"【{self.contract_code}】–>无订单数据可撤销")
                return False
            
            active_states = [49, 50, 51, 52, 55]  # 可撤状态：待报、已报、已报待撤、部成待撤、部成
            orders_cancelled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                current_order_id = getattr(order, 'm_strOrderSysID', '')
                current_contract = getattr(order, 'm_strInstrumentID', '')
                
                if order_sys_id and current_order_id != order_sys_id:
                    continue
                    
                if contract_code and current_contract != contract_code:
                    continue
                    
                if status in active_states:
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    
                    if volume_total > volume_traded:
                        result = cancel(
                            current_order_id,
                            self.account_manager.account_id,
                            'STOCK_OPTION',
                            C
                        )
                        
                        if result:
                            print(f"【{self.contract_code}】–>撤单成功–>ID: {current_order_id}")
                            orders_cancelled = True
                            if current_order_id in self.active_orders:
                                del self.active_orders[current_order_id]
                        else:
                            print(f"【{self.contract_code}】–>撤单失败–>ID: {current_order_id}")
                            # 检查最新状态并移除无效订单
                            if status not in active_states or volume_total <= volume_traded:
                                if current_order_id in self.active_orders:
                                    print(f"【{self.contract_code}】–>订单已非活动状态，移除–>ID: {current_order_id}")
                                    del self.active_orders[current_order_id]
                                
            return orders_cancelled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤单异常: {str(e)}")
            traceback.print_exc()
            return False

    def load_parameters(self):
        """从XML文件加载策略参数"""
        try:
            xml_path = r"C:\国金QMT交易端模拟\python\formulaLayout\新期权策略.xml"
            print(f"[{self.contract_code}]-->加载策略参数-->路径: {xml_path}")
            
            if not os.path.exists(xml_path):
                raise FileNotFoundError(f"未找到配置文件: {xml_path}")
            
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            updated_params = []
            for item in root.findall('.//item'):
                bind = item.get('bind')
                value = item.get('value')
                
                if not bind or not value:
                    continue
                    
                try:
                    if bind == 'max_position':
                        self.max_position = int(value)
                        updated_params.append(('最大持仓', self.max_position))
                    elif bind == 'stop_loss':
                        self.stop_loss = float(value)
                        updated_params.append(('止损比例', self.stop_loss))
                    elif bind == 'drawdown':
                        self.drawdown = float(value)
                        updated_params.append(('回撤比例', self.drawdown))
                    elif bind == 'trend_sensitivity':
                        self.trend_sensitivity = int(value)
                        updated_params.append(('趋势敏感度', self.trend_sensitivity))
                    elif bind == 'breakout_confirm':
                        self.breakout_confirm = int(value)
                        updated_params.append(('突破确认', self.breakout_confirm))
                    elif bind == 'reversal_threshold':
                        self.reversal_threshold = float(value)
                        updated_params.append(('反转阈值', self.reversal_threshold))
                    elif bind == 'monitor_periods':
                        self.monitor_periods = int(value)
                        updated_params.append(('监测周期数', self.monitor_periods))
                    elif bind == 'commission_per_lot':
                        self.commission_per_lot = float(value)
                        updated_params.append(('每张手续费', self.commission_per_lot))
                    elif bind == 'trend_stop_enabled':
                        self.trend_stop_enabled = int(value)
                        updated_params.append(('趋势止损开关', self.trend_stop_enabled))
                    elif bind == 'trend_offset':
                        self.trend_offset = int(value)
                        updated_params.append(('下跌趋势偏移量', self.trend_offset))
                    elif bind == 'undl_code':
                        self.undl_code = value
                        updated_params.append(('标的代码', self.undl_code))
                    elif bind == 'account_id':
                        self.account_id = value
                        updated_params.append(('账户ID', self.account_id))
                    else:
                        print(f"[{self.contract_code}]-->未知参数: {bind} (值: {value})")
                        continue
                    
                except ValueError as e:
                    print(f"[{self.contract_code}]-->参数[{bind}]格式错误: {e}")
                    continue
            
            if updated_params:
                print(f"[{self.contract_code}]-->成功更新参数:")
                for param_name, param_value in updated_params:
                    print(f"{param_name}: {param_value}")
            else:
                print(f"[{self.contract_code}]-->警告: 未能更新任何参数")
                
        except Exception as e:
            print(f"[{self.contract_code}]-->加载参数异常: {e}")
            print(f"[{self.contract_code}]-->使用默认参数")

    def init_position(self, contract_code, volume, open_price, open_time=None):
        """初始化持仓，复用 update_price 的结果"""
        if not self.update_price():
            print(f"【{self.contract_code}】–>无法获取实时tick价格，初始化失败")
            return False
        print(f"【{self.contract_code}】–>获取实时tick价格成功: {self.current_price:.4f}")
        self.entry_price = self.current_price
        self.original_entry_price = self.current_price
        self._entry_price_fixed = True
        self.position = volume
        self.in_position = volume > 0
        self.highest_price = self.current_price
        self.lowest_price = self.current_price
        self.total_cost = self.entry_price * volume * 10000
        self.total_volume = volume
        self.entry_batches = [{'price': self.entry_price, 'volume': volume}]
        if self.account_manager and self.contract_code.replace('.SHO', '') in self.account_manager.positions:
            self.account_manager.positions[self.contract_code.replace('.SHO', '')]['OpenPrice'] = self.entry_price
            self.account_manager.positions[self.contract_code.replace('.SHO', '')]['CustomOpenPrice'] = self.entry_price
            print(f"【{self.contract_code}】–>同步开仓价到account_manager: {self.entry_price:.4f}")
        print(f"【{self.contract_code}】–>初始化总成本: {self.total_cost:.2f}, 总持仓量: {self.total_volume}")
        print(f"【{self.contract_code}】–>初始化入场批次: 价格={self.entry_price:.4f}, 数量={volume}")
        # 修改：添加超限检查
        if self.position > self.max_position:
            print(f"【{self.contract_code}】–>警告: 初始化持仓 {self.position} 超过最大限制 {self.max_position}")
        return True

    def update_price(self):
        try:
            current_time = time.time()
            self._last_update_time = current_time
            
            tick_data = self.C.get_full_tick([self.contract_code])
            if not isinstance(tick_data, dict) or self.contract_code not in tick_data:
                print(f"【{self.contract_code}】–>实时tick数据错误或无该合约")
                return False
            
            data = tick_data[self.contract_code]
            if not isinstance(data, dict):
                print(f"【{self.contract_code}】–>实时tick数据内容格式错误: {type(data)}")
                return False
            
            price = data.get('lastPrice', 0.0)
            if price <= 0:
                bid_price = data.get('bidPrice', [0])[0]
                ask_price = data.get('askPrice', [0])[0]
                price = (bid_price + ask_price) / 2 if bid_price > 0 and ask_price > 0 else data.get('lastClose', 0.0)
                if price <= 0:
                    print(f"【{self.contract_code}】–>无法获取有效实时价格")
                    return False
            
            price = round(price, 4)
            if self.current_price != price:
                self.current_price = price
                current_tick_time = data.get('timetag', '')
                self.price_history.append({'price': price, 'time': current_tick_time})
                self.price_history.sort(key=lambda x: x['time'])
                if len(self.price_history) > 20:
                    self.price_history.pop(0)
                
                if not hasattr(self, 'effective_ticks'):
                    self.effective_ticks = []
                if not self.effective_ticks or (self.effective_ticks[-1]['price'] != price and current_tick_time > self.effective_ticks[-1]['time']):
                    self.effective_ticks.append({
                        'price': price,
                        'time': current_tick_time,
                        'id': len(self.effective_ticks) + 1
                    })
                    
                if self.in_position and hasattr(self, 'effective_ticks'):
                    self.highest_price = max([tick['price'] for tick in self.effective_ticks])
            
            # 其他逻辑保持不变
            return True
        except Exception as e:
            print(f"【{self.contract_code}】–>更新价格异常: {str(e)}")
            return False

    # 修改：仅在连续下跌趋势中触发趋势止损
    def check_exit_conditions(self, check_time=True):
        """检查是否满足出场条件，统一基于每批次开仓价计算止损/止盈"""
        if not self.in_position or (check_time and not is_trade_time()) or not self.update_price():
            print(f"【{self.contract_code}】–>未持仓或非交易时间或更新价格失败，跳过平仓检查")
            return False
                                                    
        # 修改：检查未完成订单
        if self.active_orders:
            for order_id, info in list(self.active_orders.items()):
                if info['direction'] == 51:  # 平仓订单（SELL 改为 51，与方向一致）
                    print(f"【{self.contract_code}】–>存在未完成平仓订单，撤销并重新提交: ID={order_id}")
                    if self.cancel_order(self.C, order_id):
                        del self.active_orders[order_id]
                    else:
                        # 修改：再次检查订单状态
                        orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                        order_found = False
                        for order in orders:
                            if getattr(order, 'm_strOrderSysID', '') == order_id:
                                status = getattr(order, 'm_nOrderStatus', -1)
                                if status not in [0, 1, 3, 50, 51]:
                                    print(f"【{self.contract_code}】–>订单已结束，移除: ID={order_id}")
                                    del self.active_orders[order_id]
                                order_found = True
                                break
                        if not order_found:
                            print(f"【{self.contract_code}】–>订单不存在，移除: ID={order_id}")
                            del self.active_orders[order_id]
                        else:
                            print(f"【{self.contract_code}】–>撤销失败，暂不平仓")
                            return False
                else:
                    print(f"【{self.contract_code}】–>存在未完成开仓订单，继续平仓")

        # 检查每批次持仓是否满足平仓条件
        if not hasattr(self, 'entry_batches') or not self.entry_batches:
            print(f"【{self.contract_code}】–>无入场批次记录，尝试同步持仓")
            self.account_manager.update(self.C)
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            contract_key = self.contract_code.replace('.SHO', '')
            for pos in positions:
                if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract_key:
                    self.position = getattr(pos, 'm_nVolume', 0)
                    self.in_position = self.position > 0
                    if self.in_position:
                        open_price = getattr(pos, 'm_dOpenPrice', self.current_price if self.update_price() else 0.0)
                        self.entry_price = open_price
                        self.entry_batches = [{'price': self.entry_price, 'volume': self.position}]
                        print(f"【{self.contract_code}】–>强制同步持仓: 数量={self.position}, 开仓价={self.entry_price:.4f}")
                    break
            if not self.in_position or not self.entry_batches:
                print(f"【{self.contract_code}】–>仍无有效持仓或批次信息，跳过平仓")
                return False

        batches_to_close = []
        trend = None
        # 初始化日志控制变量
        if not hasattr(self, '_last_trend_logged'):
            self._last_trend_logged = None
        if not hasattr(self, '_last_trend_log_time'):
            self._last_trend_log_time = 0
        current_time = time.time()
        log_interval = 60  # 每60秒最多记录一次相同的趋势状态

        # 修改：趋势开关逻辑与日志优化
        if self.trend_stop_enabled:
            trend, trend_type = self.calculate_trend()
            if trend != "down":
                return False
            else:
                # 仅在趋势变为下跌时输出一次，或满足时间间隔
                if trend != self._last_trend_logged or current_time - self._last_trend_log_time >= log_interval:
                    print(f"【{self.contract_code}】–>趋势开关开，检测到下跌趋势，开始检查批次")
                    self._last_trend_logged = trend
                    self._last_trend_log_time = current_time
        
        for batch in self.entry_batches:
            if batch['volume'] <= 0:
                continue
            batch_price = batch['price']
            current_price = self.current_price if self.current_price is not None else 0.0
            
            # 计算批次的盈亏比例（止损）和回撤（止盈）
            profit_ratio = (current_price - batch_price) / batch_price if batch_price > 0 else 0
            batch_drawdown = (self.highest_price - current_price) / self.highest_price if self.highest_price > 0 else 0
            
            # 平仓条件
            should_close = False
            if profit_ratio <= -self.stop_loss:
                self.close_reason = f"批次止损 {profit_ratio*100:.2f}% (价格={batch_price:.4f})OldHighestPrice:{self.highest_price}"
                should_close = True
            elif batch_drawdown >= self.drawdown:
                self.close_reason = f"批次回撤 {batch_drawdown*100:.2f}% (价格={batch_price:.4f})OldHighestPrice:{self.highest_price}"
                should_close = True
            
            if should_close:
                batches_to_close.append(batch)
                # 添加日志频率控制
                if not hasattr(self, '_last_exit_log_time'):
                    self._last_exit_log_time = 0
                if current_time - self._last_exit_log_time >= log_interval:
                    print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价={current_price:.4f}, 数量={batch['volume']}")
                    self._last_exit_log_time = current_time

        if batches_to_close:
            self.batches_to_close = batches_to_close
            return True
                                            
        return False

    def enter_position(self, C):
        """执行入场操作（买入开仓），调用 execute_order"""
        try:
            if C is None:
                print(f"【{self.contract_code}】–>交易客户端未初始化，无法入场")
                return False

            if not self.update_price():
                print(f"【{self.contract_code}】–>无法获取当前行情数据")
                return False
            self.current_tick = self.C.get_full_tick([self.contract_code]).get(self.contract_code)

            if not isinstance(self.current_tick, dict):
                print(f"【{self.contract_code}】–>当前 tick 数据无效: {type(self.current_tick)}")
                return False

            # 修改：实时同步持仓和未完成订单
            self.account_manager.update(C)  # 强制更新
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            self.position = 0
            if positions and isinstance(positions, list):
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        break

            pending_volume = 0
            if self.active_orders:
                current_time = time.time()
                for order_id, info in list(self.active_orders.items()):
                    if info['direction'] == 50:  # 开仓订单
                        pending_volume += info['volume'] - info['traded']
                        if current_time - info['time'] > self.order_timeout:
                            print(f"【{self.contract_code}】–>入场前清理超时开仓订单–>ID: {order_id}")
                            if self.cancel_order(self.C, order_id):
                                print(f"【{self.contract_code}】–>超时开仓订单撤单成功–>ID: {order_id}")
                                del self.active_orders[order_id]
                                pending_volume -= (info['volume'] - info['traded'])
                            else:
                                print(f"【{self.contract_code}】–>超时开仓订单撤单失败–>ID: {order_id}")
            
            if self.position + pending_volume >= self.max_position:
                print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position} (当前: {self.position}, 未完成: {pending_volume})")
                return False

            if self.contract_code not in ContractMonitor._subscribed_contracts:
                print(f"【{self.contract_code}】–>订阅合约")
                self.C.subscribe_quote(self.contract_code, "1", "0")
                ContractMonitor._subscribed_contracts.add(self.contract_code)

            bid_price = self.current_tick.get('bidPrice', [0])[0]
            ask_price = self.current_tick.get('askPrice', [0])[0]
            ask_vol = self.current_tick.get('askVol', [0])[0]
            if not bid_price or not ask_price or not ask_vol:
                print(f"【{self.contract_code}】–>无法获取有效买卖价格或卖一量")
                return False

            available_fund = self.account_manager.account_info.get('m_dAvailable', 0)
            max_by_fund = int(available_fund / (ask_price * 10000))
            remaining_capacity = self.max_position - self.position - pending_volume
            order_volume = min(ask_vol, remaining_capacity, max_by_fund, 6)
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用入场数量 (剩余容量: {remaining_capacity})")
                return False

            print(f"【{self.contract_code}】–>触发入场（买入开仓）–>参考价格: {ask_price:.4f}, 数量: {order_volume}")

            order_remark = execute_order(
                self.contract_code, 'BUY', order_volume, 14, -1,
                self.account_manager.account_id, C, self.account_manager.account_info,
                is_open=True, current_tick=self.current_tick
            )

            if order_remark:
                print(f"【{self.contract_code}】–>入场订单提交成功–>备注: {order_remark}")
                self.last_order_id = order_remark
                self.last_order_check_time = time.time()
                self.active_orders[order_remark] = {
                    'time': time.time(),
                    'is_close': False,
                    'price': ask_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 50,
                    'contract': self.contract_code
                }
                self._last_entry_time = time.time()
                return True
            print(f"【{self.contract_code}】–>入场订单提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行入场操作异常: {str(e)}")
            traceback.print_exc()
            return False

    def exit_position(self, reason):
        print(f"【{self.contract_code}】–>触发平仓: {reason}, 当前价: {self.current_price:.4f}")
        
        try:
            if not hasattr(self, 'C') or self.C is None:
                raise AttributeError(f"【{self.contract_code}】–>上下文对象 'self.C' 未定义或为空")
            
            # 跳过 check_exit_conditions 检查（强制平仓场景）
            if reason != "收盘前强制平仓" and not self.check_exit_conditions():
                print(f"【{self.contract_code}】–>未满足平仓条件，跳过平仓")
                return False
            
            # 初始化重试计数器
            if not hasattr(self, '_exit_attempts'):
                self._exit_attempts = 0
            max_attempts = 3
            
            # 清理超时订单
            current_time = time.time()
            for order_id, info in list(self.active_orders.items()):
                if info['direction'] == 51 and current_time - info['time'] > self.order_timeout:
                    if self.cancel_order(self.C, order_id):
                        print(f"【{self.contract_code}】–>超时平仓订单已撤销: {order_id}")
                    else:
                        print(f"【{self.contract_code}】–>超时平仓订单撤销失败: {order_id}")
            
            while self._exit_attempts < max_attempts:
                # 同步持仓
                self.account_manager.update(self.C)
                contract_key = self.contract_code.replace('.SHO', '')
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                total_volume = 0
                available_volume = 0
                frozen_volume = 0
                for pos in positions:
                    if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract_key:
                        total_volume = getattr(pos, 'm_nVolume', 0)
                        frozen_volume = getattr(pos, 'm_nFrozenVolume', 0)
                        available_volume = total_volume - frozen_volume
                        self.position = total_volume
                        self.in_position = total_volume > 0
                        break
                else:
                    print(f"【{self.contract_code}】–>无持仓记录，重置状态")
                    self.position = 0
                    self.in_position = False
                    self.total_cost = 0
                    self.total_volume = 0
                    self.entry_price = None
                    self.price_history = []
                    self.highest_price = 0.0
                    if hasattr(self, 'batches_to_close'):
                        print(f"【{self.contract_code}】–>清理 batches_to_close")
                        delattr(self, 'batches_to_close')
                    if self.is_legacy_position:
                        self.is_legacy_position = False
                        print(f"【{self.contract_code}】–>重置遗留持仓标记")
                    self._exit_attempts = 0
                    return True
                
                if total_volume <= 0:
                    print(f"【{self.contract_code}】–>持仓已清零，无需平仓")
                    self.in_position = False
                    self._exit_attempts = 0
                    return True
                
                print(f"【{self.contract_code}】–>持仓状态: 总持仓={total_volume}, 冻结={frozen_volume}, 可用={available_volume}")
                
                # 检查未完成订单
                orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                pending_close_volume = 0
                active_order_ids = []
                for order in orders:
                    if (hasattr(order, 'm_strInstrumentID') and 
                        order.m_strInstrumentID == contract_key and 
                        getattr(order, 'm_nOffsetFlag', 0) == 51):
                        status = getattr(order, 'm_nOrderStatus', -1)
                        order_id = getattr(order, 'm_strOrderSysID', '')
                        if status in [49, 50, 51, 52, 55]:
                            volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                            volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                            pending_volume = volume_total - volume_traded
                            pending_close_volume += pending_volume
                            active_order_ids.append(order_id)
                            # 撤销超时的未完成订单
                            if order_id in self.active_orders and current_time - self.active_orders[order_id]['time'] > self.order_timeout:
                                if self.cancel_order(self.C, order_id):
                                    print(f"【{self.contract_code}】–>撤销超时未完成平仓订单: {order_id}")
                                    pending_close_volume -= pending_volume
                                else:
                                    print(f"【{self.contract_code}】–>撤销超时未完成平仓订单失败: {order_id}")
                
                if pending_close_volume > 0:
                    print(f"【{self.contract_code}】–>存在未完成平仓订单: {pending_close_volume}, 订单: {active_order_ids}")
                    if reason == "收盘前强制平仓":
                        print(f"【{self.contract_code}】–>强制平仓模式，撤销所有未完成平仓订单")
                        for order_id in active_order_ids:
                            if self.cancel_order(self.C, order_id):
                                print(f"【{self.contract_code}】–>撤销未完成平仓订单成功: {order_id}")
                                pending_close_volume = max(0, pending_close_volume - self.active_orders.get(order_id, {'volume': 0})['volume'])
                            else:
                                print(f"【{self.contract_code}】–>撤销未完成平仓订单失败: {order_id}")
                        if pending_close_volume > 0:
                            print(f"【{self.contract_code}】–>仍有未完成订单，暂停本次平仓")
                            return False
                
                if available_volume <= 0:
                    print(f"【{self.contract_code}】–>可用持仓为0，检查冻结状态")
                    if pending_close_volume == 0:
                        print(f"【{self.contract_code}】–>无未完成订单，强制使用总持仓量")
                        available_volume = total_volume
                    else:
                        print(f"【{self.contract_code}】–>等待未完成订单处理")
                        return False
                
                # 获取tick数据
                retries = 3
                bid_vol = 0
                bid_price = 0
                current_tick = None
                for attempt in range(retries):
                    try:
                        tick_data = self.C.get_full_tick([self.contract_code])
                        if tick_data and self.contract_code in tick_data:
                            current_tick = tick_data[self.contract_code]
                            bid_vol = current_tick.get('bidVol', [0])[0]
                            bid_price = current_tick.get('bidPrice', [0])[0]
                            if bid_price > 0 and bid_vol > 0:
                                self.current_tick = current_tick
                                break
                        print(f"【{self.contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                        time.sleep(0.5)
                    except Exception as e:
                        print(f"【{self.contract_code}】–>获取tick数据异常: {str(e)}")
                        time.sleep(0.5)
                else:
                    print(f"【{self.contract_code}】–>多次尝试后仍无法获取有效tick数据")
                    self._exit_attempts += 1
                    continue
                
                if bid_vol == 0:
                    print(f"【{self.contract_code}】–>无买一量，跳过本次平仓")
                    self._exit_attempts += 1
                    continue
                
                # 动态调整平仓量
                order_volume = total_volume if reason == "收盘前强制平仓" else min(available_volume, bid_vol)
                if bid_vol < order_volume:
                    order_volume = bid_vol
                    print(f"【{self.contract_code}】–>买一量不足，调整平仓量为: {order_volume}")
                
                if order_volume <= 0:
                    print(f"【{self.contract_code}】–>无可用平仓数量")
                    self._exit_attempts += 1
                    continue
                
                print(f"【{self.contract_code}】–>提交平仓订单 (尝试 {self._exit_attempts + 1}/{max_attempts}): 参考价格 {bid_price:.4f}, 数量 {order_volume}")
                
                order_remark = execute_order(
                    self.contract_code, 'SELL', order_volume, 14, -1,
                    self.account_manager.account_id, self.C, self.account_manager.account_info,
                    is_open=False, current_tick=self.current_tick
                )
                
                if order_remark:
                    print(f"【{self.contract_code}】–>平仓订单提交成功: 数量 {order_volume}, 订单备注: {order_remark}")
                    self.active_orders[order_remark] = {
                        'time': time.time(),
                        'is_close': True,
                        'price': bid_price,
                        'volume': order_volume,
                        'traded': 0,
                        'direction': 51,
                        'contract': self.contract_code
                    }
                    self.pending_close = True
                    self._exit_attempts = 0
                    return True
                else:
                    print(f"【{self.contract_code}】–>平仓订单提交失败")
                    self._exit_attempts += 1
                    time.sleep(1)  # 增加重试间隔
            
            print(f"【{self.contract_code}】–>平仓重试 {max_attempts} 次仍失败，暂停平仓")
            self._exit_attempts = 0
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行出场异常: {str(e)}")
            traceback.print_exc()
            return False

    def cancel_frozen_orders(self, C, is_open=False):
        """撤销冻结订单，支持入场和平仓"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            direction_flag = 50 if is_open else 51  # 50: 开仓, 51: 平仓
            active_states = [0, 1, 2, 3, 50, 51]  # 包括“已报待撤”(假设 2)
            canceled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus') or not hasattr(order, 'm_nOffsetFlag'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                direction = int(getattr(order, 'm_nOffsetFlag', 0))
                order_id = getattr(order, 'm_strOrderSysID', '')
                contract = getattr(order, 'm_strInstrumentID', '')
                
                if (contract == self.contract_code.replace('.SHO', '') and 
                    direction == direction_flag and status in active_states):
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    if volume_total > volume_traded:
                        result = cancel(order_id, self.account_manager.account_id, 'STOCK_OPTION', C)
                        if result:
                            print(f"【{self.contract_code}】–>撤销冻结订单成功–>ID: {order_id}, 状态: {status}")
                            canceled = True
                        else:
                            print(f"【{self.contract_code}】–>撤销冻结订单失败–>ID: {order_id}, 状态: {status}")
            
            return canceled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤销冻结订单异常: {str(e)}")
            traceback.print_exc()
            return False

    # 修改：仅在连续上涨趋势中触发入场
    def check_entry_conditions(self):
        try:
            if not is_trade_time():
                print(f"【{self.contract_code}】–>非交易时间，暂不入场")
                return False
            
            # 实时同步持仓
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            self.position = 0
            if positions and isinstance(positions, list):
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        break

            # 计算未完成订单的委托量
            pending_volume = 0
            if self.active_orders:
                current_time = time.time()
                for order_id, info in list(self.active_orders.items()):
                    if info['direction'] == 50:  # 开仓订单（50 表示买入开仓）
                        pending_volume += info['volume'] - info['traded']
                        # 添加：清理超时的开仓订单
                        if current_time - info['time'] > self.order_timeout:
                            print(f"【{self.contract_code}】–>检查入场时清理超时订单–>ID: {order_id}")
                            if self.cancel_order(self.C, order_id):
                                print(f"【{self.contract_code}】–>超时订单撤单成功–>ID: {order_id}")
                                del self.active_orders[order_id]
                                pending_volume -= (info['volume'] - info['traded'])
                            else:
                                print(f"【{self.contract_code}】–>超时订单撤单失败–>ID: {order_id}")
            
            # 检查持仓量 + 未成交委托是否超过最大持仓限制
            if self.position + pending_volume >= self.max_position:
                # 添加日志频率控制
                if not hasattr(self, '_last_max_position_log_time'):
                    self._last_max_position_log_time = 0
                current_time = time.time()
                if current_time - self._last_max_position_log_time >= 60:  # 每60秒打印一次
                    print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position} (当前: {self.position}, 未完成: {pending_volume})")
                    self._last_max_position_log_time = current_time
                return False
            else:
                # 重置日志标记，确保每次未超限时都能打印
                if hasattr(self, '_max_position_logged'):
                    delattr(self, '_max_position_logged')

            # 检查未成交订单，防止重复提交
            if self.active_orders and any(not info['traded'] for info in self.active_orders.values()):
                print(f"【{self.contract_code}】–>存在未成交订单，暂停入场")
                return False

            if not self.account_manager or not self.account_manager.account_info:
                print(f"【{self.contract_code}】–>等待账户信息初始化")
                return False
                    
            if len(self.price_history) < self.trend_sensitivity + self.breakout_confirm:
                return False
                    
            if not self.current_price:
                return False

            # 添加频率控制，防止短时间内重复触发（改为5秒冷却）
            if not hasattr(self, '_last_entry_time'):
                self._last_entry_time = 0
            current_time = time.time()
            if current_time - self._last_entry_time < 5:  # 修改为5秒冷却
                return False

            # 日志控制（趋势非上涨）
            if not hasattr(self, '_last_entry_trend_logged'):
                self._last_entry_trend_logged = None
            if not hasattr(self, '_last_entry_log_time'):
                self._last_entry_log_time = 0
            log_interval = 60

            trend, trend_type = self.calculate_trend()
            if trend == "up":
                print(f"【{self.contract_code}】–>触发入场: 趋势 {trend}, 类型 {trend_type}")
                self._last_entry_time = current_time  # 更新最后入场时间
                return self.enter_position(self.C)
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>检查入场条件异常: {str(e)}")
            traceback.print_exc()
            return False

    def on_tick(self):
        """处理tick数据，更新价格、持仓和订单状态，执行交易逻辑"""
        try:
            # 初始化价格历史数据
            if not hasattr(self, '_initialized'):
                self.price_history = []
                self.effective_ticks = []
                self._initialized = True
                print(f"【{self.contract_code}】–>初始化价格历史数据")

            current_trade_status = is_trade_time()
            current_time = time.time()
            current_datetime = datetime.now()

            # 收盘前10分钟（14:50）强制平仓或停止监测
            if not hasattr(self, '_last_cleanup_time'):
                self._last_cleanup_time = 0
            if current_trade_status and current_datetime.hour == 14 and current_datetime.minute >= 50:
                if current_time - self._last_cleanup_time < 10:  # 每10秒触发一次
                    return
                self._last_cleanup_time = current_time
                print(f"【{self.contract_code}】–>接近收盘（14:50），检查持仓并处理")
                
                # 强制同步持仓
                self.account_manager.update(self.C)
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                self.position = 0
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if (hasattr(pos, 'm_strInstrumentID') and 
                            pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                            self.position = getattr(pos, 'm_nVolume', 0)
                            self.in_position = self.position > 0
                            print(f"【{self.contract_code}】–>同步持仓: {self.position}")
                            break
                
                # 检查持仓并决定操作
                if self.position > 0:
                    print(f"【{self.contract_code}】–>检测到持仓，执行强制平仓")
                    self.exit_position("收盘前强制平仓")
                else:
                    print(f"【{self.contract_code}】–>无持仓，停止交易信号监测")
                return

            # 交易时间状态切换
            if not hasattr(self, '_last_trade_status'):
                self._last_trade_status = current_trade_status
            
            if current_trade_status != self._last_trade_status:
                print(f"【{self.contract_code}】–>{'进入' if current_trade_status else '退出'}交易时间")
                self._last_trade_status = current_trade_status

            if not current_trade_status:
                return

            self._last_tick_time = current_time
            
            # 检查行情订阅状态，若未订阅则尝试重新订阅
            if self.contract_code not in ContractMonitor._subscribed_contracts:
                print(f"【{self.contract_code}】–>合约未订阅，尝试重新订阅")  # 修复f-string
                self.C.subscribe_quote(self.contract_code, "1", "0")
                ContractMonitor._subscribed_contracts.add(self.contract_code)

            # 检查超时订单
            if self.active_orders:
                print(f"【{self.contract_code}】–>检查活动订单: {list(self.active_orders.keys())}")
                for order_id, info in list(self.active_orders.items()):
                    elapsed_time = current_time - info['time']
                    if elapsed_time > self.order_timeout:
                        print(f"【{self.contract_code}】–>检测到超时订单–>ID: {order_id}, 已等待 {elapsed_time:.1f}秒")
                        if self.cancel_order(self.C, order_id):
                            print(f"【{self.contract_code}】–>超时订单撤单成功–>ID: {order_id}")
                            del self.active_orders[order_id]
                            if info['is_close']:
                                print(f"【{self.contract_code}】–>平仓订单超时，重新提交平仓")
                                self.exit_position("超时订单触发平仓")
                        else:
                            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                            order_found = False
                            if orders and isinstance(orders, list):
                                print(f"【{self.contract_code}】–>查询订单数据: {len(orders)} 条")
                                for order in orders:
                                    if getattr(order, 'm_strOrderSysID', '') == order_id:
                                        status = int(getattr(order, 'm_nOrderStatus', -1))
                                        if status not in [49, 50, 51, 52, 55]:
                                            print(f"【{self.contract_code}】–>订单已非活动状态–>ID: {order_id}, 状态: {status}, 移除")
                                            del self.active_orders[order_id]
                                        order_found = True
                                        break
                                if not order_found and order_id in self.active_orders:
                                    print(f"【{self.contract_code}】–>订单未在最新数据中找到，移除–>ID: {order_id}")
                                    del self.active_orders[order_id]
                            else:
                                print(f"【{self.contract_code}】–>订单等待中–>ID: {order_id}, 剩余时间: {(self.order_timeout - elapsed_time):.1f}秒")
            
            # 更新价格
            if not self.update_price():
                return
            
            if hasattr(self, 'account_manager'):
                self.account_manager.update(self.C)
            
            # 初始化日志控制
            if not hasattr(self, '_last_timeout_log_time'):
                self._last_timeout_log_time = {}
            log_interval = 60

            # 实时同步持仓状态
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            pending_orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if positions and isinstance(positions, list):
                self.position = 0
                self.in_position = False
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        self.in_position = self.position > 0
                        if self.in_position and not hasattr(self, 'total_cost') and self.is_legacy_position:
                            multiplier = 10000
                            if self.update_price():
                                self.total_cost = self.current_price * self.position * multiplier
                                self.total_volume = self.position
                                if not hasattr(self, '_entry_price_fixed') or not self._entry_price_fixed:
                                    self.entry_price = self.current_price
                                    self.original_entry_price = self.current_price
                                    self._entry_price_fixed = True
                                    print(f"【{self.contract_code}】–>首次初始化遗留持仓: 开仓价固定为 {self.entry_price:.4f}")
                                self.entry_batches = [{'price': self.entry_price, 'volume': self.position}]
                                print(f"【{self.contract_code}】–>初始化持仓: 数量={self.total_volume}, 开仓价={self.entry_price:.4f}, 来源: 实时tick")
                            else:
                                print(f"【{self.contract_code}】–>无法获取实时tick价格，持仓初始化失败")
                if self.position == 0 and not self.pending_close:
                    if hasattr(self, 'batches_to_close'):
                        print(f"【{self.contract_code}】–>无持仓，清理 batches_to_close")
                        delattr(self, 'batches_to_close')
                    self.total_cost = 0
                    self.total_volume = 0
                    self.entry_price = None
                    if self.is_legacy_position:
                        self.is_legacy_position = False
                        print(f"【{self.contract_code}】–>持仓清零，重置遗留持仓标记")

            pending_volume = 0
            if pending_orders and isinstance(pending_orders, list):
                for order in pending_orders:
                    if (hasattr(order, 'm_strInstrumentID') and 
                        order.m_strInstrumentID == self.contract_code.replace('.SHO', '') and 
                        order.m_nOrderStatus in [50, 51]):
                        pending_volume += getattr(order, 'm_nVolumeTotal', 0) - getattr(order, 'm_nVolumeTraded', 0)

            if self.active_orders and any(not info['traded'] for info in self.active_orders.values()):
                print(f"【{self.contract_code}】–>存在未成交订单，暂停入场检查")
                return

            # 计算趋势
            trend, trend_type = self.calculate_trend()

            # 检查待平仓批次
            if hasattr(self, 'batches_to_close') and self.batches_to_close:
                if not hasattr(self, '_batches_to_close_time'):
                    self._batches_to_close_time = current_time
                elif current_time - self._batches_to_close_time > 30:
                    print(f"【{self.contract_code}】–>batches_to_close超时未平仓，重新尝试")
                    self.exit_position(self.close_reason)
                    self._batches_to_close_time = current_time

            # 检查平仓条件
            if self.in_position and self.check_exit_conditions():
                if self.exit_position(self.close_reason):
                    print(f"【{self.contract_code}】–>平仓订单已提交，等待成交")

            # 检查入场条件
            if not hasattr(self, '_last_limit_log_time'):
                self._last_limit_log_time = 0
            if self.position + pending_volume < self.max_position:
                self.check_entry_conditions()
            else:
                if current_time - self._last_limit_log_time >= log_interval:
                    if pending_volume > 0:
                        print(f"【{self.contract_code}】–>存在未完成订单，持仓量: {self.position}, 未完成: {pending_volume}")
                    elif self.position >= self.max_position:
                        print(f"【{self.contract_code}】–>持仓已满: {self.position}/{self.max_position}，继续监测入场条件")
                    self._last_limit_log_time = current_time
                return

        except Exception as e:
            print(f"【{self.contract_code}】–>on_tick异常: {str(e)}")
            traceback.print_exc()

    def update_entry_price(self, price, volume):
        if not hasattr(self, 'total_cost'):
            self.total_cost = 0
            self.total_volume = 0
            self.entry_batches = []
        multiplier = 10000
        if self.is_legacy_position and hasattr(self, '_entry_price_fixed') and self._entry_price_fixed:
            self.total_cost += price * volume * multiplier
            self.total_volume += volume
            self.entry_batches.append({'price': price, 'volume': volume})
            print(f"【{self.contract_code}】–>遗留持仓加仓: 新批次价格={price:.4f}, 数量={volume}, 原开仓价保持: {self.entry_price:.4f}")
        else:
            self.total_cost += price * volume * multiplier
            self.total_volume += volume
            self.entry_price = self.total_cost / (self.total_volume * multiplier) if self.total_volume > 0 else 0
            self.entry_batches.append({'price': price, 'volume': volume})
            # 仅在首次初始化时打印 entry_price
            if not hasattr(self, '_entry_price_logged') or not self._entry_price_logged:
                print(f"【{self.contract_code}】–>首次记录入场: 价格={price:.4f}, 数量={volume}, 初始平均开仓价={self.entry_price:.4f}")
                self._entry_price_logged = True
            else:
                print(f"【{self.contract_code}】–>记录入场批次: 价格={price:.4f}, 数量={volume}")

    def on_order_filled(self, price, volume, direction):
        if direction == 50:  # 开仓（买入）
            self.update_entry_price(price, volume)
            print(f"【{self.contract_code}】–>开仓成交: 价格={price:.4f}, 数量={volume}")
        elif direction == 51:  # 平仓（卖出）
            if hasattr(self, 'batches_to_close') and self.batches_to_close:
                remaining_volume = volume
                for batch in self.batches_to_close:
                    if remaining_volume <= 0:
                        break
                    if batch['volume'] > 0:
                        reduce_volume = min(remaining_volume, batch['volume'])
                        batch['volume'] -= reduce_volume
                        remaining_volume -= reduce_volume
                        for eb in self.entry_batches:
                            if eb['price'] == batch['price'] and eb['volume'] >= reduce_volume:
                                eb['volume'] -= reduce_volume
                                break
                        print(f"【{self.contract_code}】–>平仓批次更新: 价格={batch['price']:.4f}, 减少数量={reduce_volume}, 剩余={batch['volume']}")
            self.total_volume -= volume
            self.position -= volume  # 修改：同步 position
            self.in_position = self.position > 0  # 修改：更新 in_position
            multiplier = 10000
            self.total_cost = self.entry_price * self.total_volume * multiplier if self.total_volume > 0 else 0
            print(f"【{self.contract_code}】–>平仓成交: 价格={price:.4f}, 数量={volume}, 剩余持仓={self.total_volume}")

# 5. 主要策略函数
def call_back(data):
    """主策略回调函数"""
    try:
        # 处理订阅成功的初始事件（可选保留，仅打印日志）
        if isinstance(data, dict) and 'EventCode' in data and data['EventCode'] == 0:
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
            C = call_back.context
            print(f"订阅标的行情: {strategy_state.undl_code}")
            # 删除 select_options 和后续逻辑，因为已在 after_init 中实现
            return

        # 处理实时行情数据
        if isinstance(data, dict):
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
            C = call_back.context
            for code, tick_data in data.items():
                if code in C.monitors:
                    monitor = C.monitors[code]
                    monitor.on_tick()

    except Exception as e:
        print(f"策略异常: {str(e)}")
        traceback.print_exc()

def init(C):
    """初始化策略"""
    global strategy_state
    
    strategy_state = StrategyState(C)
    
    strategy_state.subID = 0
    strategy_state.undl_code = '510300.SH'
    strategy_state.monitors = {}
    C.monitors = strategy_state.monitors
    
    print(f"订阅标的行情: {strategy_state.undl_code}")
    C.subscribe_quote(strategy_state.undl_code, "1", "0")
    
    account_id = '*********'
    strategy_state.account_manager = AccountManager(account_id)
    
    C.set_account(account_id)
    
    call_back.context = C

def after_init(C):
    """初始化后执行的操作"""
    try:
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
            positions = get_trade_detail_data(strategy_state.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            position_contracts = set()
            
            if positions and isinstance(positions, list):
                for pos in positions:
                    if hasattr(pos, 'm_strInstrumentID'):
                        contract_code = pos.m_strInstrumentID + '.SHO'
                        pos_volume = getattr(pos, 'm_nVolume', 0)
                        if pos_volume > 0:
                            position_contracts.add(contract_code)
                            if contract_code not in strategy_state.monitors:
                                monitor = ContractMonitor(C, contract_code, strategy_state.account_manager)
                                strategy_state.monitors[contract_code] = monitor
                                entry_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                                monitor.position = pos_volume
                                monitor.in_position = True
                                monitor.is_legacy_position = True
                                monitor.load_parameters()
                                retries = 5
                                for attempt in range(retries):
                                    if monitor.update_price():
                                        monitor.init_position(contract_code, pos_volume, entry_price)
                                        print(f"【{contract_code}】–>初始化已有持仓–>持仓量: {pos_volume}, 开仓价: {monitor.entry_price:.4f}, 来源: 实时tick")
                                        break
                                    else:
                                        print(f"【{contract_code}】–>等待实时价格，尝试 {attempt + 1}/{retries}")
                                        time.sleep(1)
                                else:
                                    print(f"【{contract_code}】–>无法获取实时价格，初始化失败")
                                    return
        
        tick_data = C.get_full_tick([strategy_state.undl_code])
        if not tick_data or strategy_state.undl_code not in tick_data:
            print("无法获取市场数据")
            return
            
        data = tick_data[strategy_state.undl_code]
        if not isinstance(data, dict):
            print("市场数据格式错误")
            return
            
        timetag = data.get('timetag', '')
        if not timetag:
            print("无法获取时间标签")
            return
        current_date = timetag.split()[0].replace('-', '') if timetag else ''
        # 确保 current_date 是字符串
        if isinstance(current_date, (int, float)):
            current_date = str(int(current_date))
        if not current_date.isdigit() or len(current_date) != 8:
            print(f"无效的当前日期格式: {current_date}")
            return
            
        target_price = data.get('lastPrice', 0)
        if target_price <= 0:
            target_price = data.get('lastClose', 0)
            if target_price > 0:
                print("使用昨收盘价")
            else:
                print("无法获取有效价格")
                return
                
        print(f"\n当前日期: {current_date}")
        print(f"标的{strategy_state.undl_code}当前价格: {target_price}")
        
        all_options = C.get_option_list(strategy_state.undl_code, current_date, "", True)
        if not all_options:
            print("没有可用期权")
            return
            
        expiry_groups = {}
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if not detail:
                continue
            expiry = detail['ExpireDate']
            # 确保 expiry 是字符串
            if isinstance(expiry, (int, float)):
                expiry = str(int(expiry))
            if not expiry.isdigit() or len(expiry) != 8:
                print(f"无效的到期日格式: {expiry}")
                continue
            expiry_groups.setdefault(expiry, []).append((opt, detail))
        
        if not expiry_groups:
            print("无法获取期权详情")
            return
            
        # 计算剩余天数并选择到期日
        try:
            current_date_obj = datetime.strptime(current_date, '%Y%m%d')
        except ValueError as e:
            print(f"解析当前日期失败: {current_date}, 错误: {e}")
            return
            
        expiry_dates = sorted(expiry_groups.keys())
        nearest_expiry = expiry_dates[0]
        try:
            nearest_expiry_obj = datetime.strptime(nearest_expiry, '%Y%m%d')
        except ValueError as e:
            print(f"解析最近到期日失败: {nearest_expiry}, 错误: {e}")
            return
            
        days_to_expiry = (nearest_expiry_obj - current_date_obj).days
        
        if days_to_expiry <= 5 and len(expiry_dates) > 1:
            # 选择下一月份的到期日
            nearest_expiry_obj = datetime.strptime(nearest_expiry, '%Y%m%d')
            for expiry in expiry_dates[1:]:
                try:
                    expiry_obj = datetime.strptime(expiry, '%Y%m%d')
                except ValueError:
                    print(f"解析到期日失败: {expiry}")
                    continue
                # 验证是否为下一月份
                if (expiry_obj.year == nearest_expiry_obj.year and expiry_obj.month == nearest_expiry_obj.month + 1) or \
                   (expiry_obj.year == nearest_expiry_obj.year + 1 and expiry_obj.month == 1 and nearest_expiry_obj.month == 12):
                    nearest_expiry = expiry
                    print(f"\n最近到期日 {expiry_dates[0]} 剩余 {days_to_expiry} 天，切换至下一月份: {nearest_expiry}")
                    break
            else:
                print(f"\n最近到期日 {expiry_dates[0]} 剩余 {days_to_expiry} 天，无下一月份到期日，保留最近到期日")
        else:
            print(f"\n选择到期日: {nearest_expiry} (剩余 {days_to_expiry} 天)")
        
        selected_options = []
        min_call_diff = float('inf')
        min_put_diff = float('inf')
        closest_call = None
        closest_put = None
        
        for opt, detail in expiry_groups[nearest_expiry]:
            strike = float(detail['OptExercisePrice'])
            if detail['optType'] == 'CALL' and strike > target_price:
                diff = abs(strike - target_price)
                if diff < min_call_diff:
                    min_call_diff = diff
                    closest_call = (opt, detail)
            elif detail['optType'] == 'PUT' and strike < target_price:
                diff = abs(strike - target_price)
                if diff < min_put_diff:
                    min_put_diff = diff
                    closest_put = (opt, detail)
        
        if closest_call:
            opt, detail = closest_call
            tick_data = C.get_full_tick([opt])
            if tick_data and opt in tick_data and tick_data[opt].get('volume', 0) > 0:
                if opt not in selected_options:
                    selected_options.append(opt)
                    print(f"\n选中看涨期权: {opt}")
                    print(f"到期日: {detail['ExpireDate']}")
                    print(f"行权价: {detail['OptExercisePrice']}")
                    print(f"【{opt}】–与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            else:
                print(f"【{opt}】–>流动性不足，跳过")
        if closest_put:
            opt, detail = closest_put
            tick_data = C.get_full_tick([opt])
            if tick_data and opt in tick_data and tick_data[opt].get('volume', 0) > 0:
                if opt not in selected_options:
                    selected_options.append(opt)
                    print(f"\n选中看跌期权: {opt}")
                    print(f"到期日: {detail['ExpireDate']}")
                    print(f"行权价: {detail['OptExercisePrice']}")
                    print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            else:
                print(f"【{opt}】–>流动性不足，跳过")
            
        if selected_options:
            print(f"\n订阅期权合约: {selected_options}")
            for opt in selected_options:
                if opt not in strategy_state.monitors:
                    monitor = ContractMonitor(C, opt, strategy_state.account_manager)
                    strategy_state.monitors[opt] = monitor
                    monitor.load_parameters()
                    print(f"【{opt}】–>初始化策略合约")
                    monitor.update_price()
            
            active_contracts = list(set(selected_options) | position_contracts)
            if active_contracts:
                strategy_state.subID = C.subscribe_whole_quote(active_contracts, callback=call_back)
                if strategy_state.subID > 0:
                    print(f"行情订阅成功，订阅号: {strategy_state.subID}")
                    ContractMonitor._subscribed_contracts.update(active_contracts)
                    time.sleep(5)
                    for contract in active_contracts:
                        tick_data = C.get_full_tick([contract])
                        if not tick_data or contract not in tick_data:
                            print(f"【{contract}】–>订阅后未收到实时数据，尝试重新订阅")
                            C.subscribe_quote(contract, "1", "0")
                            ContractMonitor._subscribed_contracts.add(contract)
                else:
                    print("行情订阅失败")
                    return
        else:
            print("未找到合适期权合约")
            
    except Exception as e:
        print(f"初始化异常: {str(e)}")
        traceback.print_exc()
        strategy_state.subID = 0

def handlebar(C):
    """K线数据更新时执行"""
    try:
        if not hasattr(C, 'monitors'):
            return
            
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
        for code, monitor in C.monitors.items():
            try:
                monitor.on_tick()
            except Exception as e:
                print(f"处理合约 {code} 异常: {str(e)}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"handlebar异常: {str(e)}")
        traceback.print_exc()

def is_trade_time():
    """检查是否在交易时段"""
    current_time = time.localtime()
    hour = current_time.tm_hour
    minute = current_time.tm_min
    
    if current_time.tm_wday >= 5:
        return False
        
    if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
        return True
        
    if (hour >= 13 and hour < 15) or (hour == 15 and minute == 0):
        return True
        
    return False

def execute_order(contract_code, direction, volume, price_type, price, account_id, C, account_info, is_open=True, current_tick=None):
    """执行订单"""
    try:
        # 修改：检查 C 是否有效
        if C is None:
            print("交易上下文无效")
            return None
            
        if not is_trade_time():
            print("当前不在交易时段,不能下单")
            return None
            
        if not account_info:
            print("账户信息无效")
            return None
            
        if is_open:
            try:
                total_position = 0
                positions = get_trade_detail_data(account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_nVolume'):
                            total_position += getattr(pos, 'm_nVolume', 0)
                
                pending_volume = 0
                has_pending_orders = False
                orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
                if orders and isinstance(orders, list):
                    for order in orders:
                        if (hasattr(order, 'm_nOffsetFlag') and 
                            getattr(order, 'm_nOffsetFlag') == 48):
                            status = getattr(order, 'm_nOrderStatus', -1)
                            if status in [0, 1, 3]:
                                if (hasattr(order, 'm_strInstrumentID') and 
                                    order.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                    pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                    getattr(order, 'm_nVolumeTraded', 0))
                                    has_pending_orders = True
                                else:
                                    pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                    getattr(order, 'm_nVolumeTraded', 0))
                                    has_pending_orders = True
                
                total_exposure = total_position + pending_volume + volume
                # 修改：从 ContractMonitor 获取 max_position
                monitor = C.monitors.get(contract_code)
                max_position = monitor.max_position if monitor else 30  # 默认值 30 作为兜底
                
                if total_exposure > max_position:
                    print(f"\n>>> 持仓限制检查 <<<")
                    print(f"当前持仓: {total_position}")
                    print(f"未完成开仓: {pending_volume}")
                    print(f"本次委托: {volume}")
                    print(f"总暴露: {total_exposure}")
                    print(f"最大持仓限制: {max_position}")
                    print("超过最大持仓限制，取消委托")
                    return None
                    
                if has_pending_orders and pending_volume > 0:
                    print(f"存在未完成的开仓委托（未完成量: {pending_volume}），等待处理完成后再开新仓")
                    return None
                    
            except Exception as e:
                print(f"检查持仓限制异常: {str(e)}")
                return None
                
        if not is_open:
            # 平仓时，使用传入的 current_tick 数据
            if current_tick:
                bid_vol = current_tick.get('bidVol', [0])[0]
                print(f"【{contract_code}】–>使用传入tick数据，买一量: {bid_vol}")
                if bid_vol < volume:
                    print(f"市场可成交量不足: 需要{volume}, 可成交量{bid_vol}")
                    return None
            else:
                # 若无传入数据，重试获取
                retries = 3
                for attempt in range(retries):
                    tick_data = C.get_full_tick([f"{contract_code}.SHO"])
                    if tick_data and f"{contract_code}.SHO" in tick_data:
                        data = tick_data[f"{contract_code}.SHO"]
                        bid_vol = data.get('bidVol', [0])[0]
                        print(f"【{contract_code}】–>重试获取tick数据，买一量: {bid_vol}")
                        if bid_vol >= volume:
                            break
                    print(f"【{contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                else:
                    print(f"【{contract_code}】–>无法获取市场报价，假设市场无成交量")
                    return None
            
        # 修改：支持对手价单逻辑
        if price_type == 14:  # 对手价单
            exec_price = -1
            if current_tick:
                ref_price = current_tick.get('askPrice', [0])[0] if direction == 'BUY' else current_tick.get('bidPrice', [0])[0]
                print(f"【{contract_code}】–>对手价单，参考价格: {ref_price:.4f}")
            else:
                print(f"【{contract_code}】–>对手价单，无参考价格")
        else:  # 限价单（保留原有逻辑）
            if current_tick and direction == 'SELL':
                exec_price = current_tick.get('bidPrice', [0])[0] if current_tick else price
                print(f"【{contract_code}】–>使用传入tick价格: {exec_price}")
            else:
                subscribe_code = f"{contract_code}.SHO"
                tick_data = C.get_full_tick([subscribe_code])
                if tick_data and subscribe_code in tick_data:
                    data = tick_data[subscribe_code]
                    if isinstance(data, dict):
                        bid_price = data.get('bidPrice', [0])[0]
                        ask_price = data.get('askPrice', [0])[0]
                        if bid_price > 0 and ask_price > 0:
                            print(f"获取到有效行情 - 买价: {bid_price}, 卖价: {ask_price}")
                            exec_price = bid_price if direction == 'SELL' else ask_price
                        else:
                            print("行情价格无效，使用委托价格")
                            exec_price = price
                    else:
                        print("行情数据格式错误，使用委托价格")
                        exec_price = price
                else:
                    print(f"无法获取{contract_code}市场报价，使用委托价格")
                    exec_price = price
        
        if direction == 'BUY':
            if not is_open:
                print("不支持买入平仓操作")
                return None
            op_type = 50
        else:
            if is_open:
                print("不支持卖出开仓操作")
                return None
            op_type = 51
            
        print("\n>>> 委托参数 <<<")
        print(f"合约代码: {contract_code}")
        print(f"交易方向: {direction}")
        print(f"开平标志: {'开仓' if is_open else '平仓'}")
        print(f"委托数量: {volume}")
        print(f"价格类型: {price_type}")
        print(f"委托价格: {exec_price}")
        
        print(f"\n>>> 开始执行委托 - {'开仓' if is_open else '平仓'} <<<")
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        random_num = random.randint(1000, 9999)
        order_remark = f"{contract_code}_{direction}_{timestamp}_{random_num}"
        
        exec_price = round(exec_price, 4) if price_type != 14 else exec_price  # 对手价单无需四舍五入
        result = passorder(
            op_type,
            1101,
            account_id,
            contract_code,
            price_type,
            exec_price,
            volume,
            '期权交易',
            2,
            order_remark,
            C
        )
        
        if result is not None:
            print(f"\n>>> 委托已发送 - 投资备注: {order_remark} <<<")
            return order_remark
        else:
            print("委托发送失败，返回值为None")
            return None
            
    except Exception as e:
        print(f"订单执行异常: {str(e)}")
        traceback.print_exc()
        return None

# 3. 账户管理类
class AccountManager:
    def __init__(self, account_id):
        """账户管理器初始化"""
        self.account_id = account_id
        self.account_info = {
            'm_strAccountID': account_id,
            'm_dAvailable': 0.0,
            'm_dBalance': 0.0,
            'm_dInstrumentValue': 0.0
        }
        self.orders = {}
        self.positions = {}
        self.last_update_time = 0
        self.update_interval = 1
        self.enable_logging = False  # 新增：日志开关，默认关闭
        print(f"\n>>> 账户管理器初始化 - {account_id} <<<")

    def update(self, C):
        """
        更新账户信息和持仓状态
        """
        try:
            current_time = time.time()
            self.last_update_time = current_time
            
            if not hasattr(self, '_last_sync_log_time'):
                self._last_sync_log_time = {}
            log_interval = 60
            
            orders = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ORDER')
            if orders:
                new_orders = {}
                for order in orders:
                    order_id = getattr(order, 'm_strOrderSysID', '')
                    if not order_id:
                        continue
                                    
                    status = int(getattr(order, 'm_nOrderStatus', -1))
                    contract = getattr(order, 'm_strInstrumentID', '')
                    direction = int(getattr(order, 'm_nOffsetFlag', 0))
                    
                    if direction not in [50, 51]:
                        continue
                                    
                    if status in [49, 50, 51, 52, 55]:  # 可活动状态
                        new_orders[order_id] = {
                            'contract': contract,
                            'direction': direction,
                            'price': float(getattr(order, 'm_dLimitPrice', 0.0)),
                            'volume': int(getattr(order, 'm_nVolumeTotalOriginal', 0)),
                            'traded': int(getattr(order, 'm_nVolumeTraded', 0)),
                            'status': status,
                            'submit_time': getattr(order, 'm_strInsertTime', '')
                        }
                                    
                        if order_id not in self.orders:
                            if hasattr(C, 'monitors'):
                                monitor = C.monitors.get(f"{contract}.SHO")
                                if monitor:
                                    monitor.active_orders[order_id] = {
                                        'time': time.time(),
                                        'is_close': direction == 51,
                                        'price': new_orders[order_id]['price'],
                                        'volume': new_orders[order_id]['volume'],
                                        'traded': new_orders[order_id]['traded'],
                                        'direction': direction
                                    }
                        elif hasattr(C, 'monitors'):
                            monitor = C.monitors.get(f"{contract}.SHO")
                            if monitor and order_id in monitor.active_orders:
                                monitor.active_orders[order_id].update({
                                    'traded': new_orders[order_id]['traded']
                                })
                        
                    self.orders = new_orders
                            
                    if hasattr(C, 'monitors'):
                        for monitor in C.monitors.values():
                            to_delete = [order_id for order_id in monitor.active_orders 
                                        if order_id not in new_orders]
                            for order_id in to_delete:
                                del monitor.active_orders[order_id]
                
                # 清理无持仓时的活动订单
                for monitor in C.monitors.values():
                    if monitor.position == 0 and monitor.active_orders:
                        print(f"【{monitor.contract_code}】–>持仓为0，清理活动订单")
                        monitor.active_orders.clear()
            
            account_info = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ACCOUNT')
            if account_info and len(account_info) > 0:
                account_obj = account_info[0]
                new_account_info = {
                    'm_strAccountID': self.account_id,
                    'm_dAvailable': float(getattr(account_obj, 'm_dAvailable', 0.0)),
                    'm_dBalance': float(getattr(account_obj, 'm_dBalance', 0.0)),
                    'm_dInstrumentValue': float(getattr(account_obj, 'm_dInstrumentValue', 0.0))
                }
                            
                if self._has_account_changes(new_account_info):
                    self.account_info = new_account_info
                    self._log_account_changes(new_account_info)
                
            positions = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'POSITION')
            if positions:
                new_positions = {}
                for pos in positions:
                    contract = getattr(pos, 'm_strInstrumentID', '')
                    volume = getattr(pos, 'm_nVolume', 0)
                    frozen_volume = getattr(pos, 'm_nFrozenVolume', 0)
                    if volume > 0:
                        open_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                        new_positions[contract] = {
                            'Volume': volume,
                            'FrozenVolume': frozen_volume,
                            'AvailableVolume': volume - frozen_volume,
                            'OpenPrice': open_price,
                            'PositionValue': volume * open_price * 10000,
                            'UnrealizedPL': float(getattr(pos, 'm_dPositionProfit', 0.0))
                        }
                        if contract in self.positions and 'CustomOpenPrice' in self.positions[contract]:
                            new_positions[contract]['CustomOpenPrice'] = self.positions[contract]['CustomOpenPrice']
                                            
                        if hasattr(C, 'monitors'):
                            monitor = C.monitors.get(f"{contract}.SHO")
                            if monitor and monitor.contract_code == f"{contract}.SHO":
                                monitor.position = volume
                                monitor.in_position = volume > 0
                                if volume > 0:
                                    if not hasattr(monitor, '_entry_price_synced'):
                                        monitor._entry_price_synced = False
                                    old_entry_price = monitor.entry_price
                                    old_original_entry_price = monitor.original_entry_price
                                    
                                    if monitor.is_legacy_position and not monitor.entry_price:
                                        monitor.entry_price = new_positions[contract].get('CustomOpenPrice', monitor.current_price if monitor.update_price() else open_price)
                                        monitor._entry_price_synced = True
                                    elif not monitor.is_legacy_position and not monitor.entry_price:
                                        monitor.entry_price = new_positions[contract]['OpenPrice']
                                        monitor.entry_time = time.strftime("%Y%m%d%H%M%S")
                                    if not monitor.original_entry_price and not monitor.is_legacy_position:
                                        if not monitor.update_price():
                                            monitor.current_price = open_price
                                            print(f"【{contract}.SHO】–>无法获取最新价，使用平均开仓价: {open_price:.4f}")
                                        monitor.original_entry_price = monitor.current_price
                                        monitor._entry_price_synced = True
                                    
                                    if (not monitor._entry_price_synced or 
                                        old_entry_price != monitor.entry_price or 
                                        old_original_entry_price != monitor.original_entry_price):
                                        if (contract not in self._last_sync_log_time or 
                                            current_time - self._last_sync_log_time.get(contract, 0) >= log_interval):
                                            print(f"【{contract}.SHO】–>同步 entry_price: {monitor.entry_price:.4f}, 原始开仓价: {monitor.original_entry_price:.4f}")
                                            self._last_sync_log_time[contract] = current_time
                                    monitor._entry_price_synced = True
                            
                if self._has_position_changes(new_positions):
                    self._log_position_changes(new_positions)
                    self.positions = new_positions
                                        
        except Exception as e:
            print(f"更新账户信息异常: {str(e)}")
            traceback.print_exc()

    def _has_account_changes(self, new_account_info):
        """检查账户信息是否有变化"""
        if not self.account_info:
            return True
            
        thresholds = {
            'm_dAvailable': 0.01,
            'm_dBalance': 0.01,
            'm_dInstrumentValue': 0.01,
        }
        
        for key, threshold in thresholds.items():
            old_value = self.account_info.get(key, 0)
            new_value = new_account_info[key]
            if abs(new_value - old_value) >= threshold:
                return True
                
        if new_account_info.get('m_strStatus') != self.account_info.get('m_strStatus'):
            return True
            
        return False
        
    def _log_account_changes(self, new_account_info):
        """记录账户变化"""
        if self.enable_logging:  # 新增：检查日志开关
            print("\n>>> 账户状态 <<<")
            print(f"账户ID: {new_account_info['m_strAccountID']}")
            print(f"可用资金: {new_account_info['m_dAvailable']:.2f}")
            print(f"总资产: {new_account_info['m_dBalance']:.2f}")
            print(f"持仓市值: {new_account_info['m_dInstrumentValue']:.2f}")
            print("")
        
    def _has_position_changes(self, new_positions):
        """检查持仓是否有显著变化"""
        if not self.positions:
            return bool(new_positions)
            
        if set(self.positions.keys()) != set(new_positions.keys()):
            return True
            
        for contract in self.positions:
            if contract not in new_positions:
                continue
                
            old_pos = self.positions[contract]
            new_pos = new_positions[contract]
            
            if old_pos['Volume'] != new_pos['Volume']:
                return True
                
            old_pnl_ratio = (old_pos['UnrealizedPL'] / old_pos['PositionValue'] * 100 
                        if old_pos['PositionValue'] > 0 else 0)
            new_pnl_ratio = (new_pos['UnrealizedPL'] / new_pos['PositionValue'] * 100 
                        if new_pos['PositionValue'] > 0 else 0)
            if abs(new_pnl_ratio - old_pnl_ratio) > 0.1:
                return True
                
        return False
        
    def _log_position_changes(self, new_positions):
        """记录持仓变化"""
        if not new_positions:
            return
            
        if self.enable_logging:  # 新增：检查日志开关
            print("\n>>> 持仓状态 <<<")
            for contract_id, pos in new_positions.items():
                volume = pos.get('Volume', 0)
                open_price = pos.get('OpenPrice', 0.0)
                unrealized_pl = pos.get('UnrealizedPL', 0.0)
                
                market_value = volume * open_price * 10000
                if market_value > 0:
                    pl_ratio = unrealized_pl / market_value if market_value != 0 else 0
                    print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}, 盈亏比例: {pl_ratio*100:.2f}%")
                else:
                    print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}")
                    if 'OpenTime' in pos:
                        print(f"开仓时间: {pos['OpenTime']}")
                    if 'Direction' in pos:
                        print(f"方向: {'多头' if pos['Direction'] == 1 else '空头'}")
                    if 'MarketValue' in pos:
                        print(f"市值: {pos['MarketValue']:.2f}")
                    print("")