﻿函数命名规则
· 函数名以 get_ 开头的，表示数据来源于客户端内存
· 函数名以 query_ 开头的，表示数据是向服务查询
· 账号类型说明
· 'STOCK_OPTION' - 股票期权
· 数据类
· #Tick - Tick 对象
· 行情快照数据
· #get_market_data_ex/get_full_tick返回对象：
             字段名                    数据类型                                       含义
            time                    int                                       时间戳
            stime                  string                                   时间戳字符串形式
          lastPrice                float                                      最新价
            open                   float                                      开盘价
            high                   float                                      最高价
             low                   float                                      最低价
          lastClose                float                                      前收盘价
           amount                  float                                      成交总额
           volume                   int                                     成交总量（手）
           pvolume                  int                           原始成交总量(未经过股手转换的成交总量)【不推荐使用】
         stockStatus                int                                       证券状态
        openInterest                int                  若是股票，则openInt含义为股票状态，非股票则是持仓量openInt字段说明在新窗口打开
       transactionNum              float                                成交笔数(期货没有，单独计算)
     lastSettlementPrice           float                                   前结算(股票为0)
       settlementPrice             float                                   今结算(股票为0)
          askPrice              list[float]                                  多档委卖价
           askVol                list[int]                                   多档委卖量
          bidPrice              list[float]                                  多档委买价
           bidVol                list[int]                                   多档委买量
· #get_market_data返回对象：
             字段                    数据类型                                       含义
timetag                      string          时间戳，格式为: %Y%m%d %H:%M:%S
lastPrice                    float           最新价
open                         float           开盘价
high                         float           最高价
low                          float           最低价
lastClose                    float           前收盘价
amount                       float           成交额
volume                       float           成交量（手）
pvolume                      float           原始成交量（股）【不推荐使用】
stockStatus                  int             作废 参考openInt
openInt                      float           若是股票，则openInt含义为股票状态，非股票则是持仓量openInt字段说明
lastSettlementPrice          float           昨结算价
pe                           float           对于股票是市盈率,对于ETF是iopv值
askPrice                     list            委卖价
bidPrice                     list            委买价
askVol                       list            委卖量
bidVol                       list            委买量
settlementPrice              float           今结算价
· #subscribe_quote/subscribe_whole_quote回调对象：
· 同 get_full_tick 返回结构
· #Bar - Bar对象
· bar数据是指各种频率的行情数据
            字段                     数据类型                           含义
time                       int                 时间
open                       float               开盘价
high                       float               最高价
low                        float               最低价
close                      float               收盘价
volume                     float               成交量
amount                     float               成交额
settelementPrice           float               今结算
openInterest               float               持仓量
preClose                   float               前收盘价
suspendFlag                int                 停牌 1停牌，0 不停牌
交易类
#Account - 账户对象
                 字段名                      数据类型                            解释
m_strAccountID                        str          资金账号，用于识别不同的资金账户
m_nBrokerType                         int          账号类型，表示账号的具体种类
m_dMaxMarginRate                      float        保证金比率，通常用于期货账号
m_dFrozenMargin                       float        冻结保证金，指投资者在交易中被冻结的保证金金额
m_dFrozenCash                         float        冻结金额，指投资者在交易中被冻结的资金金额
m_dFrozenCommission                   float        冻结手续费，指投资者在交易中被冻结的手续费金额
m_dRisk                               float        风险度，指投资者账户的风险程度
m_dNav                                float        单位净值，用于表示基金的净值
m_dPreBalance                         float        期初权益，指期初时账户的资金金额
m_dBalance                            float        总资产，表示账户的总资金金额
m_dAvailable                          float        可用金额，指账户中可用于交易和提取的资金金额
m_dCommission                         float        手续费 (旧版本为 m_dComission)
m_dPositionProfit                     float        持仓盈亏，指当前持有的证券或期货合约的盈亏金额
m_dCloseProfit                        float        平仓盈亏，在期货交易中表示已经平仓的交易的盈亏金额
m_dCashIn                             float        出入金净值，表示账户中出入金的净额
m_dCurrMargin                         float        当前使用的保证金金额
m_dInitBalance                        float        初始权益，指账户初始时的权益金额
m_strStatus                           str          状态，表示账户的当前状态
m_dInitCloseMoney                     float        期初平仓盈亏，指账户初始时的平仓盈亏金额
m_dInstrumentValue                    float        总市值，表示持有的证券或期货合约的总市值
m_dDeposit                            float        入金，指账户中的入金金额
m_dWithdraw                           float        出金，指账户中的出金金额
m_dPreCredit                          float        上次信用额度，用于表示上次的信用额度
m_dPreMortgage                        float        上次质押，指上次的质押金额
m_dMortgage                           float        质押，指当前的质押金额
m_dCredit                             float        信用额度，表示账户的信用额度
m_dAssetBalance                       float        证券初始资金，表示股票账户的初始资金
m_strOpenDate                         str          起始日期，表示账户的起始日期
m_dFetchBalance                       float        可取金额，指账户中可取出的金额
m_strTradingDate                      str          交易日，表示当前的交易日期
m_dStockValue                         float        股票总市值，表示股票账户中持有的股票的总市值
m_dLoanValue                          float        债券总市值，表示账户中持有的债券的总市值
m_dFundValue                          float        基金总市值，包括ETF和封闭式基金在内的基金的总市值
m_dRepurchaseValue                    float        回购总市值，表示账户中持有的所有回购交易的总市值
m_dLongValue                          float        多单总市值，指现货账户中多单持仓的总市值
m_dShortValue                         float        空单总市值，指现货账户中空单持仓的总市值
m_dNetValue                           float        净持仓总市值，指现货账户中多单总市值减去空单总市值的差额
m_dAssureAsset                        float        净资产，表示账户的净资产金额
m_dTotalDebit                         float        总负债，表示账户的总负债金额
m_dEntrustAsset                       float        可信资产，用于校对账户资金的准确性
m_dInstrumentValueRMB                 float        总市值（人民币），指沪港通账户中的持仓证券的总市值
m_dSubscribeFee                       float        申购费，指申购基金时支付的费用
m_dGoldValue                          float        库存市值，表示黄金现货账户中黄金库存的市值
m_dGoldFrozen                         float        现货冻结，表示黄金现货账户中被冻结的黄金金额
m_dMargin                             float        占用保证金，用于维持保证金
m_strMoneyType                        str          币种，表示账户的资金所使用的货币种类
m_dPurchasingPower                    float        购买力，指账户可用于购买投资品的金额
m_dRawMargin                          float        原始保证金，指期货账户中的原始保证金金额
m_dBuyWaitMoney                       float        待交收金额（元），指账户中买入股票但尚未交收的金额
m_dSellWaitMoney                      float        卖出待交收金额（元），指账户中卖出股票但尚未交收的金额
m_dReceiveInterestTotal               float        本期间应计利息，指账户本期间内应计的利息金额
m_dRoyalty                            float        权利金收支，指期货期权交易中的权利金收支金额
m_dFrozenRoyalty                      float        冻结权利金，指期货期权交易中被冻结的权利金金额
m_dRealUsedMargin                     float        实时占用保证金，用于股票期权交易中表示实时占用的保证金金额
m_dRealRiskDegree                     float        实时风险度，用于股票期权交易中表示实时的风险度
#Order - 委托对象
                  字段                      数据类型                           解释
m_strAccountID                        str         资金账号，账号，账号，资金账号
m_strExchangeID                       str         证券市场
m_strExchangeName                     str         交易市场
m_strProductID                        str         品种代码
m_strProductName                      str         品种名称
m_strInstrumentID                     str         证券代码
m_strInstrumentName                   str         证券名称，合约名称
m_strOrderRef                         str         内部委托号，下单引用等于股票的内部委托号
m_nOrderPriceType                     int         EBrokerPriceType 类型，例如市价单、限价单在新窗口打开
m_nDirection                          int         EEntrustBS 类型，操作，多空，期货多空，股票买卖永远是 48，其他的 dir 同理
m_nOffsetFlag                         int         EOffset_Flag_Type类型，买卖/开平，用此字段区分股票买卖，期货开、平仓，期权买卖等
m_nHedgeFlag                          int         EHedge_Flag_Type 类型，投保
m_dLimitPrice                         float       委托价格，限价单的限价，即报价
m_nVolumeTotalOriginal                int         委托数量，最初的委托数量
m_nOrderSubmitStatus                  int         EEntrustSubmitStatus 类型，报单状态，提交状态，股票中不需要报单状态
m_strOrderSysID                       str         合同编号，委托号
m_nOrderStatus                        int         EEntrustStatus，委托状态
m_nVolumeTraded                       int         成交数量，已成交量
m_nVolumeTotal                        int         委托剩余量，当前总委托量，股票中表示总委托量减去成交量
m_nErrorID                            int         状态ID
m_strErrorMsg                         str         状态信息
m_nTaskId                             int         任务号
m_dFrozenMargin                       float       冻结金额，冻结保证金
m_dFrozenCommission                   float       冻结手续费
m_strInsertDate                       str         委托日期，报单日期
m_strInsertTime                       str         委托时间
m_dTradedPrice                        float       成交均价（股票）
m_dCancelAmount                       float       已撤数量
m_strOptName                          str         买卖标记，展示委托属性的中文
m_dTradeAmount                        float       成交金额，期货的计算方式为均价乘以数量乘以合约乘数
m_eEntrustType                        int         EEntrustTypes，委托类别
m_strCancelInfo                       str         废单原因
m_strUnderCode                        str         标的证券代码
m_eCoveredFlag                        int         备兑标记，'0’表示非备兑，'1’表示备兑
m_dOrderPriceRMB                      float       委托价格（人民币），目前用于港股通
m_dTradeAmountRMB                     float       成交金额（人民币），目前用于港股通
m_dReferenceRate                      float       汇率，目前用于港股通
m_strCompactNo                        str         合约编号
m_eCashgroupProp                      int         EXTCompactBrushSource类型，头寸来源
m_dShortOccupedMargin                 float       预估在途占用保证金，用于期权
m_strXTTrade                          str         是否是迅投交易
m_strAccountKey                       str         账号key，唯一区别不同账号的key
m_strRemark                           str         投资备注
#Deal - 成交对象
                字段                     数据类型                             解释
m_strAccountID                     str         资金账号
m_strExchangeID                    str         证券市场
m_strExchangeName                  str         交易市场
m_strProductID                     str         品种代码
m_strProductName                   str         品种名称
m_strInstrumentID                  str         证券代码
m_strInstrumentName                str         证券名称
m_strTradeID                       str         成交编号
m_strOrderRef                      str         下单引用，等于股票的内部委托号
m_strOrderSysID                    str         合同编号，报单编号，委托号
m_nDirection                       int         EEntrustBS，买卖方向 对于股票该值始终是48在新窗口打开
m_nOffsetFlag                      int         EOffset_Flag_Type，买卖/开平，用此字段区分股票买卖，期货开、平仓，期权买卖等在新窗口打
                                               开
m_nHedgeFlag                       int         EHedge_Flag_Type 类型，投保在新窗口打开
m_dPrice                           float       成交均价
m_nVolume                          int         成交量，期货单位手，股票做到股
m_strTradeDate                     str         成交日期
m_strTradeTime                     str         成交时间
m_dCommission                      float       手续费 (旧版本为 m_dComission)
m_dTradeAmount                     float       成交额，期货 = 均价 * 量 * 合约乘数
m_nTaskId                          int         任务号
m_nOrderPriceType                  int         EBrokerPriceType 类型，例如市价单、限价单在新窗口打开
m_strOptName                       str         买卖标记，展示委托属性的中文
m_eEntrustType                     int         EEntrustTypes，委托类别在新窗口打开
m_eFutureTradeType                 int         EFutureTradeType 类型，成交类型在新窗口打开
m_nRealOffsetFlag                  int         EOffset_Flag_Type 类型，实际开平，主要是区分平今和平昨在新窗口打开
m_eCoveredFlag                     int         ECoveredFlag类型，备兑标记 '0' - 非备兑，'1' - 备兑
m_nCloseTodayVolume                int         平今量，不显示
m_dOrderPriceRMB                   float       委托价格（人民币），目前用于港股通
m_dPriceRMB                        float       成交价格（人民币），目前用于港股通
m_dTradeAmountRMB                  float       成交金额（人民币），目前用于港股通
m_dReferenceRate                   float       汇率，目前用于港股通
m_strXTTrade                       str         是否是迅投交易
m_strCompactNo                     str         合约编号
m_dCloseProfit                     float       平仓盈亏，目前用于外盘
m_strRemark                        str         投资备注
m_strAccountKey                    str         账号key，唯一区别不同账号的key
m_nRef                             int         订单编号
#Position - 持仓对象
                  字段名                        数据类型                           含义
m_strAccountID                          string        资金账号
m_strExchangeID                         string        证券市场
m_strExchangeName                       string        市场名称
m_strProductID                          string        品种代码
m_strProductName                        string        品种名称
m_strInstrumentID                       string        证券代码
m_strInstrumentName                     string        证券名称
m_nHedgeFlag                            int           EHedge_Flag_Type 类型，投保 ，股票不适用在新窗口打开
m_nDirection                            int           EEntrustBS，买卖方向 对于股票该值始终是48在新窗口打开
m_strOpenDate                           string        开仓日期 股票此字段无效
m_strTradeID                            string        成交号，最初开仓位的成交
m_nVolume                               int           当前拥股/持仓量
m_dOpenPrice                            float         持仓成本 ；持仓成本 = (总买入金额 - 总卖出金额) / 剩余数量
m_strTradingDay                         string        在实盘运行中是当前交易日，在回测中是股票最后交易过的日期
m_dMargin                               float         使用的保证金，历史的直接用ctp的，新的自己用成本价存量系数算，股票不适用
m_dOpenCost                             float         开仓成本，等于成本价*第一次建仓的量，后续减持会影响，不算手续费，股票不适用
m_dSettlementPrice                      float         最新结算价/当前价
m_nCloseVolume                          int           平仓量（对于股票不适用）
m_dCloseAmount                          float         平仓额（对于股票不适用）
m_dFloatProfit                          float         浮动盈亏
m_dCloseProfit                          float         平仓盈亏（对于股票不适用）
m_dMarketValue                          float         市值/合约价值
m_dPositionCost                         float         持仓成本（对于股票不适用）
m_dPositionProfit                       float         持仓盈亏（对于股票不适用）
m_dLastSettlementPrice                  float         最新结算价（对于股票不适用）
m_dInstrumentValue                      float         合约价值（对于股票不适用）
m_bIsToday                              bool          是否今仓
m_strStockHolder                        string        股东账号
m_nFrozenVolume                         int           冻结数量
m_nCanUseVolume                         int           可用余额
m_nOnRoadVolume                         int           在途股份
m_nYesterdayVolume                      int           昨夜拥股
m_dLastPrice                            float         最新价/当前价
m_dAvgOpenPrice                         float         开仓均价（对于股票不适用）
m_dProfitRate                           float         盈亏比例
m_eFutureTradeType                      int           EFutureTradeType 类型，成交类型在新窗口打开
m_strExpireDate                         string        到期日（针对逆回购）
m_strComTradeID                         string        组合成交号
m_nLegId                                int           组合序号
m_dTotalCost                            float         累计成本（自定义，股票信用用到）
m_dSingleCost                           float         单股成本（自定义，股票信用用
m_nCoveredVolume                        int           备兑数量，用于个股期权
m_eSideFlag                             int           持仓类型 ，用于个股期权，标记 '0' - 权利，'1' - 义务，'2' - '备兑'
m_dReferenceRate                        float         汇率，目前用于港股通
m_dStructFundVol                        float         分级基金可用（可分拆或可合并）
m_dRedemptionVolume                     float         分级基金可赎回量
m_nPREnableVolume                       int           申赎可用量（记录当日申购赎回的股票或基金数量）
m_dRealUsedMargin                       float         实时占用保证金，用于期权
m_dRoyalty                              float         权利金
m_dStockLastPrice                       float         标的证券最新价，用于期权
m_dStaticHoldMargin                     float         静态持仓占用保证金，用于期权
m_nOptCombUsedVolume                    int           期权组合占用数量
m_nEnableExerciseVolume                 int           能够行使的数量，用于个股期权
m_strAccountKey                         string        账号key，唯一区别不同账号的key
#PositionStatistics - 持仓统计对象
                       字段名                                数据类型                            描述
m_strAccountID                                    string              账号
m_strExchangeID                                   string              市场代码
m_strExchangeName                                 string              市场名称
m_strProductID                                    string              品种代码
m_strInstrumentID                                 string              合约代码
m_strInstrumentName                               string              合约名称
m_nDirection                                      int                 多空
m_nHedgeFlag                                      int                 投保
m_nPosition                                       int                 持仓
m_nYestodayPosition                               int                 昨仓
m_nTodayPosition                                  int                 今仓
m_nCanCloseVol                                    int                 可平
m_dPositionCost                                   float               持仓成本
m_dAvgPrice                                       float               持仓均价
m_dPositionProfit                                 float               持仓盈亏
m_dFloatProfit                                    float               浮动盈亏
m_dOpenPrice                                      float               开仓均价
m_dUsedMargin                                     float               已使用保证金
m_dUsedCommission                                 float               已使用的手续费
m_dFrozenMargin                                   float               冻结保证金
m_dFrozenCommission                               float               冻结手续费
m_dInstrumentValue                                float               市值，合约价值
m_nOpenTimes                                      int                 开仓次数
m_nOpenVolume                                     int                 总开仓量 中间平仓不减
m_nCancelTimes                                    int                 撤单次数
m_dLastPrice                                      float               最新价
m_dRiseRatio                                      float               当日涨幅
m_strProductName                                  string              产品名称
m_dRoyalty                                        float               权利金市值
m_strExpireDate                                   string              到期日
m_dAssestWeight                                   float               资产占比
m_dIncreaseBySettlement                           float               当日涨幅（结）
m_dMarginRatio                                    float               保证金占比
m_dFloatProfitDivideByUsedMargin                  float               浮盈比例（保证金）
m_dFloatProfitDivideByBalance                     float               浮盈比例（动态权益）
m_dTodayProfitLoss                                float               当日盈亏（结）
m_nYestodayInitPosition                           int                 昨日持仓
m_dFrozenRoyalty                                  float               冻结权利金
m_dTodayCloseProfitLoss                           float               当日盈亏（收）
m_dCloseProfit                                    float               平仓盈亏
m_strFtProductName                                string              品种名称
m_dOpenCost                                       float               开仓成本
PassorderArguments - 下单函数参数对象
          字段名                数据类型                             解释
        opType                int                     passorder的opType参数
       orderType              int                   passorder的orderType参数
       accountID            string                           资金账号
       orderCode            string                           交易代码
        prType                int                   passorder的prType，价格类型
      modelPrice             float                           下单价格
      modelVolume             int                         下单量（手数或股数）
     strategyName           string                     策略名 _ &&& _ 投资备注
#CTaskDetail - 任务对象
             字段名                   数据类型                                解释
          m_nTaskId                int                                任务号
          m_eStatus                enum             任务状态 ETaskStatus类型,见ETaskStatus说明在新窗口打开
           m_strMsg               string                             任务状态消息
         m_startTime               int                           任务开始时间, 时间戳类型
          m_endTime                int                           任务结束时间, 时间戳类型
         m_cancelTime              int                               任务取消时间
        m_nBusinessNum             int                                已成交量
          m_nGroupId               int                                组合Id
         m_stockCode              string                         下单代码(不针对组合下单)
        m_strAccountID            string                          下单用户(单用户下单)
       m_eOperationType            enum      下单操作：开平、多空……EOperationType类型, 见EOperationType说明在新窗口打开
         m_eOrderType              enum           算法交易、普通交易 EOrderType类型, 见EOrderType说明在新窗口打开
         m_ePriceType              enum           报价方式：对手、最新…… EPriceType类型见EPriceType说明在新窗口打开
         m_dFixPrice              float                               委托价
            m_nNum                 int                                委托量
         m_strRemark              string                              投资备注
#CLockPosition - 期权标的持仓
                字段名                       数据类型                解释
           m_strAccountID                string              账号名
          m_strExchangeID                string              交易所
         m_strExchangeName               string              交易所名
         m_strInstrumentID               string              标的代码
        m_strInstrumentName              string              标的名称
             m_totalVol                    int               总持仓量
             m_lockVol                     int              可用锁定量
            m_unlockVol                    int               未锁定量
            m_coveredVol                   int               备兑量
        m_nOnRoadcoveredVol                int              在途备兑量
#CStkOptCombPositionDetail - 期权组合持仓
                 字段名                        数据类型                            解释
            m_strAccountID                 string                          账号名
           m_strExchangeID                 string                          交易所
          m_strExchangeName                string                          交易所名
         m_strContractAccount              string                          合约账号
             m_strCombID                   string                          组合编号
            m_strCombCode                  string                         组合策略编码
          m_strCombCodeName                string                         组合策略名称
              m_nVolume                     int                            持仓量
           m_nFrozenVolume                  int                            冻结数量
           m_nCanUseVolume                  int                            可用数量
            m_strFirstCode                 string                          合约一
           m_eFirstCodeType                 enum                    合约一类型 认购:48,认沽:49
          m_strFirstCodeName               string                         合约一名称
         m_eFirstCodePosType                enum                合约一持仓类型 认购:48,义务:49,备兑:50
           m_nFirstCodeAmt                  int                           合约一数量
           m_strSecondCode                 string                          合约二
          m_eSecondCodeType                 enum                    合约二类型 认购:48,认沽:49
         m_strSecondCodeName               string                         合约二名称
         m_eSecondCodePosType               enum                合约二持仓类型 权利:48,义务:49,备兑:50
           m_nSecondCodeAmt                 int                           合约二数量
          m_dCombBailBalance               float                          占用保证金
#entrustType - 委托类型
· 0 - 未知
· 1 - 正常交易业务
· 2 - 即时成交剩余撤销
· 3 - ETF基金申报
· 4 - 最优五档即时成交剩余撤销
· 5 - 全额成交或撤销
· 6 - 本方最优价格
· 7 - 对手方最优价格
#openInt - 证券状态
    编码                                                状态
0,10       默认为未知
1          停牌
11         开盘前S
12         集合竞价时段C
13         连续交易T
14         休市B
15         闭市E
16         波动性中断V,例如(10006742.SHO)50ETF沽9月2300在2024/08/28 10:15:34 - 2024/08/28 10:18:34
            触发熔断临时停牌，此时的openInt值为16
17         临时停牌P
18         收盘集合竞价U
19         盘中集合竞价M
20         暂停交易至闭市N
21         获取字段异常
22         盘后固定价格行情
23         盘后固定价格行情完毕
ContextInfo 对象
ContextInfo 是策略运行环境对象，是 init, after_init, handlebar 等基本方法的入参，里面包括了终端自带的属性和方法。一般情况下不建议对ContextInfo添加自定义属性，ContextInfo会随着bar的切换而重置到上一根bar的结束状态，建议用自建的全局变量来存储。详细说明请看这里在新窗口打开
#init - 初始化函数
初始化函数，只在整个策略开始时调用运行到一次。用于初始订阅行情，订阅账号信息使用。init函数执行完成前部分接口无法使用，如交易日获取函数get_trading_dates。
系统函数 不可被手动调用
参数：
        名称              类型                                         描述
ContextInfo        object      策略运行环境对象，可以用于存储自定义的全局变量
返回： 无
示例：
python
def init(ContextInfo):
    ContextInfo.initProfit = 0
在init函数中订阅行情示例：
python
#coding:gbk

def init(C):
	#init函数入参为ContextInfo对象 定义时可以选择更简短的形参名 如C
	#在init函数中 可以进行 订阅行情的操作
    #如需在行情回调函数中下单 下单函数需要传入ContextInfo对象 可以通过在init中定义回调函数 来使用外层的ContextInfo
	def my_callback_function(data):
		#自定义行情回调函数 入参为指数据字典
		print(data)
	stock = '600000.SH'
	C.subscribe_quote(stock, period = '5m', callback = my_callback_function)
	#init函数执行完成后 
	print('init函数执行完成')
#after_init - 初始化后函数
后初始化函数，在初始化函数执行完成后被调用一次。可以用于放置一次性触发的下单，取数据操作代码。
系统会在init函数执行完后和执行handlebar之前调用after_init, 有些init里不支持的函数比如ContextInfo.get_trading_dates可以在after_init里调用。
系统函数 不可被手动调用
参数：
        名称              类型                                         描述
ContextInfo        object      策略运行环境对象，可以用于存储自定义的全局变量
返回： 无
示例：
python
#coding:gbk
def init(ContextInfo):
    print('init')  


def after_init(ContextInfo):
    print('系统会在init函数执行完后和执行handlebar之前调用after_init')


def handlebar(ContextInfo):
    if ContextInfo.is_last_bar():
        print('handlebar')

after_init函数中立刻下单示例：
python
#coding:gbk

def after_init(C):
	#after_init 函数 可以用于执行运行开始时 需要执行一次的代码 例如下一笔委托
	#account变量是模型交易界面 添加策略时选择的资金账号 不需要手动填写 交易模型需要在模型交易界面运行 才有效
	#快速交易参数(quickTrade )填2 passorder函数执行后立刻下单 不会等待k线走完再委托。 可以在after_init函数 run_time函数注册的回调函数里进行委托 
	msg = f"投资备注字符串 用来区分不同委托"
	passorder(23, 1101, account, '600000.SH', 5, -1, 100, '测试下单', 2, msg, C)
#handlebar - 行情事件函数
系统函数 不可被手动调用
释义： 行情事件函数，每根 K 线运行一次；实时行情获取状态下，先每根历史 K 线运行一次，再在每个 tick 数据来后驱动运行一次
历史k线上，按时间顺序每根K线触发一次调用；盘中，每个新到达的TICK数据驱动运行一次。可以作为行情驱动的函数，实现指标计算，回测，实盘下单的效果。
参数：
        名称              类型                                         描述
ContextInfo        object      策略运行环境对象，可以用于存储自定义的全局变量
返回： 无
示例：
def handlebar(ContextInfo):
    # 输出当前运行到的 K 线的位置
    print(ContextInfo.barpos)
#ContextInfo.schedule_run - 设置定时器
说明
1. 该函数是新版设置定时器函数，相比旧版run_time，新版schedule_run新增了任务分组,任务取消等多种功能
原型:
python
ContextInfo.schedule_run(
    func:Callable, # 回调函数，到达定时器预定时间时触发调用，参数为ContextInfo类型，无需返回值，定义示例def on_timer(C:ContextInfo):
    time_point:Union[dt.datetime,str], # 表示预定的第一次触发时间，如果设置定时器时已经过了预定时间，会立即执行func以及后续逻辑；当使用str类型时，格式为'yyyymmddHHMMSS'如'20231231235959'，需要满足转换dt.datetime.strptime('20231231235959','%Y%m%d%H%M%S')
    repeat_times:int=0, # 表示在预定时间触发后按interval间隔再触发多少次
    interval:datetime.timedelta=None, # 表示预定时间触发后的后续重复执行的时间间隔
    name:str='' # 定时器任务组名，可用于定时器分组，多次设置同名定时任务不会互相覆盖，会计入同一个任务组，按任务组名取消时会全部取消
    )
参数：
      名称                 类型                                          描述
func          Callable                 回调函数，到达定时器预定时间时触发调用，参数为ContextInfo类型，无需返回值，定义示例def
                                        on_timer(C:ContextInfo):
                                        pass
time_point    Union[datetime.datetime,s表示预定的第一次触发时间，如果设置定时器时已经过了预定时间，会立即执行func以及后续逻辑；当使用str类型时，格式为'yyy
              tr]                      ymmddHHMMSS'如'20231231235959'，需要满足转换datetime.datetime.strptime(
                                       '20231231235959','%Y%m%d%H%M%S')
repeat_times  int                      表示在预定时间触发后按interval间隔再触发多少次，传-1表示不限制次数
interval      datetime.timedelta       表示预定时间触发后的后续重复执行的时间间隔
name          str                      定时器任务组名，可用于定时器分组，多次设置同名定时任务不会互相覆盖，会计入同一个任务组，按任务组名取消时会全部取消
回调函数参数： ContextInfo：策略模型全局对象
返回值：
int类型，表示本次调用后生成的定时任务号，可用于取消本次定时任务，全局唯一不重复
示例：
python
import datetime as dt
def on_timer(C:ContextInfo):
    print('hello world')
def init(ContextInfo):
    tid=ContextInfo.schedule_run(on_timer,'20231231235959',-1,dt.timedelta(minutes=1),'my_timer')
def handlebar(ContextInfo):
    pass
#此例为自2023-12-31 23:59:59后每60s运行一次on_timer
#ContextInfo.cancel_schedule_run - 取消由schedule_run产生的定时任务
原型：
python
ContextInfo.cancel_schedule_run(
    key:Union[seq:int,name:str] # 定时任务号或定时任务组名称
    )
参数：
    名称                    类型                                             描述
key:       Union[seq:int,name:str]         类型为int时，表示按任务号取消;类型为str时，表示按任务组取消，会取消组内所有定时任务
返回值：
bool类型，表示是否取消成功，即是否能按key找到目标定时任务
示例：
示例

ContextInfo.cancel_schedule_run('my_timer') #取消my_timer任务组所有定时任务
ContextInfo.cancel_schedule_run(1) #取消任务号为1的定时任务

#ContextInfo.run_time - 设置定时器
设置定时器函数，可以指定时间间隔，定时触发用户定义的回调函数。适用与在盘中，持续判断交易信号的模型。
用法： ContextInfo.run_time(funcName,period,startTime) 定时触发指定的 funcName函数, funcName函数由用户定义, 入参为ContextInfo对象。
参数：
· funcName：回调函数名
· period：重复调用的时间间隔,'5nSecond'表示每5秒运行1次回调函数,'5nDay'表示每5天运行一次回调函数,'500nMilliSecond'表示每500毫秒运行1次回调函数
· startTime：表示定时器第一次启动的时间,如果要定时器立刻启动,可以设置历史的时间
回调函数参数： ContextInfo：策略模型全局对象
示例：
python
import time
def init(ContextInfo):
    ContextInfo.run_time("f","5nSecond","2019-10-14 13:20:00")
def f(ContextInfo):
    print('hello world')

#此例为自2019-10-14 13:20:00后每5s运行一次函数f
注意
1. 模型回测时无效
2. 定时器没有结束方法，会随着策略的结束而结束。
3. period有nMilliSecond、nSecond和Day三个周期单元，部分周期下定时器函数在第一次运行之前会先等待一个period
#stop - 停止处理函数
系统函数 不可被手动调用
释义： PY策略模型关闭停止前运行到的函数，复杂策略模型，如中间有起线程可通过在该函数内实现停止线程操作。注意, 当前版本stop函数被调用时交易连接已断开, 不能在stop函数中做报单 / 撤单操作.
参数：
        名称              类型                                         描述
ContextInfo        object      策略运行环境对象，可以用于存储自定义的全局变量
示例：
python
def stop(ContextInfo):
    print( 'strategy is stop !')
#ContextInfo.is_last_bar - 是否为最后一根K线
用法： ContextInfo.is_last_bar()
释义： 判定是否为最后一根 K 线
参数： 无
返回： bool，返回值含义：True 是右侧最新k线 False不是最新k线
True：是
False：否
示例：
pythonresult
def handlebar(ContextInfo):
    print(ContextInfo.is_last_bar())
#ContextInfo.is_new_bar - 判定是否为新的 K 线
用法： ContextInfo.is_new_bar()
释义： 某根 K 线的第一个 tick 数据到来时，判定该 K 线为新的 K 线，其后的tick不会认为是新的 K 线
参数： 无
返回： bool，返回值含义：
True：是
False：否
示例：
pythonresult
def handlebar(ContextInfo):
    print(ContextInfo.is_new_bar()) #历史k线每根都是新k线 盘中 每根新k线第一个分笔返回True 其他分笔返回False
#ContextInfo.get_stock_name - 根据代码获取名称
注意
我们计划后续版本抛弃这个函数，不建议继续使用，可以用ContextInfo.get_instrument_detail("stockcode")["InstrumentName"]来实现同样功能
用法： ContextInfo.get_stock_name('stockcode')
释义： 根据代码获取名称
参数： stockcode：股票代码，如'000001.SZ'，缺省值 ' ' 默认为当前图代码
返回： string（GBK编码）
示例：
示例返回值
def handlebar(ContextInfo):
    print(ContextInfo.get_stock_name('000001.SZ'))
#ContextInfo.get_open_date - 根据代码返回对应股票的上市时间
用法： ContextInfo.get_open_date('stockcode')
释义： 根据代码返回对应股票的上市时间
参数： stockcode：股票代码，如'000001.SZ'，缺省值 ' ' 默认为当前图代码
返回： number
示例：
pythonresult
def init(ContextInfo):
    print(ContextInfo.get_open_date('000001.SZ'))
#ContextInfo.set_output_index_property - 设定指标绘制的属性
用法： ContextInfo.set_output_index_property(index_name,draw_style=0,color='white',noaxis=False,nodraw=False,noshow=False)
释义： 设定指标绘制的属性，会最终覆盖掉指标对应的属性字段
参数：
· index_name:string,指标名称，不可缺省
· draw_style,同paint函数的drawstyle，可缺省默认为0
· color,同paint函数的color，可缺省默认为'white'
· noaxis:bool,是否无坐标，可缺省默认为False
· nodraw:bool,是否不画线，可缺省默认为False
· noshow:bool,是否不展示，可缺省默认为False
返回： 无
示例：
pythonpythonresult
def init(ContextInfo):
    ContextInfo.set_output_index_property('单位净值', nodraw = True)#使回测指标'单位净值'不画线
#create_sector - 创建板块
用法： create_sector(parent_node,sector_name,overwrite)
释义： 创建板块
参数：
· parent_node：str，父节点，''为'我的'（默认目录）
· sector_name：str，要创建的板块名
· overwrite：bool，是否覆盖。如果目标节点已存在，为True时跳过，为False时在sector_name后增加数字编号，编号为从1开始自增的第一个不重复的值。
返回： sector_name2：实际创建的板块名
示例：
#create_sector_folder - 创建板块目录节点
用法： create_sector_folder(parent_node,folder_name,overwrite)
释义： 创建板块目录节点
参数：
· parent_node：str，父节点，''为'我的'（默认目录）
· sector_name：str，要创建的节点名
· overwrite：bool，是否覆盖。如果目标节点已存在，为True时跳过，为False时在folder_name后增加数字编号，编号为从1开始自增的第一个不重复的值。
返回： sector_name2：实际创建的节点名
示例：
pythonresult
folder=create_sector_folder('我的','新建分类',False)
#get_sector_list - 获取板块目录信息
用法： get_sector_list(node)
释义： 获取板块目录信息
参数：
· node：str，板块节点名，''为顶层目录
返回： info_list：[[s1,s2,...],[f1,f2,...]]s为板块名，f为目录节点名，例如[['我的自选'],['新建分类1']]
示例：
pythonresult
get_sector_list('我的')
#reset_sector_stock_list - 设置板块成分股
用法： reset_sector_stock_list(sector,stock_list)
释义： 设置板块成分股
参数：
· sector：板块名
· stock_list：list，品种代码列表，例如['000001.SZ','600000.SH']
返回： result：bool，操作成功为True，失败为False
示例：
pythonresult
reset_sector_stock_list('我的自选',['000001.SZ','600000.SH'])
#remove_stock_from_sector - 移除板块成分股
用法： remove_stock_from_sector(sector,stock_code)
释义： 移除板块成分股
参数：
· sector：板块名
· stock_code：品种代码，例如'000001.SZ'
返回： result：bool，操作成功为True，失败为False
示例：
pythonresult
remove_stock_from_sector('我的自选','000001.SZ')
#add_stock_to_sector - 添加板块成分股
用法： add_stock_to_sector(sector,stock_code)
释义： 添加板块成分股
参数：
· sector：板块名
· stock_code：品种代码，例如'000001.SZ'
返回： result：bool，操作成功为True，失败为False
示例：
pythonresult
add_stock_to_sector('我的自选','000001.SZ')
获取行情数据
该目录下的函数用于获取实时行情,历史行情
#ContextInfo.get_market_data_ex - 获取行情数据
注意
1. 该函数不建议在init中运行,在init中运行时仅能取到本地数据
2. 关于获取行情函数之间的区别与注意事项可在 - 常见问题-行情相关在新窗口打开 查看
3. 除实时行情外，该函数还可用于获取特色数据，如资金流向数据,订单流数据等，获取方式见数据字典在新窗口打开
原型
内置python
ContextInfo.get_market_data_ex(
    fields=[], 
    stock_code=[], 
    period='follow', 
    start_time='', 
    end_time='', 
    count=-1, 
    dividend_type='follow', 
    fill_data=True, 
    subscribe=True)
释义
获取实时行情与历史行情数据
参数
           名称                类型                                              描述
field                   list         数据字段，详情见下方field字段表
stock_list              list         合约代码列表
period                  str          数据周期，可选字段为:
                                     "tick"
                                     "1m"：1分钟线
                                     "5m"：5分钟线；"15m"：15分钟线；"30m"：30分钟线
                                     "1h"小时线
                                     "1d"：日线
                                     "1w"：周线
                                     "1mon"：月线
                                     "1q"：季线
                                     "1hy"：半年线
                                     "1y"：年线
                                     'l2quote'：Level2行情快照
                                     'l2quoteaux'：Level2行情快照补充
                                     'l2order'：Level2逐笔委托
                                     'l2transaction'：Level2逐笔成交
                                     'l2transactioncount'：Level2大单统计
                                     'l2orderqueue'：Level2委买委卖队列
start_time              str          数据起始时间，格式为 %Y%m%d 或 %Y%m%d%H%M%S，填""为获取历史最早一天
end_time                str          数据结束时间，格式为 %Y%m%d 或 %Y%m%d%H%M%S ，填""为截止到最新一天
count                   int          数据个数
dividend_type           str          除权方式,可选值为
                                     'none'：不复权
                                     'front':前复权
                                     'back':后复权
                                     'front_ratio': 等比前复权
                                     'back_ratio': 等比后复权
fill_data               bool         是否填充数据
subscribe               bool         订阅数据开关，默认为True，设置为False时不做数据订阅，只读取本地已有数据。
· field字段可选：
        field                 数据类型                           含义
time                  int                 时间
open                  float               开盘价
high                  float               最高价
low                   float               最低价
close                 float               收盘价
volume                float               成交量
amount                float               成交额
settle                float               今结算
openInterest          float               持仓量
preClose              float               前收盘价
suspendFlag           int                 停牌 1停牌，0 不停牌
· period周期为tick时，field字段可选:
        field                 数据类型                           含义
time                  int                 时间
lastPrice             float               最新价
lastClose             float               前收盘价
open                  float               开盘价
high                  float               最高价
low                   float               最低价
close                 float               收盘价
volume                float               成交量
amount                float               成交额
settle                float               今结算
openInterest          float               持仓量
stockStatus           int                 停牌 1停牌，0 不停牌
· period周期为Level2数据时，字段参考数据结构
返回值
· 返回dict { stock_code1 : value1, stock_code2 : value2, ... }
· value1, value2, ... ：pd.DataFrame 数据集，index为time_list，columns为fields,可参考Bar字段在新窗口打开
· 各标的对应的DataFrame维度相同、索引相同
示例
示例data1返回值data2返回值data3返回值data4返回值历史tick期货五档盘口
# coding:gbk
import pandas as pd
import numpy as np

def init(C):	
	C.stock_list = ["000001.SZ","600519.SH", "510050.SH"]# 指定获取的标的
	C.start_time = "20230901"# 指定获取数据的开始时间
	C.end_time = "20231101"# 指定获取数据的结束时间
	
def handlebar(C):
	# 获取多只股票，多个字段，一条数据
	data1 = C.get_market_data_ex([],C.stock_list, period = "1d",count = 1)
	# 获取多只股票，多个字段，指定时间数据
	data2 = C.get_market_data_ex([],C.stock_list, period = "1d", start_time = C.start_time, end_time = C.end_time)
	# 获取多只股票，多个字段，指定时间15m数据
	data3 = C.get_market_data_ex([],C.stock_list, period = "15m", start_time = C.start_time, end_time = C.end_time)
	# 获取多只股票，指定字段，指定时间15m数据
	data4 = C.get_market_data_ex(["close","open"],C.stock_list, period = "15m", start_time = C.start_time, end_time = C.end_time)
	# 获取多只股票，历史tick
	tick = C.get_market_data_ex([],C.stock_list, period = "tick", start_time = C.start_time, end_time = C.end_time)
	# 获取期货5档盘口tick
	future_lv2_quote = C.get_market_data_ex([],["rb2405.SF","ec2404.INE"], period = "l2quote", count = 1)
	print(data1)
	print(data2["000001.SZ"].tail())
	print(data3)
	print(data4["000001.SZ"])
	print(data4["000001.SZ"].to_csv("your_path")) # 导出文件为csv格式，路径填本机路径
	print(tick["000001.SZ"])
	print(future_lv2_quote)

#ContextInfo.get_full_tick - 获取全推数据
提示
不能用于回测 只能取最新的分笔，不能取历史分笔
原型
内置python
ContextInfo.get_full_tick(stock_code=[])
释义
获取最新分笔数据
参数
         名称                类型                                               描述
stock_code          list[str]       合约代码列表，如['600000.SH','600036.SH']，不指定时为当前主图合约。
返回值 根据stock_code返回一个dict，该字典的key值是股票代码，其值仍然是一个dict，在该dict中存放股票代码对应的最新的数据。该字典数据key值参考tick字段在新窗口打开
示例
示例返回值
# coding:gbk
import pandas as pd
import numpy as np

def init(C):
	C.stock_list = ["000001.SZ","600519.SH", "510050.SH"]
	
def handlebar(C):
	tick = C.get_full_tick(C.stock_list)
	print(tick["510050.SH"])
#ContextInfo.subscribe_quote - 订阅行情数据
提示
1. 该函数属于订阅函数，非VIP用户限制订阅数量
2. VIP用户支持全推市场指定周期K线
3. VIP用户权限请参考vip-行情用户优势对比
原型
内置python
ContextInfo.subscribe_quote(
    stock_code,
    period='follow',
    dividend_type='follow',
    result_type='',
    callback=None)
释义
订阅行情数据,关于订阅机制请参考运行机制对比在新窗口打开
参数
          字段名                 数据类型                                             解释
       stockcode             string                            股票代码，'stkcode.market'，如'600000.SH'
         period              string                                          K线周期类型
     dividend_type           string                                        除权方式,可选值为
                                                                           'none'：不复权
                                                                          'front':前复权
                                                                           'back':后复权
                                                                      'front_ratio': 等比前复权
                                                                      'back_ratio': 等比后复权
                                                                        注意：分笔周期返回数据均为不复权
      result_type            string      返回数据格式,可选范围：<br>'DataFrame'或''（默认）：返回{code:data}，data为pd.DataFrame数据集，index为字符
                                         串格式的时间序列，columns为数据字段<br>'dict'：返回{code:{k1:v1,k2:v2,...}}，k为数据字段名，v为字段值<br>'l
                                                       ist'：返回{code:{k1:[v1],k2:[v2],...}}，k为数据字段名，v为字段值
        callback            function                                      指定推送行情的回调函数
返回值
int：订阅号，用于反订阅
示例
示例返回值
# conding = gbk
def call_back(data):
	print(data)
	
def init(C):
	C.subID = C.subscribe_quote("000001.SZ","1d", callback = call_back)
def handlebar(C):
	print("============================")
	print("C.subID: ",C.subID)
	
#ContextInfo.subscribe_whole_quote - 订阅全推数据
提示
内置python
ContextInfo.subscribe_whole_quote(code_list,callback=None)
释义
订阅全推数据，全推数据只有分笔周期，每次增量推送数据有变化的品种
参数
       字段名               数据类型                                               解释
    code_list       list[str,...]                市场代码列表/品种代码列表,如 ['SH','SZ'] 或 ['600000.SH', '000001.SZ']
    callback           function                                           数据推送回调
返回值int，订阅号，可用ContextInfo.unsubscribe_quote做反订阅
示例返回值
# conding = gbk
def call_back(data):
	print(data)
	
def init(C):
	C.stock_list = ["000001.SZ","600519.SH", "510050.SH"]
	C.subID = C.subscribe_whole_quote(C.stock_list,callback=call_back)
def handlebar(C):
	print("============================")
	print("C.subID: ",C.subID)
#ContextInfo.unsubscribe_quote - 反订阅行情数据
原型
内置python
ContextInfo.unsubscribe_quote(subId)
释义
反订阅行情数据，配合ContextInfo.subscribe_quote()或ContextInfo.subscribe_whole_quote()使用
参数
      字段名              数据类型                       解释
     subId              int                   行情订阅返回的订阅号
示例
示例
# conding = gbk
def call_back(data):
	print(data)
def init(C):
	C.stock_list = ["000001.SZ","600519.SH", "510050.SH"]
	C.subID = C.subscribe_whole_quote(C.stock_list,callback=call_back)

def handlebar(C):
	print("============================")
	print("C.subID: ",C.subID)
	if C.subID > 0:
		C.unsubscribe_quote(C.subID) # 取消行情订阅
#subscribe_formula - 订阅模型
原型
内置python
subscribe_formula(
   formula_name,stock_code,period
   ,start_time="",end_time="",count=-1
   ,dividend_type="none"
   ,extend_param={}
   ,callback=None)
释义 订阅vba模型运行结果，使用前要注意补充本地K线数据或分笔数据
参数
          字段名               类型                                   描述
formula_name            str       模型名称名
stock_code              str       模型主图代码形式如'stkcode.market'，如'000300.SH'
period                  str       K线周期类型，可选范围：'tick':分笔线，'1d':日线，'1m':分钟线，'3m':三分钟线，'5m':5分钟线，'15m'
                                  :15分钟线，'30m':30分钟线，'1h':小时线，'1w':周线，'1mon':月线，'1q':季线，'1hy':半年线，'
                                  1y':年线
start_time              str       模型运行起始时间，形如:'20200101'，默认为空视为最早
end_time                str       模型运行截止时间，形如:'20200101'，默认为空视为最新
count                   int       模型运行范围为向前 count 根 bar，默认为 -1 运行所有 bar
dividend_type           str       复权方式，默认为主图除权方式，可选范围：'none':不复权，'front':向前复权，'back':向后复权，'front_ra
                                  tio':等比向前复权，'back_ratio':等比向后复权
extend_param            dict      模型的入参，形如 {'a': 1, '__basket': {}}
__basket                dict      可选参数，组合模型的股票池权重，形如 {'600000.SH': 0.06, '000001.SZ': 0.01}
返回值 分两块，
· subscribe_formula返回模型的订阅号,可用于后续反订阅，失败返回 -1
· callback:
o timelist： 数据时间戳
o outputs：模型的输出值，结构为{变量名:值}
示例
示例
#encoding=gbk
def callback(data):
    print(data)

def init(ContextInfo):
    basket={
       '600000.SH':0.06,
       '000001.SZ':0.01
      }
    argsDict={'a':100,'__basket':basket}
    subID=subscribe_formula(
      '单股模型示范','000300.SH','1d',
      '20240101','20240201',-1,
      "none",
      argsDict,
      callback
   )

#unsubscribe_formula - 反订阅模型
原型
内置python
unsubscribe_formula(subID)
释义 反订阅模型
参数
     字段名          类型              描述
subID         int        模型订阅号
返回值
· bool:反订阅成功为True，失败为False
示例
示例
#encoding=gbk
def callback(data):
    print(data)

def init(ContextInfo):
    basket={
       '600000.SH':0.06,
       '000001.SZ':0.01
      }
    argsDict={'a':100,'__basket':basket}
    subID=subscribe_formula(
      '单股模型示范','000300.SH','1d',
      '20240101','20240201',-1,
      "none",
      argsDict,
      callback
   )

	unsubscribe_formula(subID)
#call_formula - 调用模型
原型
内置python
call_formula(formula_name,stock_code,period,start_time="",end_time="",count=-1,dividend_type="none",extend_param={})
释义 获取vba模型运行结果，使用前要注意补充本地K线数据或分笔数据
参数
          字段名              类型                                    描述
formula_name           str       模型名称名
stock_code             str       模型主图代码形式如'stkcode.market'，如'000300.SH'
period                 str       K线周期类型，可选范围：'tick':分笔线，'1d':日线，'1m':分钟线，'3m':三分钟线，'5m':5分钟线，'15m':
                                 15分钟线，'30m':30分钟线，'1h':小时线，'1w':周线，'1mon':月线，'1q':季线，'1hy':半年线，'1y
                                 ':年线
start_time             str       模型运行起始时间，形如:'20200101'，默认为空视为最早
end_time               str       模型运行截止时间，形如:'20200101'，默认为空视为最新
count                  int       模型运行范围为向前 count 根 bar，默认为 -1 运行所有 bar
dividend_type          str       复权方式，默认为主图除权方式，可选范围：'none':不复权，'front':向前复权，'back':向后复权，'front_rat
                                 io':等比向前复权，'back_ratio':等比向后复权
extend_param           dict      模型的入参,{"模型名:参数名":参数值},例如在跑模型MA时，{'MA:n1':1};入参可以添加__basket:dict,组合
                                 模型的股票池权重,形如{'__basket':{'600000.SH':0.06,'000001.SZ':0.01}}，如果在跑一个
                                 模型1的时候，模型1调用了模型2，如果只想修改模型2的参数可以传{'模型2:参数':参数值}
返回值 返回：dict{ 'dbt':0,#返回数据类型，0:全部历史数据 'timelist':[...],#返回数据时间范围list, 'outputs':{'var1':[...],'var2':[...]}#输出变量名：变量值list }
示例
示例
def handlebar(ContextInfo):
    basket={'600000.SH':0.06,'000001.SZ':0.01}
    argsDict={'a':100,'__basket':basket}
    modelRet=call_formula('单股模型示范','000300.SH','1d','20240101','20240201',-1,"none",argsDict)
    print(modelRet)

#call_formula_batch - 批量调用模型
原型
内置python
call_formula_batch(formula_names,stock_codes,period,start_time="",end_time="",count=-1,dividend_type="none",extend_params=[])

释义 批量获取vba模型运行结果，使用前要注意补充本地K线数据或分笔数据
参数
           字段名              类型                                    描述
formula_names            list     包含要批量运行的模型名
stock_codes              list     包含要批量运行的模型主图代码形式'stkcode.market'，如'000300.SH'
period                   str      K线周期类型，可选范围：'tick':分笔线，'1d':日线，'1m':分钟线，'3m':三分钟线，'5m':5分钟线，'15m':
                                  15分钟线，'30m':30分钟线，'1h':小时线，'1w':周线，'1mon':月线，'1q':季线，'1hy':半年线，'1y
                                  ':年线
start_time               str      模型运行起始时间，形如:'20200101'，默认为空视为最早
end_time                 str      模型运行截止时间，形如:'20200101'，默认为空视为最新
count                    int      模型运行范围为向前 count 根 bar，默认为 -1 运行所有 bar
dividend_type            str      复权方式，默认为主图除权方式，可选范围：'none':不复权，'front':向前复权，'back':向后复权，'front_rat
                                  io':等比向前复权，'back_ratio':等比向后复权
extend_params            list     包含每个模型的入参,[{"模型名:参数名":参数值}],例如在跑模型MA时，{'MA:n1':1};入参可以添加__basket:d
                                  ict,组合模型的股票池权重,形如{'__basket':{'600000.SH':0.06,'000001.SZ':0.01}}，
                                  如果在跑一个模型1的时候，模型1调用了模型2，如果只想修改模型2的参数可以传{'模型2:参数':参数值}
返回值
· list[dict]
o dict说明:
§ formula:模型名
§ stock:品种代码
§ argument:参数
§ result:dict参考call_formula返回结果
示例
示例

def handlebar(ContextInfo):
    formulas=['testModel1','testModel2']
    codes=['600000.SH','000001.SZ']
    basket={'600000.SH':0.06,'000001.SZ':0.01}
    args=[{'a':100,'__basket':basket},{'a':200,'__basket':basket}]
    modelRet=call_formula_batch(formulas,codes,'1d',extend_params=args);
    print(modelRet)

#ContextInfo.get_svol - 根据代码获取对应股票的内盘成交量
原型
内置python
ContextInfo.get_svol(stockcode)
释义
根据代码获取对应股票的内盘成交量
参数
      字段名             数据类型       解释
   stockcode         string      股票代码，如 '000001.SZ'，缺省值''，默认为当前图代码
返回值int:内盘成交量
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	data = C.get_svol('000001.SZ')
	print(data)
#ContextInfo.get_bvol - 根据代码获取对应股票的外盘成交量
原型
内置python
ContextInfo.get_bvol(stockcode)
释义
根据代码获取对应股票的外盘成交量
参数
      字段名             数据类型       解释
   stockcode         string      股票代码，如 '000001.SZ'，缺省值''，默认为当前图代码
返回值
int:外盘成交量
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	data = C.get_bvol('000001.SZ')
	print(data)
#ContextInfo.get_turnover_rate - 获取换手率
提示
使用之前需要下载财务数据(在财务数据下载中)以及日线数据
如果不补充股本数据,将使用最新流通股本计算历史换手率,可能会造成历史换手率不正确
原型
内置python
ContextInfo.get_turnover_rate(stock_list,startTime,endTime)
释义
获取换手率
参数
       字段名              数据类型       解释
   stock_list           list       股票列表，如['600000.SH','000001.SZ']
    startTime          string      起始时间，如'20170101'
     endTime           string      结束时间，如'20180101'
返回值
pandas.Dataframe
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	data = C.get_turnover_rate(['000002.SZ'],'20170101','20170301')
	print(data)
#ContextInfo.get_longhubang - 获取龙虎榜数据
原型
内置python
ContextInfo.get_longhubang(stock_list, startTime, endTime)
释义
获取龙虎榜数据
参数
       参数名称            类型                                  描述
stock_list        list        股票列表，如 ['600000.SH', '600036.SH']
startTime         str         起始时间，如 '20170101'
endTime           str         结束时间，如 '20180101'
返回值
· 格式为pandas.DataFrame:
            参数名称                        数据类型                      描述
reason                      str                          上榜原因
close                       float                        收盘价
spreadRate                  float                        涨跌幅
TurnoverVolune              float                        成交量
Turnover_Amount             float                        成交金额
buyTraderBooth              pandas.DataFrame             买方席位
sellTraderBooth             pandas.DataFrame             卖方席位
· buyTraderBooth 或 sellTraderBooth 包含字段：
        参数名称                数据类型                          描述
traderName           str                交易营业部名称
buyAmount            float              买入金额
buyPercent           float              买入金额占总成交占比
sellAmount           float              卖出金额
sellPercent          float              卖出金额占总成交占比
totalAmount          float              该席位总成交金额
rank                 int                席位排行
direction            int                买卖方向
示例
示例返回值
# coding:gbk

def init(C):
    return

def handlebar(C):
    print(C.get_longhubang(['000002.SZ'],'20100101','20180101'))
#ContextInfo.get_north_finance_change - 获取对应周期的北向数据
原型
内置python
ContextInfo.get_north_finance_change(period)
释义
获取对应周期的北向数据
参数
      字段名             数据类型                描述
period         str                数据周期
返回值
· 根据period返回一个dict，该字典的key值是北向数据的时间戳，其值仍然是一个dict，其值的key值是北向数据的字段类型，其值是对应字段的值。该字典数据key值有：
                字段名                      数据类型                       描述
hgtNorthBuyMoney                   int              HGT北向买入资金
hgtNorthSellMoney                  int              HGT北向卖出资金
hgtSouthBuyMoney                   int              HGT南向买入资金
hgtSouthSellMoney                  int              HGT南向卖出资金
sgtNorthBuyMoney                   int              SGT北向买入资金
sgtNorthSellMoney                  int              SGT北向卖出资金
sgtSouthBuyMoney                   int              SGT南向买入资金
sgtSouthSellMoney                  int              SGT南向卖出资金
hgtNorthNetInFlow                  int              HGT北向资金净流入
hgtNorthBalanceByDay               int              HGT北向当日资金余额
hgtSouthNetInFlow                  int              HGT南向资金净流入
hgtSouthBalanceByDay               int              HGT南向当日资金余额
sgtNorthNetInFlow                  int              SGT北向资金净流入
sgtNorthBalanceByDay               int              SGT北向当日资金余额
sgtSouthNetInFlow                  int              SGT南向资金净流入
sgtSouthBalanceByDay               int              SGT南向当日资金余额
示例：
示例返回值
# coding = gbk
def init(C):
    return
# 获取市场北向数据
def handlebar(C):
    print(C.get_north_finance_change('1d'))
#ContextInfo.get_hkt_details - 获取指定品种的持股明细
原型
内置python
ContextInfo.get_hkt_details(stockcode)
释义
获取指定品种的持股明细
参数
       参数名称              数据类型                          描述
stockcode         string            必须是'stock.market'形式
返回值
· 根据stockcode返回一个dict，该字典的key值是北向持股明细数据的时间戳，其值仍然是一个dict，其值的key值是北向持股明细数据的字段类型，其值是对应字段的值，该字典数据key值有：
               参数名称                        数据类型/单位                                  描述
stockCode                         str                      股票代码
ownSharesCompany                  str                      机构名称
ownSharesAmount                   int                      持股数量
ownSharesMarketValue              float                    持股市值
ownSharesRatio                    float                    持股数量占比
ownSharesNetBuy                   float                    净买入金额（当日持股-前一日持股）
示例：
示例返回值
# coding = gbk
def init(C):
    return
def handlebar(C):
    data = C.get_hkt_details('600000.SH')
    print(data)
#ContextInfo.get_hkt_statistics - 获取指定品种的持股统计
原型
内置python
ContextInfo.get_hkt_statistics(stockcode)
释义
获取指定品种的持股统计
参数
       字段名              数据类型       解释
    stockcode          string      必须是'stock.market'形式
返回值
根据stockcode返回一个dict，该字典的key值是北向持股统计数据的时间戳，其值仍然是一个dict，其值的key值是北向持股统计数据的字段类型，其值是对应字段的值，该字典数据key值有：
               字段名                      数据类型       解释
            stockCode                  string      股票代码
         ownSharesAmount                float      持股数量，单位：股
       ownSharesMarketValue             float      持股市值，单位：元
          ownSharesRatio                float      持股数量占比，单位：%
         ownSharesNetBuy                float      净买入，单位：元，浮点数（当日持股-前一日持股）
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):

	print(C.get_hkt_statistics('600000.SH'))
#get_etf_info - 根据ETF基金代码获取ETF申赎清单及对应成分股数据
原型
内置python
get_etf_info(stockcode)
释义
根据ETF基金代码获取ETF申赎清单及对应成分股数据,每日盘前更新
参数
       字段名              数据类型       解释
    stockcode          string      ETF基金代码如"510050.SH"
返回值
一个多层嵌套的dict
示例
示例返回值
# coding:gbk
def init(C):
    pass
    
def handlebar(C):
    d = get_etf_info("510050.SH")
    print(d)
#get_etf_iopv - 根据ETF基金代码获取ETF的基金份额参考净值
原型
内置python
get_etf_iopv(stockcode)
释义
根据ETF基金代码获取ETF的基金份额参考净值
参数
       字段名              数据类型       解释
    stockcode          string      ETF基金代码如"510050.SH"
返回值
float类型值,IOPV，基金份额参考净值
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	print(get_etf_iopv("510050.SH"))
获取期权信息
#ContextInfo.get_option_detail_data - 获取指定期权品种的详细信息
原型
内置python
ContextInfo.get_option_detail_data(optioncode)
释义
获取指定期权品种的详细信息
参数
       字段名             数据类型      解释
    optioncode        string     期权代码,如'10001506.SHO',当填写空字符串时候默认为当前主图的期权品种
返回值dict,字段如下：
                 字段                      类型                    说明
ExchangeID                           str        期权市场代码
InstrumentID                         str        期权代码
ProductID                            str        期权标的的产品ID
OpenDate                             int        发行日期
ExpireDate                           int        到期日
PreClose                             float      前收价格
SettlementPrice                      float      前结算价格
UpStopPrice                          float      当日涨停价
DownStopPrice                        float      当日跌停价
LongMarginRatio                      float      多头保证金率
ShortMarginRatio                     float      空头保证金率
PriceTick                            float      最小变价单位
VolumeMultiple                       int        合约乘数
MaxMarketOrderVolume                 int        涨跌停价最大下单量
MinMarketOrderVolume                 int        涨跌停价最小下单量
MaxLimitOrderVolume                  int        限价单最大下单量
MinLimitOrderVolume                  int        限价单最小下单量
OptUnit                              int        期权合约单位
MarginUnit                           float      期权单位保证金
OptUndlCode                          str        期权标的证券代码
OptUndlMarket                        str        期权标的证券市场
OptExercisePrice                     float      期权行权价
NeeqExeType                          str        全国股转转让类型
OptUndlRiskFreeRate                  float      期权标的无风险利率
OptUndlHistoryRate                   float      期权标的历史波动率
EndDelivDate                         int        期权行权终止日
optType                              str        期权类型
示例
示例返回值
#encoding:gbk
def init(ContextInfo):
  pass

def after_init(ContextInfo):
  print(ContextInfo.get_option_detail_data('10002235.SHO'))
#ContextInfo.get_option_list - 获取指定期权列表
原型
内置python
ContextInfo.get_option_list(undl_code,dedate,opttype,isavailable)
释义
获取指定期权列表。如获取历史期权，需先下载过期合约列表
参数
       字段名            数据类型     解释
    undl_code        string    期权标的代码,如'510300.SH'
     dedate          string    期权到期月或当前交易日期，"YYYYMM"格式为期权到期月，"YYYYMMDD"格式为获取当前日期交易的期权
     opttype         string    期权类型，默认值为空，"CALL"，"PUT"，为空时认购认沽都取
   isavailable        bool     是否可交易，当dedate的格式为"YYYYMMDD"格式为获取当前日期交易的期权时，isavailable为True时返回当前可用，为False时
                               返回当前和历史可用
返回值
list，期权合约列表
示例
示例data1返回值data2返回值data3返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):
	# 获取到期月份为202101的上交所510300ETF认购合约
	data1=C.get_option_list('510300.SH','202101',"CALL")

	# 获取20210104当天上交所510300ETF可交易的认购合约
	data2=C.get_option_list('510300.SH','20210104',"CALL",True)

	# 获取20210104当天上交所510300ETF已经上市的认购合约(包括退市)
	data3=C.get_option_list('510300.SH','20210104',"CALL",False)
#ContextInfo.get_option_undl_data - 获取指定期权标的对应的期权品种列表
原型
内置python
ContextInfo.get_option_undl_data(undl_code_ref)
释义
获取指定期权标的对应的期权品种列表
参数
         字段名               数据类型      解释
    undl_code_ref         string     期权标的代码,如'510300.SH'，传空字符串时获取全部标的数据
返回值
指定期权标的代码时返回对应该标的的期权合约列表list
期权标的代码为空字符串时返回全部标的对应的品种列表的字典dict
示例
示例返回值
# coding:gbk
def init(C):
	pass
	
def handlebar(C):

	print(C.get_option_undl_data('510300.SH')[:30])
#ContextInfo.bsm_price - 基于BS模型计算欧式期权理论价格
原型
内置python
ContextInfo.bsm_price(optionType,objectPrices,strikePrice,riskFree,sigma,days,dividend)
释义
基于Black-Scholes-Merton模型，输入期权标的价格、期权行权价、无风险利率、期权标的年化波动率、剩余天数、标的分红率、计算期权的理论价格
参数
          字段                类型                                     说明
optionType            str           期权类型，认购：'C'，认沽：'P'
objectPrices          float         期权标的价格，可以是价格列表或者单个价格
strikePrice           float         期权行权价
riskFree              float         无风险收益率
sigma                 float         标的波动率
days                  int           剩余天数
dividend              float         分红率
返回
提示
· objectPrices为float时，返回float
· objectPrices为list时，返回list
· 计算结果最小值0.0001，结果保留4位小数,输入非法参数返回nan
示例返回值
#encoding:gbk
import numpy as np


def init(ContextInfo):
  pass

def after_init(ContextInfo):
  object_prices=list(np.arange(3,4,0.01));
  #计算剩余15天的行权价3.5的认购期权,在无风险利率3%,分红率为0,标的年化波动率为23%时标的价格从3元到4元变动过程中期权理论价格序列
  prices=ContextInfo.bsm_price('C',object_prices,3.5,0.03,0.23,15,0)
  print(prices)
  #计算剩余15天的行权价3.5的认购期权,在无风险利率3%,分红率为0,标的年化波动率为23%时标的价格为3.51元的平值期权的理论价格
  price=ContextInfo.bsm_price('C',3.51,3.5,0.03,0.23,15,0)
  print(price)

#ContextInfo.bsm_iv - 基于BS模型计算欧式期权隐含波动率
原型
内置python
ContextInfo.bsm_iv(optionType,objectPrices,strikePrice,optionPrice,riskFree,days,dividend)

释义 基于Black-Scholes-Merton模型,输入期权标的价格、期权行权价、期权现价、无风险利率、剩余天数、标的分红率,计算期权的隐含波动率
参数
          字段                类型                                     说明
optionType            str           期权类型，认购：'C'，认沽：'P'
objectPrices          float         期权标的价格，可以是价格列表或者单个价格
strikePrice           float         期权行权价
riskFree              float         无风险收益率
sigma                 float         标的波动率
days                  int           剩余天数
dividend              float         分红率
返回
double
示例返回值
#encoding:gbk
import numpy as np

def init(ContextInfo):
    pass

def after_init(ContextInfo):
    # 计算剩余15天的行权价3.5的认购期权,在无风险利率3%,分红率为0时,标的现价3.51元,期权价格0.0725元时的隐含波动率
    iv=ContextInfo.bsm_iv('C',3.51,3.5,0.0725,0.03,15)
    print(iv)
交易下单函数
#passorder - 综合下单函数
综合下单函数，用于股票、期货、期权等下单和新股、新债申购、融资融券等交易操作推荐使用
提示
1. 推荐使用
2. 可覆盖多品种下单
3. 注意参数的变化
调用方法：
python示例
passorder(
    opType, orderType, accountid
    , orderCode, prType, price, volume
    , strategyName, quickTrade, userOrderId
    , ContextInfo
)
'''
passorder(
    2 #opType 操作号
    , 1101 #orderType 组合方式
    , '1000044' #accountid 资金账号
    , 'cu2403.SF' #orderCode 品种代码
    , 14 #prType 报价类型
    , 0.0 #price 价格
    , 2 #volume 下单量
    , '示例下单' #strategyName 策略名称
    , 1 #quickTrade 快速下单标记
    , '投资备注' #userOrderId 投资备注
    , C #ContextInfo 策略上下文
)
'''
参数：
         参数名              类型           说明                                提示
opType               int         交易类型          可选买、买，期货开仓、平仓等

                                               可选值参考opType-操作类型在新窗口打开
orderType            int                       可选值参考orderType-下单方式在新窗口打开

                                 下单方式          可选按股票数量买卖或按照金额等方式买卖

                                               一、期货不支持 1102 和 1202;

                                               二、对所有账号组的操作相当于对账号组里的每个账号做一样的操作，如 passorder (23, 1202,
                                                'testS', '000001. SZ', 5, -1, 50000,
                                                ContextInfo)，意思就是对账号组 testS 里的所有账号都以最新价开仓买入 50000
                                                元市值的 000001.SZ 平安银行；passorder
                                                (60,1101,"test",'510050. SH', 5,-1,1,
                                                ContextInfo)意思就是账号test申购 1 个单位 (900000股)的华夏上证50ETF
                                                (只申购不买入成分股)。
accountID            string      资金账号          下单的账号ID（可多个）或账号组名或套利组名（一个篮子一个套利账号，如 accountID =
                                                '股票账户名,
                                                期货账号'）
orderCode            string      下单代码          1. 如果是单股或单期货、港股，则该参数填合约代码；
                                               2. 如果是组合交易, 则该参数填篮子名称，参考组合交易在新窗口打开；
                                               3. 如果是组合套利，则填一个篮子名和一个期货合约名（如orderCode = '篮子名,
                                                期货合约名'），请参考组合套利交易在新窗口打开
prType               int         下单选价类型        可选值参考prType-下单选价类型在新窗口打开

                                               特别的对于套利，这个 prType 只对篮子起作用，期货的采用默认的方式）
price                float       下单价格          一、单股下单时，prType 是模型价/科创板盘后定价时 price 有效；其它情况无效；

                                               1.1 即单股时， prType 参数为 11，49 时被使用。

                                               1.2 prType 参数不为 11，49 时也需填写，填写的内容可为 -1，0，2，100 等任意数字；

                                               二、组合下单时，是组合套利时，price 作套利比例有效，其它情况无效。
volume               int         下单数量（股 / 手 /  根据 orderType 值最后一位确定 volume 的单位，可选值参考volume - 下单在新窗口打开
                                  元 /
                                  %）
strategyName         string      自定义策略名

                                               一、用来区分 order 委托和deal 成交来自不同的策略。

                                               根据该策略名，get_trade_detail_data，get_last_order_id
                                                函数可以获取相应策略名对应的委托或成交集合。

                                               strategyName 只对同账号本地客户端有效，即 strategyName
                                                只对当前客户端下的单进行策略区分，且该策略区分只能当前客户端使用。
quickTrade           int         设定是否立即触发下单

                                               可选值参考quicktrade - 快速下单在新窗口打开

                                               passorder是对最后一根K线完全走完后生成的模型信号在下一根K线的第一个tick数据来时触发下单交易；

                                               采用quickTrade参数设置为1时，非历史bar上执行时（ContextInfo.is_last_bar(
                                               )为True），只要策略模型中调用到就触发下单交易。

                                               quickTrade参数设置为2时，不判断bar状态，只要策略模型中调用到就触发下单交易，历史bar上也能触发
                                               下单，请谨慎使用。
userOrderId          string      用户自设委托 ID     如果传入该参数，
                                               则 strategyName 和 quickTrade 参数也填写。
                                               对应 order 委托对象和 deal 成交对象中的 m_strRemark 属性，通过
                                                get_trade_detail_data 函数或委托主推函数 order_callback
                                                和成交主推函数 deal_callback
                                                可拿到这两个对象信息。
ContextInfo          class       系统参数          含有k线信息和接口的上下文对象
返回：
无
更多示例：
1. 股票在新窗口打开
2. 基金在新窗口打开
3. 两融在新窗口打开
4. 期货在新窗口打开
5. 期权在新窗口打开
6. 新股申购在新窗口打开
7. 债券在新窗口打开
8. ETF在新窗口打开
9. 组合交易在新窗口打开
10. 组合套利交易在新窗口打开
#algo_passorder - 算法下单（拆单）函数
用于按固定时间间隔和固定规则把目标交易数量拆分成多次下单的交易函数
调用用法：
python
algo_passorder(opType,orderType,accountid,orderCode,prType,price,volume,[strategyName,quickTrade,userOrderId,userOrderParam],ContextInfo)`
提示
算法交易下单，此时使用交易面板-程序交易-函数交易-函数交易参数中设置的下单类型(普通交易,算法交易,随机量交易) 如果函数交易参数使用未修改的默认值,此函数和passorder函数一致， 设置了函数交易参数后，将会使用函数交易参数的超价等拆单参数，algo_passorder内的prType若赋值,则优先使用该参数，若algo_passorder内的prType=-1,将会使用userOrderParam内的opType，若userOrderParam未赋值，则使用界面上的函数交易参数的报价方式
参数：
其他参数同passorder，详细解释可参考passorder的说明
userOrderParam dict[str:value] 是用户自定义交易参数,主要用于修改算法交易的参数 其中Key Value定义如下
注：所有参数均为非必选
             Key                   Value类型                             Value
OrderType                     int              普通交易:0
                                               算法交易:1
                                               随机量交易:2
PriceType                     int              报价方式:数值同passorde prType
MaxOrderCount                 int              最大下单次数
SinglePriceRange              int              波动区间是否单向:
                                               否:0，
                                               是:1
PriceRangeType                int              波动区间类型按比例:0,按数值1
PriceRangeValue               float            波动区间(按数值)
PriceRangeRate                float            波动区间(按比例)[0-1]
SuperPriceType                int              单笔超价类型:
                                               按比例:0
                                               按数值1
SuperPriceRate                float            单笔超价(按比例)[0-1]
SuperPriceValue               float            单笔超价(按数值)
VolumeType                    int              单笔基准量类型卖1+2+3+4+5量:0
                                               卖1+2+3+4量:1
                                               ...
                                               卖1量:4
                                               买1量:5
                                               ...
                                               买1+2+3+4+5量:9
                                               目标量:10
                                               目标剩余量:11
                                               持仓数量:12
VolumeRate                    float            单笔下单比率[0-1]
SingleNumMin                  float            单笔下单量最小值
SingleNumMax                  float            单笔下单量最大值
ValidTimeType                 int              有效时间类型:
                                               0:按持续时间
                                               1 按时间区间，默认为0
ValidTimeElapse               int              有效持续时间,ValidTimeType设置为0时生效
ValidTimeStart                int              有效开始时间偏移,ValidTimeType设置为1时生效
ValidTimeEnd                  int              有效结束时间偏移,ValidTimeType设置为1时生效
UndealtEntrustRule            int              未成委托处理数值同prType
PlaceOrderInterval            int              下撤单时间间隔
UseTrigger                    int              是否触价:
                                               否:0
                                               是:1
TriggerType                   int              触价类型:
                                               最新价大于:1
                                               最新价小于:2
TriggerPrice                  float            触价价格
SuperPriceEnable              int              超价启用笔数
返回
无
示例
python
#coding:gbk
userparam = {
    "OrderType": 1,
    "MaxOrderCount": 20,
    "SuperPriceType": 1,
    "SuperPriceValue": 1.12}
accid = '918800000818'  #资金账号
algo_passorder(23,1101,accid,'000001.SZ',5,15,1000,'',1,'strReMark',userparam,ContextInfo)
#表示修改算法交易的最大委托次数为20,单笔下单基准类型为按价格类型超价,单笔超价1.12元,其他参数同函数交易参数中设置
#smart_algo_passorder - 智能算法（VWAP 等）函数
提示
1. 调用该函数需要有【智能算法】使用权限
用于使用主动算法或被动算法交易的函数如VWAP TWAP等
调用方法一：
python
smart_algo_passorder(opType,orderType,accountid,orderCode,prType,price,volume,strageName,quickTrade,userOrderId,smartAlgoType,limitOverRate,minAmountPerOrder,[targetPriceLevel,startTime,endTime,limitControl],ContextInfo)
提示
可选参数可缺省
参数：
其他参数同passorder，详细解释可参考passorder的说明在新窗口打开
            参数名                类型                      说明                              提示
prType                      int     可选值：
                                    11:限价（只对单股情况支持,对组合交易不支持）
                                    12:市价
                                    特别的对于套利：这个prType只对篮子起作用，期货的采用默认的方式
smartAlgoType               str     智能算法类型
                                     [enum_constants#smartAlgoType智能算法类型在新窗口打
                                    开]
limitOverRate               int     量比 数据范围0-100                             网格算法无此项
                                                                             若在algoParam中填写量比，则填写范围
                                                                             0-1的小数。
minAmountPerOrder           int     智能算法最小委托金额，数据范围0-100000
targetPriceLevel            int     智能算法目标价格,可选值：                            一、输入无效值则targetPriceLev
                                    1：己方盘口 1                                 el为1
                                    2：己方盘口2                                  二、本项只针对冰山算法,其他算法可缺省。
                                    3：己方盘口3
                                    4：己方盘口4
                                    5：己方盘口5
                                    6：最新价
                                    7：对方盘口
startTime                   str     智能算法开始时间                                 格式"HH:MM:SS"，如"10:30:0
                                                                             0"。如果缺省值，则默认为"09:30:00
                                                                             "
endTime                     str     智能算法截止时间                                 格式"HH:MM:SS"，如"14:30:0
                                                                             0"。如果缺省值，则默认为"15:30:00
                                                                             "
limitControl                int     涨跌停控制                                    默认值为1
                                                                             1：涨停不卖跌停不卖
                                                                             0：无限制
返回
无
示例：
python
#coding:gbk


def init(ContextInfo):
    pass


def after_init(ContextInfo):
    # # 使用smart_algo_passorder 下单
    smart_algo_passorder(
        23,                # 买入
        1101,              # 表示volume的单位是股
        account,           # 资金账号
        '000001.SZ',
        12,                #  11限价，12市价
        0,                 # 限价时，价格填任意数量占位
        50000,             # 5000股
        '',
        2,                 # quickTrade
        '',
        'VWAP',
        25,                 # 量比25%
        0,                  # 智能算法最小委托金额
        1,                  # 智能算法目标价格 本项只针对冰山算法,其他算法可缺省。
        "10:25:00",         # 开始时间
        "14:50:00",         # 结束时间
        1,                  # 涨跌停控制 1为涨停不卖跌停不卖 0 为无限制
        ContextInfo
        )
调用方法二：
当时用algoParam时，函数声明为：smart_algo_passorder(opType,orderType,accountid,orderCode,prType,modelprice,volume,strageName,quickTrade,userid,smartAlgoType,startTime,endTime,algoParam,ContextInfo)参数均不可缺省
smartAlgoType,startTime,endTime 含义同上，algoParam请使用下面的方法获取：
#获取algoParam具体字段
释义
获取智能算法参数配置信息
用法
python
get_smart_algo_param(algoList)
参数
      参数          类型                                          说明
algoList      list       需要查询参数配置信息的算法名称列表, 若传空则查询全部有权限的算法参数配置信息
返回
返回一个字典，键为算法名称，值为参数字典列表。
             字段                    类型                                     说明
key                          string        参数名称key值,即smart_algo_order中algoList字典需要传的键值
name                         string        参数名称
dataType                     string        参数类型
valueRange                   string        参数范围
defaultValue                 string        参数默认值
enumName                     string        参数枚举值的名称
enumValue                    string        参数实际的枚举值
unit                         string        参数的单位, 当单位为%时, 值要填写小数而非参数范围所示的百分数值
valueRangeByName             string        不同算法参数范围
defaultValueByName           string        不同算法参数默认值
示例
python
#coding:gbk


def init(ContextInfo):
    pass

    # 方法2 使用algoParam 和smart_algo_passorder
    # 该方法部分旧版本客户端可能会不支持
    # algoParam
    # 先获取所有需要传入的参数
    #
    print(get_smart_algo_param(['VWAP']))
    '''
    输出：[2024-01-30 11:21:10][智能算法1][SH000300][日线] 
    {'VWAP': [
        {'key': 'm_dLimitOverRate', 'name': '量比比例', 'dataType': '浮点数', 'valueRange': '0.00-100.00', 'defaultValue': '20.00', 'enumName': '', 'enumValue': '', 'unit': '%', 'valueRangByName': '', 'defaultValueByName': ''}, 
        {'key': 'm_dMinAmountPerOrder', 'name': '委托最小金额', 'dataType': '整数', 'valueRange': '0-100000', 'defaultValue': '0', 'enumName': '', 'enumValue': '', 'unit': '', 'valueRangByName': '', 'defaultValueByName': ''},
        {'key': 'm_dMaxAmountPerOrder', 'name': '委托最大金额', 'dataType': '浮点数', 'valueRange': '0.00-*********.00', 'defaultValue': '0', 'enumName': '', 'enumValue': '', 'unit': '', 'valueRangByName': '', 'defaultValueByName': ''}, 
        {'key': 'm_nStopTradeForOwnHiLow', 'name': '涨跌停控制', 'dataType': '整数', 'valueRange': '', 'defaultValue': '涨停不卖跌停不买', 'enumName': '无,涨停不卖跌停不买', 'enumValue': '0,1', 'unit': '', 'valueRangByName': '', 'defaultValueByName': ''}, 
        {'key': 'm_dMulitAccountRate', 'name': '多账号总量比', 'dataType': '浮点数', 'valueRange': '0.00-100.00', 'defaultValue': '0', 'enumName': '', 'enumValue': '', 'unit': '%', 'valueRangByName': '', 'defaultValueByName': ''}, 
        {'key': 'm_strCmdRemark', 'name': '投资备注', 'dataType': '字符串', 'valueRange': '', 'defaultValue': '', 'enumName': '', 'enumValue': '', 'unit': '', 'valueRangByName': '', 'defaultValueByName': ''}]}
    '''
    algoParam={
    'm_dLimitOverRate': 0.25,      # 量比 25%
    'm_dMinAmountPerOrder':0,      # 委托最小金额
    'm_dMaxAmountPerOrder':10000,  # 委托最大金额
    'm_nStopTradeForOwnHiLow': 1,  # 涨跌停控制
    'm_dMulitAccountRate':0.30,    # 多账号总量比
    'm_strCmdRemark':  '投资备注1'  # 投资备注
    }
    smart_algo_passorder(
        23,
        1101,
        account,
        '600000.SH',
        12,
        0,
        10000,
        '',
        2,               # quickTrade
        '投资备注',
        'VWAP',
        "10:25:00",      # 开始时间
        "14:50:00",      # 结束时间
        algoParam,       # 算法参数
        ContextInfo
        ) 
    
#cancel-撤销委托
调用方法cancel(orderId, accountId, accountType, ContextInfo)
参数
         参数名               类型                             含义                            说明
orderId               string       委托号                                              必填
accountID             string       资金账号                                             必填
AccountType           string       账号类型 可选：                                         必填
                                   'FUTURE'：期货
                                   'STOCK'：股票
                                   'CREDIT'：信用
                                   'HUGANGTONG'：沪港通
                                   'SHENGANGTONG'：深港通
                                   'STOCK_OPTION'：期权
ContextInfo           class        含有k线信息和接口的上下文对象                                  必填
返回 bool，是否发出了取消委托信号，返回值含义：
True：是
False：否
示例
python返回值
#coding:gbk
'''
（1）下单前,根据 get_trade_detail_data 函数返回账号的信息，判定资金是否充足，账号是否在登录状态，统计持仓情况等等。
（2）满足一定的模型条件，用 passorder 下单。
（3）下单后，时刻根据 get_last_order_id 函数获取委托和成交的最新id，注意如果委托生成了，就有了委托号（这个id需要自己保存做一个全局控制）。
（4）用该委托号根据 get_value_by_order_id 函数查看委托的状态，各种情况等。
当一个委托的状态变成“已成'后，那么对应的成交 deal 信息就有一条成交数据；用该委托号可查看成交情况。
*注：委托列表和成交列表中的委托号是一样的,都是这个 m_strOrderSysID 属性值。
可用 get_last_order_id 获取最新的 order 的委托号,然后根据这个委托号获取 deal 的信息，当获取成功后，也说明这笔交易是成了，可再根据 position 持仓信息再进一步验证。
（5）根据委托号获取委托信息，根据委托状态，或模型设定，用 cancel 取消委托。
'''


def init(ContextInfo):
    ContextInfo.accid = '**********'

def handlebar(ContextInfo):
    if ContextInfo.is_last_bar():
        orderid = get_last_order_id(ContextInfo.accid, 'stock', 'order')
        print(cancel(orderid, ContextInfo.accid, 'stock', ContextInfo))
#cancel_task - 撤销任务
调用方法cancel_task(taskId,accountId,accountType,ContextInfo)
参数
         参数名               类型                             含义                            说明
taskId                string       委托号                                              必填
accountID             string       资金账号                                             必填
AccountType           string       账号类型 可选：                                         必填
                                   'FUTURE'：期货
                                   'STOCK'：股票
                                   'CREDIT'：信用
                                   'HUGANGTONG'：沪港通
                                   'SHENGANGTONG'：深港通
                                   'STOCK_OPTION'：期权
ContextInfo           class        含有k线信息和接口的上下文对象                                  必填
返回 bool，是否发出了撤销任务信号，返回值含义：
True：是
False：否
示例
python
#coding:gbk
'''
（1）根据get_trade_detail_data函数返回任务的信息,获取任务编号（m_nTaskId），任务状态等等；
（2）根据任务编号，用cancel_task取消委托。
'''

def init(ContextInfo):
    ContextInfo.accid = '**********'

def handlebar(ContextInfo):
    # 获取当前客户端所有的任务
    if ContextInfo.is_last_bar():
        objlist = get_trade_detail_data(ContextInfo.accid,'stock','task')
        for obj in objlist:
            cancel_task(str(obj.m_nTaskId),ContextInfo.accid,'stock',ContextInfo)
#pause_task - 暂停任务
暂停智能算法任务
调用方法 pause_task(taskId,accountId,accountType,ContextInfo)
参数
         参数名               类型                             含义                            说明
taskId                string       委托号                                              必填
accountID             string       资金账号                                             必填
AccountType           string       账号类型 可选：                                         必填
                                   'FUTURE'：期货
                                   'STOCK'：股票
                                   'CREDIT'：信用
                                   'HUGANGTONG'：沪港通
                                   'SHENGANGTONG'：深港通
                                   'STOCK_OPTION'：期权
ContextInfo           class        含有k线信息和接口的上下文对象                                  必填
返回 bool，是否发出了暂停任务信号，返回值含义：
True：是
False：否
示例
python
#coding:gbk
'''
（1）根据get_trade_detail_data函数返回任务的信息,获取任务编号（m_nTaskId），任务状态等等；
（2）根据任务编号，用pause_task暂停智能算法任务。
'''

def init(ContextInfo):
    ContextInfo.accid = '**********'    

def handlebar(ContextInfo):
    
    if ContextInfo.is_last_bar():
        # 获取当前客户端所有的任务
        objlist = get_trade_detail_data(ContextInfo.accid,'stock','task')
        for obj in objlist:
            pause_task(obj.m_nTaskId,ContextInfo.accid,'stock',ContextInfo)
#resume_task - 继续任务
继续智能算法任务
调用方法resume_task(taskId,accountId,accountType,ContextInfo)
参数
         参数名               类型                             含义                            说明
taskId                string       委托号                                              必填
accountID             string       资金账号                                             必填
AccountType           string       账号类型 可选：                                         必填
                                   'FUTURE'：期货
                                   'STOCK'：股票
                                   'CREDIT'：信用
                                   'HUGANGTONG'：沪港通
                                   'SHENGANGTONG'：深港通
                                   'STOCK_OPTION'：期权
ContextInfo           class        含有k线信息和接口的上下文对象                                  必填
返回 bool，是否发出了重启任务信号，返回值含义：
True：是
False：否
示例
python
#coding:gbk
'''
（1）根据get_trade_detail_data函数返回任务的信息,获取任务编号（m_nTaskId），任务状态等等；
（2）根据任务编号，用resume_task启动已暂停智能算法任务。
'''

def init(ContextInfo):
    ContextInfo.accid = '**********'    
def handlebar(ContextInfo):
    if ContextInfo.is_last_bar():
        # 获取当前客户端所有的任务
        objlist = get_trade_detail_data(ContextInfo.accid,'stock','task')
        for obj in objlist:
            resume_task(obj.m_nTaskId,ContextInfo.accid,'stock',ContextInfo)
#get_basket-获取股票篮子
用法： get_basket(basketName)
释义： 获取股票篮子
参数：
· basketName：股票篮子名称
示例：
print( get_basket('basket1') )
#set_basket-设置股票篮子
用法： set_basket(basketDict)
释义： 设置passorder的股票篮子,仅用于passorder进行篮子交易,设置成功后,用get_basket可以取出后即可进行passorder组合交易下单
参数：
· basketDict：股票篮子 {'name':股票篮子名称,'stocks':[{'stock':股票名称,'weight',权重,'quantity':数量,'optType':交易类型}]} 。
示例：
table=[
    {'stock':'600000.SH','weight':0.11,'quantity':100,'optType':23},
    {'stock':'600028.SH','weight':0.11,'quantity':200,'optType':24},
]
basket={'name':'basket1','stocks':table}
set_basket(basket)
#一键买卖2份(2101代表用篮子里quantity字段)basket1里面的股票组合，即600000.SH买入200股，600028.SH卖出400股
passorder(35,2101,ContextInfo.accid,'basket1',5,-1,2,'basketOrder',2,'basketOrder',ContextInfo)
#交易查询函数
#get_trade_detail_data-查询账号资金信息、委托记录等
调用方法 get_trade_detail_data(accountID, strAccountType, strDatatype, strategyName)
或不区分策略
get_trade_detail_data(accountID, strAccountType, strDatatype)
参数
           参数名                 类型                         说明                             备注
accountID                 string       资金账号                                    必填
strAccountType            string       账号类型 可选：                                必填
                                       'FUTURE'：期货
                                       'STOCK'：股票
                                       'CREDIT'：信用
                                       'HUGANGTONG'：沪港通
                                       'SHENGANGTONG'：深港通
                                       'STOCK_OPTION'：期权
strDatatype               string       要查询数据类型 可选：                             必填
                                       ACCOUNT：账号对象在新窗口打开或信用账号对象在新窗口打开
                                       POSITION：持仓在新窗口打开
                                       POSITION_STATISTICS：持仓统计在新窗口打开
                                       ORDER：委托在新窗口打开
                                       DEAL ：成交在新窗口打开
                                       TASK：任务在新窗口打开
strategyName              string       策略 当用passorder下单时指定了strategyName        strategyName参数只对成交和委托有
                                        参数时，当查询成交和委托时传入同样的strageName，则可以只返回包含st效,选填
                                       rategyName的委托子集或成交子集
返回 list，list 中放的是对应strDatatype的 Python对象，通过 dir(pythonobj) 可返回某个对象的属性列表。
有五种交易相关信息，包括：
ACCOUNT：账号对象在新窗口打开或信用账号对象在新窗口打开
POSITION：持仓明细在新窗口打开
POSITION_STATISTICS: 持仓统计在新窗口打开
ORDER：委托在新窗口打开
DEAL：成交在新窗口打开
TASK：任务在新窗口打开
示例：
python返回值

#coding:gbk

account = '800174' # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值；编译器环境里执行的下单函数不会产生实际委托

def init(ContextInfo):
    pass

def handlebar(ContextInfo):
    if not ContextInfo.is_last_bar():
        return
    
    orders = get_trade_detail_data(account, 'stock', 'order')
    print('查询委托结果：')
    for o in orders:
        print(f'股票代码: {o.m_strInstrumentID}, 市场类型: {o.m_strExchangeID}, 证券名称: {o.m_strInstrumentName}, 买卖方向: {o.m_nOffsetFlag}',
        f'委托数量: {o.m_nVolumeTotalOriginal}, 成交均价: {o.m_dTradedPrice}, 成交数量: {o.m_nVolumeTraded}, 成交金额:{o.m_dTradeAmount}')


    deals = get_trade_detail_data(account, 'stock', 'deal')
    print('查询成交结果：')
    for dt in deals:
        print(f'股票代码: {dt.m_strInstrumentID}, 市场类型: {dt.m_strExchangeID}, 证券名称: {dt.m_strInstrumentName}, 买卖方向: {dt.m_nOffsetFlag}', 
        f'成交价格: {dt.m_dPrice}, 成交数量: {dt.m_nVolume}, 成交金额: {dt.m_dTradeAmount}')

    positions = get_trade_detail_data(account, 'stock', 'position')
    print('查询持仓结果：')
    for dt in positions:
        print(f'股票代码: {dt.m_strInstrumentID}, 市场类型: {dt.m_strExchangeID}, 证券名称: {dt.m_strInstrumentName}, 持仓量: {dt.m_nVolume}, 可用数量: {dt.m_nCanUseVolume}',
        f'成本价: {dt.m_dOpenPrice:.2f}, 市值: {dt.m_dInstrumentValue:.2f}, 持仓成本: {dt.m_dPositionCost:.2f}, 盈亏: {dt.m_dPositionProfit:.2f}')


    accounts = get_trade_detail_data(account, 'stock', 'account')
    print('查询账号结果：')
    for dt in accounts:
        print(f'总资产: {dt.m_dBalance:.2f}, 净资产: {dt.m_dAssureAsset:.2f}, 总市值: {dt.m_dInstrumentValue:.2f}', 
        f'总负债: {dt.m_dTotalDebit:.2f}, 可用金额: {dt.m_dAvailable:.2f}, 盈亏: {dt.m_dPositionProfit:.2f}')
    
    position_statistics = get_trade_detail_data(account,"FUTURE",'POSITION_STATISTICS')
    for obj in position_statistics:
        if obj.m_nDirection == 49:
			continue
		PositionInfo_dict[obj.m_strInstrumentID+"."+obj.m_strExchangeID]={
		"持仓":obj.m_nPosition,
		"成本":obj.m_dPositionCost,
		"浮动盈亏":obj.m_dFloatProfit,
		"保证金占用":obj.m_dUsedMargin
		}
	print(PositionInfo_dict)

	
#get_history_trade_detail_data - 查询历史交易明细
用法： get_history_trade_detail_data(accountID,strAccountType,strDatatype,strStratDate,strEndDate);
释义： 获取历史成交明细数据，返回结果为一个([timetag,obj...])的元组
参数：
accountID：string,账号； strAccountType：string,账号类型,有"FUTURE","STOCK","CREDIT","HUGANGTONG","SHENGANGTONG","STOCK_OPTION"； strDatatype：string,交易明细数据类型,有：持仓"POSITION"、委托"ORDER"、成交"DEAL"； strStratDate：string,开始时间,如'********'； strEndDate：string,结束时间,如'********'；
**返回：**list,list中放的是PythonObj,通过dir(pythonobj)可返回某个对象的属性列表 示例：
示例
def handlebar(ContextInfo):
    obj_list = get_history_trade_detail_data('**********','stock','position','********','********')
    for time,data in obj_list:
        for obj in data:
            print(obj.m_strInstrumentID)
            print(dir(obj))#查看有哪些属性字段
#get_ipo_data-获取当日新股新债信息
用法： get_ipo_data([,type])
释义： 获取当日新股新债信息，返回结果为一个字典,包括新股申购代码,申购名称,最大申购数量,最小申购数量等数据
参数：
· type：为空时返回新股新债信息，type="STOCK"时只返回新股申购信息，type="BOND"时只返回新债申购信息
示例：
#coding:gbk
def init(ContextInfo):
    ipoData=get_ipo_data()# 返回新股新债信息
    ipoStock=get_ipo_data("STOCK")# 返回新股信息
    ipoCB=get_ipo_data("BOND")# 返回新债申购信息
#get_new_purchase_limit-获取账户新股申购额度
用法： get_new_purchase_limit(accid)
释义： 获取账户新股申购额度，返回结果为一个字典,包括上海主板,深圳市场,上海科创版的申购额度
参数：
· accid：资金账号，必须时股票账号或者信用账号
示例：
def init(ContextInfo):
    ContextInfo.accid="********"# 返回新股新债信息
    purchase_limit=get_new_purchase_limit(ContextInfo.accid)
#get_value_by_order_id-根据委托号获取委托或成交信息
调用方法get_value_by_order_id(orderId, accountID, strAccountType, strDatatype)
参数
           参数名                类型                          含义                        说明
orderId                  string       委托号                                       必填
accountID                string       资金账号                                      必填
strAccountType           string       账号类型 可选：                                  必填
                                      'FUTURE'：期货
                                      'STOCK'：股票
                                      'CREDIT'：信用
                                      'HUGANGTONG'：沪港通
                                      'SHENGANGTONG'：深港通
                                      'STOCK_OPTION'：期权
strDatatype              string       要查询数据类型 可选：                               必填
                                      'ORDER'：委托
                                      'DEAL' ：成交
返回
委托对象 或 成交对象
示例
python返回值
def init(ContextInfo):
    ContextInfo.accid = '**********'

def handlebar(ContextInfo):
    orderid = get_last_order_id(ContextInfo.accid, 'stock', 'order')
    print(orderid)
    obj = get_value_by_order_id(orderid,ContextInfo.accid, 'stock', 'order')
    print(obj.m_strInstrumentID)
调用方法
python
# 区分策略，添加策略名称参数 strategyName
get_last_order_id(accountID, strAccountType, strDatatype, strategyName)

# 不区分策略
get_last_order_id(accountID, strAccountType, strDatatype)
参数
           参数名                类型                               含义                            说明
accountID                string       资金账号                                                必填
strAccountType           string       账号类型 可选：                                            必填
                                      'FUTURE'：期货
                                      'STOCK'：股票
                                      'CREDIT'：信用
                                      'HUGANGTONG'：沪港通
                                      'SHENGANGTONG'：深港通
                                      'STOCK_OPTION'：期权
strDatatype              string       要查询数据类型 可选：                                         必填
                                      'ORDER'：委托
                                      'DEAL' ：成交
strategyName             string       策略 当用passorder下单时指定了strategyName                    选填
                                       参数时，当查询成交和委托时传入同样的strageName，则可以只返回包含strategyName的委
                                      托子集或成交子集
返回
String，委托号，如果没找到返回 '-1'。
示例
def init(ContextInfo):
    ContextInfo.accid = '**********'

def handlebar(ContextInfo):
    orderid = get_last_order_id(ContextInfo.accid, 'stock', 'order')
    print(orderid)
    obj = get_value_by_order_id(orderid,ContextInfo.accid, 'stock', 'order')
    print(obj.m_strInstrumentID)
#get_assure_contract-获取两融担保标的明细
用法： get_assure_contract(accId)
释义： 获取信用账户担保合约明细
参数：
· accId：信用账户
返回： list，list 中放的是 StkSubjects在新窗口打开，通过 dir(pythonobj) 可返回某个对象的属性列表。
示例：
python

def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def handlebar(ContextInfo): 
    obj = get_assure_contract('**********')
    for i in obj[:3]:
		print(show_data(i))


"""
{'m_dAssureRatio': 0.0, # 担保品折算比例
'm_dFinRatio': 0.8, # 融资保证金比例
'm_dSloRatio': 1.0,  # 融券保证金比例
'm_eAssureStatus': 50,  # 是否可做担保
'm_eCreditFundCtl': 50, # 融资交易控制
'm_eCreditStkCtl': 50, # 融券交易控制
'm_eFinStatus': 48, # 融资状态
'm_eSloStatus': 48, # 融券状态
'm_nPlatformID': 10064,  # 平台号
'm_strAccountID': '********',  # 资金账号
'm_strBrokerID': '003', # 经纪公司编号
'm_strBrokerName': '光大证券信用',  # 证券公司
'm_strExchangeID': 'SH', # 交易所
'm_strInstrumentID':'510150' # 证券代码
"""
        
#get_enable_short_contract-获取可融券明细
提示
注:由于字段m_dSloRatio、m_dSloStatus提供来源和取担保品明细(get_assure_contract)重复，字段在2021年9月移除，后续用担保品明细接口获取,具体见 担保标的对象字段说明在新窗口打开
用法： get_enable_short_contract(accId)
释义： 获取信用账户当前可融券的明细
参数：
· accId：信用账户
返回： list，list 中放的是 CreditSloEnableAmount在新窗口打开，通过 dir(pythonobj) 可返回某个对象的属性列表。
示例：
python

def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def handlebar(ContextInfo):
    obj = get_enable_short_contract('**********')
    for i in obj[:3]:
		print(show_data(i))

"""
Rerutn:

{'m_eQuerySloType': 48, # 查询类型
'm_nEnableAmount': 0,  # 融券可融数量
'm_nPlatformID': 10064,  # 平台号
'm_strAccountID': '********',  # 资金账号
'm_strBrokerID': '003',  # 经纪公司编号
'm_strBrokerName': '光大证券信用',  # 证券公司
'm_strExchangeID': 'SH', # 标的市场
'm_strInstrumentID': '688321' # 证券代码
}

"""

#query_credit_account - 查询信用账户明细
注意
1. 本函数一次最多查询200只股票的两融最大下单量，且同时只能有一个查询,如果前面的查询正在进行中,后面的查询将会提前返回。本函数从服务器查询数据,建议平均查询时间间隔180s一次,不可频繁调用。
2. 该函数必须配合credit_account_callback回调才能使用，关于此回调的说明请看credit_account_callback在新窗口打开
3. callback返回的对象是CCreditAccountDetail在新窗口打开
调用query_credit_account，该接口的查询结果将会推送给credit_account_callback，所以程序里需要按照函数参数实现函数credit_account_callback,callback返回的对象是CCreditAccountDetail在新窗口打开
用法： query_credit_account(accountId,seq,ContextInfo)
释义： 查询信用账户明细。本函数只能有一个查询，如果前面的查询正在进行中，后面的查询将会提前返回。
参数：
· accountId：string，查询的两融账号
· seq：int，查询序列号，建议输入唯一值以便对应结果回调
示例：
python返回值
#coding:gbk


import time

def init(ContextInfo):
	ContextInfo.accid='200133'
	
def handlebar(ContextInfo):
	if ContextInfo.is_last_bar():
		query_credit_account(ContextInfo.accid,int(time.time()),ContextInfo)
# 该函数必须配合credit_account_callback回调才能使用
def credit_account_callback(ContextInfo,seq,result):
	print(seq)
	print(f":维持担保比例:{result.m_dPerAssurescaleValue:.2f},总负债:{result.m_dTotalDebt:.2f}")

回调示例 见query_credit_account在新窗口打开
#query_credit_opvolume - 查询两融最大可下单量
注意
1. 本函数一次最多查询200只股票的两融最大下单量，且同时只能有一个查询,如果前面的查询正在进行中,后面的查询将会提前返回。本函数从服务器查询数据,建议平均查询时间间隔180s一次,不可频繁调用。
2. 该函数必须配合credit_opvolume_callback回调才能使用,关于此回调的说明请看credit_account_callback在新窗口打开
调用query_credit_opvolume，该接口的查询结果将会推送给credit_opvolume_callback，所以必须配合credit_opvolume_callback回调才能使用
用法： query_credit_opvolume(accountId,stockCode,opType,prType,price,seq,ContextInfo)
释义： 查询两融最大可下单量。
参数：
· accountId:查询的两融账号
· stockCode:需要查询的股票代码,stockCode为List的类型,可以查询多只股票
· opType:两融下单类型,同passorder的下单类型
· prType:报单价格类型,同passorder的报价类型
· seq:查询序列号,int型，建议输入唯一值以便对应结果回调
· price:报价(非限价单可以填任意值),如果stockCode为List类型,报价也需要为长度相同的List
· ContextInfo:ContextInfo类
示例：
python返回值
#coding:gbk

import time

def init(ContextInfo):
	ContextInfo.accid='200133'
	
def handlebar(ContextInfo):
	if ContextInfo.is_last_bar():
        #查询accid账号担保品买入600000,SH限价10元的最大可下单量
		query_credit_opvolume(ContextInfo.accid,'600000.SH',33,11,10,int(time.time()),C) # 查询两融最大可下单量。
		time.sleep(0.5)
        #查询accid账号担保品买入600000,SH限价10元,000001.SZ担保品买入限价20元的最大可下单量
		query_credit_opvolume(ContextInfo.accid,["600000.SH","000001.SZ"],33,11,[10,20],int(time.time()),C) # 查询两融最大可下单量。

# 该函数必须配合credit_opvolume_callback回调才能使用
def credit_opvolume_callback(ContextInfo,accid,seq,ret,result):
	print(seq)
	print(f'查询结果:{ret}') # 正常返回:1,正在查询中-1,输入账号非法:-2,输入查询参数非法:-3,超时等服务器返回报错:-4
	print(result)


#get_option_subject_position-取期权标的持仓
用法： get_option_subject_position(accountID)
释义： 取期权标的持仓
参数：
· accountID：string,账号
返回： list,list中放的是CLockPosition在新窗口打开,通过dir(pythonobj)可返回某个对象的属性列表
示例：
data=get_option_subject_position('************')
print(len(data));
forobjindata:
    print(obj.m_strInstrumentName,obj.m_lockVol,obj.m_coveredVol);
#get_comb_option-取期权组合持仓
用法： get_comb_option(accountID)
释义： 取期权组合持仓
参数：
· accountID：string,账号
返回： list,list中放的是CStkOptCombPositionDetail 在新窗口打开,通过dir(pythonobj)可返回某个对象的属性列表
示例：
obj_list=get_comb_option('************')
print(len(obj_list));
forobjinobj_list:
    print(obj.m_strCombCodeName,obj.m_strCombID,obj.m_nVolume,obj.m_nFrozenVolume)
#get_unclosed_compacts-获取未了结负债合约明细
用法： get_unclosed_compacts(accountID,accountType)
释义： 获取未了结负债合约明细
参数：
· accountID：str，资金账号
· accountType：str，账号类型，这里应该填'CREDIT'
返回：
list([ CStkUnclosedCompacts, ... ]) 负债列表，CStkUnclosedCompacts属性如下：
                字段名称                       类型                        说明
m_strAccountID                       string        账号ID
m_nBrokerType                        int           账号类型
                                                   1-期货账号
                                                   2-股票账号
                                                   3-信用账号
                                                   5-期货期权账号
                                                   6-股票期权账号
                                                   7-沪港通账号
                                                   11-深港通账号
m_strExchangeID                      string        市场
m_strInstrumentID                    string        证券代码
m_eCompactType                       int           合约类型
                                                   32-不限制
                                                   48-融资
                                                   49-融券
m_eCashgroupProp                     int           头寸来源
                                                   32-不限制
                                                   48-普通头寸
                                                   49-专项头寸
m_nOpenDate                          int           开仓日期(如'20201231')
m_nBusinessVol                       int           合约证券数量
m_nRealCompactVol                    int           未还合约数量
m_nRetEndDate                        int           到期日(如'20201231')
m_dBusinessBalance                   float         合约金额
m_dBusinessFare                      float         合约息费
m_dRealCompactBalance                float         未还合约金额
m_dRealCompactFare                   float         未还合约息费
m_dRepaidFare                        float         已还息费
m_dRepaidBalance                     float         已还金额
m_strCompactId                       string        合约编号
m_strEntrustNo                       string        委托编号
m_nRepayPriority                     int           偿还优先级
m_strPositionStr                     string        定位串
m_eCompactRenewalStatus              int           合约展期状态
                                                   48-可申请
                                                   49-已申请
                                                   50-审批通过
                                                   51-审批不通过
                                                   52-不可申请
                                                   53-已执行
                                                   54-已取消
m_nDeferTimes                        int           展期次数
示例：
get_unclosed_compacts('**********', 'CREDIT')
#get_closed_compacts-获取已了结负债合约明细
用法： get_closed_compacts(accountID,accountType)
释义： 获取已了结负债合约明细
参数：
· accountID：str，资金账号
· accountType：str，账号类型，这里应该填'CREDIT'
返回：
list([ CStkUnclosedCompacts, ... ]) 负债列表，CStkUnclosedCompacts属性如下：
             字段名                   类型                        描述
m_strAccountID               string        账号ID
m_nBrokerType                int           账号类型
                                           1-期货账号
                                           2-股票账号
                                           3-信用账号
                                           5-期货期权账号
                                           6-股票期权账号
                                           7-沪港通账号
                                           11-深港通账号
m_strExchangeID              string        市场
m_strInstrumentID            string        证券代码
m_eCompactType               int           合约类型
                                           32-不限制
                                           48-融资
                                           49-融券
m_eCashgroupProp             int           头寸来源
                                           32-不限制
                                           48-普通头寸
                                           49-专项头寸
m_nOpenDate                  int           开仓日期(如'20201231')
m_nBusinessVol               int           合约证券数量
m_nRetEndDate                int           到期日(如'20201231')
m_nDateClear                 int           了结日期(如'20201231')
m_nEntrustVol                int           委托数量
m_dEntrustBalance            float         委托金额
m_dBusinessBalance           float         合约金额
m_dBusinessFare              float         合约息费
m_dRepaidFare                float         已还息费
m_dRepaidBalance             float         已还金额
m_strCompactId               string        合约编号
m_strEntrustNo               string        委托编号
m_strPositionStr             string        定位串
示例：
get_closed_compacts('**********', 'CREDIT')
实时主推函数
#account_callback - 资金账号状态变化主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： account_callback(ContextInfo, accountInfo)
释义： 当资金账号状态有变化时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· accountInfo：账号对象在新窗口打开或信用账号对象在新窗口打开
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 5, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def account_callback(ContextInfo, accountInfo):
    print(show_data(accountInfo)) 

#task_callback - 账号任务状态变化主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： task_callback(ContextInfo, taskInfo)
释义： 当账号任务状态有变化时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· taskInfo 任务对象在新窗口打开
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 5, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def task_callback(ContextInfo, taskInfo):
    print(show_data(taskInfo))
#order_callback - 账号委托状态变化主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： order_callback(ContextInfo, orderInfo)
释义： 当账号委托状态有变化时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· orderInfo：委托在新窗口打开
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 5, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def order_callback(ContextInfo, orderInfo):
    print(show_data(orderInfo))
#deal_callback - 账号成交状态变化主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： deal_callback(ContextInfo, dealInfo)
释义： 当账号成交状态有变化时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· dealInfo：成交在新窗口打开
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 5, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def deal_callback(ContextInfo, dealInfo):
    print(show_data(dealInfo))
#position_callback - 账号持仓状态变化主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： position_callback(ContextInfo, positonInfo)
释义： 当账号持仓状态有变化时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· positonInfo：持仓在新窗口打开
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 5, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def position_callback(ContextInfo, positionInfo):
    print(show_data(positionInfo))

#orderError_callback - 账号异常下单主推
提示
1. 仅在实盘运行模式下生效。
2. 需要先在init里调用ContextInfo.set_account后生效。
用法： orderError_callback(ContextInfo,orderArgs,errMsg)
释义： 当账号下单异常时，这个函数被客户端调用
参数：
· ContextInfo：特定对象
· orderArgs：下单参数在新窗口打开
· errMsg：错误信息
返回： 无
示例：
示例返回值
#coding:gbk
def show_data(data):
    tdata = {}
    for ar in dir(data):
        if ar[:2] != 'm_':continue
        try:
            tdata[ar] = data.__getattribute__(ar)
        except:
            tdata[ar] = '<CanNotConvert>'
    return tdata

def init(ContextInfo):
    # 设置对应的资金账号
    # 示例需要在策略交易界面运行
    ContextInfo.set_account(account)
    
def after_init(ContextInfo):
    # 在策略交易界面运行时，account的值会被赋值为策略配置中的账号，编辑器界面运行时，需要手动赋值
    # 编译器界面里执行的下单函数不会产生实际委托  
    passorder(23, 1101, account, "000001.SZ", 11, 0, 100, "示例", 2, "投资备注",ContextInfo)
    pass

def orderError_callback(ContextInfo,orderArgs,errMsg):
    print(show_data(orderArgs))
    print(errMsg)

#其他主推函数
#credit_account_callback - 查询信用账户明细回调
用法： credit_account_callback(ContextInfo,seq,result)
释义： 查询信用账户明细回调
参数：
· ContextInfo：策略模型全局对象
· seq:query_credit_account时输入查询seq
· result: 信用账户明细在新窗口打开
#credit_opvolume_callback - 查询两融最大可下单量的回调
用法： credit_opvolume_callback(ContextInfo,accid,seq,ret,result)
释义： 查询两融最大可下单量的回调。
参数：
· ContextInfo：策略模型全局对象
· accid:查询的账号
· seq:query_credit_opvolume时输入查询seq
· ret:查询结果状态。正常返回:1,正在查询中-1,输入账号非法:-2,输入查询参数非法:-3,超时等服务器返回报错:-4
· result:查询到的结果
示例 见query_credit_opvolume
ETF期权交易
    数值            描述
50         买入开仓
51         卖出平仓
52         卖出开仓
53         买入平仓
54         备兑开仓
55         备兑平仓
56         认购行权
57         认沽行权
58         证券锁定
59         证券解锁
#

交易相关
#系统对象 ContextInfo 逐 k 线保存的机制
机制说明
ContextInfo是由底层维护并传递给init、handlebar等系统函数的参数，同一个 bar（不是 bar 里面的 tick，下同）内ContextInfo本质上是同一个变量且对其进行的修改只会对本次handlebar调用的下文所起作用。handlebar里对ContextInfo做的修改在该 bar 结束后才会进行保存，也就是说，对ContextInfo做的修改会在下一个 bar 体现出来。
具体来说，ContextInfo不同于一般 python 对象，做了逐 k 线更新设计，盘中主图品种每个 Level 1 分笔到达会触发handlebar函数调用，但只有 k 线结束时最后一个分笔触发的handlebar调用，对ContextInfo的修改才有效。
每次handlebar函数调用前会对ContextInfo对象进行深拷贝, 下一次分笔行情到来时，如果新的分笔不是新 k 线 bar 第一个分笔，则判断上一个分笔不是k线最后分笔，ContextInfo对象被回退为之前深拷贝的那个。
ContextInfo对象逐k线更新机制设计的目的，是为了在盘中时模拟k线的效果，只在k线结束的分笔触发的handlebar函数运行时生效一次，丢弃所有其他分笔的修改。
影响
该机制有两个影响，一是在ContextInfo对象中存数据每次分笔到达时会被深拷贝，拖慢策略运行；二是ContextInfo适用于记录逐k线生效的交易信号（quickTrade参数传0），不适宜立刻下单的情况。
如不需要模拟k线效果，希望调用交易函数后立刻下单，quickTrade参数可以传2， 下单记录可以用普通的全局变量保存, 不能存在ContextInfo对象的属性里(实现可以参考实盘示例7-调整至目标持仓Demo)。
#快速交易参数 quickTrade
下单函数passorder有可选参数快速交易quickTrade， 默认为0。
· 传0，只在k线结束分笔时调用passorder产生有效信号，其他情况调用不产生信号。
· 传1，在当前k线为最新k线时调用passorder函数产生有效信号, 历史k线调用不产生信号。
· 传2，任何情况下调用passorder都产生有效信号，不会丢弃任何一次调用的信号。
· 如果在定时器注册的回调函数，行情回调函数, after_init函数中调用下单函数，需要传2，确保不会漏单。
· passorder以外的下单函数不能指定快速交易参数，效果与传0的passorder一致。
#下单与回报相关
1. 为保证以尽快的速度执行交易信号, qmt 客户端提供的交易接口是异步的, 以快速交易参数填2的passorder函数为例，调用后会立刻发出委托, 然后返回。不会等待委托回报, 也不会阻塞python线程的运行。
2. 委托/成交/持仓/账号信息的更新, 是在客户端后台进行的, python策略中无法手动控制。python提供的取账号信息接口 get_trade_detail_data， 与四种交易回调函数, 都是从客户端本地缓存中读取数据 / 触发调用，不是调用时查询柜台再返回。客户端本地缓存状态定期接收柜台推送刷新，有交易主推的柜台50ms一次，没有交易主推的柜台1-6秒一次。 不能认为get_trade_detail_data查到的状态是与柜台完全一致的, 比如卖出委托后立刻查询, 不会查到对应委托, 可用资金也不会变多。
3. 实盘策略需要设计盘中保存/更新委托状态的机制。常见的做法是用全局变量字典保存委托状态, 给每一笔委托独立的投资备注作为字典的key，委托状态作为字典的value, 下单后默认设置为待报, 之后查到委托后更新状态。如果某品种股票存在待报状态委托, 暂停该品种后续报单, 防止发生超单的情况。(实现可以参考实盘示例7-调整至目标持仓Demo)
4. QMT 所有策略是在同一个线程中被调用的，任意一个策略阻塞线程(死循环 sleep 加锁等操作)会导致所有策略的执行被阻塞，所以不能在策略里写等待操作。如需要多线程 / 多进程的用法，可以使用极简模式配合 xtquant 库使用
#QMT 下单失败
1. 检查是否是在模型交易界面，实盘模式运行的策略。模拟模式只显示策略信号，不发出委托。
2. 如运行到交易函数，未看到策略信号，检查交易函数是否使用了快速下单参数(quickTrade)，默认为0，只会在k线结束发出委托，日线及以上周期等于全天不会委托。传1时，非历史bar上执行时（ContextInfo.is_last_bar()为True），只要策略模型中调用到就触发下单交易。传2，无论是否是历史bar，运行到交易函数时立刻发出委托。
如果希望盘中出现信号立即下单，建议传1，这种情况下会有策略信号闪烁的风险，需要自己处理；如果希望K线结束下单（信号不闪烁），建议传0，通常情况下不建议传2
提示
具体到场景：
1. handlebar逐k线下单, 每次k线结束的分笔生效一次, 传0;
2. 需要在handlebar盘中触发立刻下单, 传1;
3. 定时器/init/after_init与交易回调函数, 行情回调函数内下单, 传2.
3. 如看到实盘的策略信号，未找到对应委托，检查客户端左下角消息提示是否有报错，如有，请根据消息提示的描述修改下单参数
#行情相关
#QMT 行情数据基础概念
QMT行情数据主要分为三种，包括本地数据，全推数据，订阅数据。
1. 本地数据： 指下载到本地的行情数据加密文件。包括历史数据，适合回测模式使用，对应python接口为get_market_data_ex(subscribe=False) 在新窗口打开
2. 全推数据： 指客户端启动后, 自动接收，更新的全市场最新数据快照， 包括日线的开高低收,成交量成交额，与五档盘口（在行情界面选择了五档行情时可用五档 具体见行情常规问题3）。支持取全市场品种, 只有最新值，没有历史值，服务器对交易所下发的数据即时转发，打包增量部分发送给下游客户端。可以用get_full_tick一次性取出当前最新值，也可以用subscribe_whole_quote注册回调函数，每次处理增量的部分。 对应python接口为get_full_tick在新窗口打开，subscribe_whole_quote在新窗口打开
3. 订阅：指向行情服务器订阅指定品种行情, 共有四种周期(分笔 1分钟 5分钟 日线)，可以订阅当日数据，当天以前的需要用 down_history_data下. 订阅有最大数量限制(例如：假设最大数量限制为300个，则可以单独订阅日线300个，若同时订阅日线和五分钟 则各150个)，如需订阅超过300个限额，可以在页面右上角，选购行情vip服务。对应python接口为subscribe_quote在新窗口打开和get_market_data_ex(subscribe=True,)在新窗口打开其中，使用get_market_data或get_market_data_ex(subscribe=True,)时客户端会自动订阅传入的品种，不需要额外调用subscibe_quote,但这种方式订阅的品种没有订阅号，无法手动反订阅，只能通过停止策略释放可订阅数。
警告
如果超出订阅数量限制，则返回的行情数据会使用前值填充，出现重复值，非正确行情数据。
#QMT 行情调用函数对比说明
· down_history_data 下载指定区间的行情数据到本地，存放在硬盘上。效果和界面,点击行情数据下载一致。 开始时间不填时，为增量下载(以本地数据最后一天为开始时间), 填写的话按填写值下载。
· get_local_data 取本地数据函数，盘中不会更新，速度快，回测可以用这个函数取。
· get_full_tick 取客户端缓存中的最新全推数据。全推数据不包括历史，不用订阅，没有品种数量限制，盘中50ms更新一次，速度快。
· subscribe_quote 向服务器订阅股票行情 盘中实时更新 初次订阅耗时长，最大订阅品种数受限. 订阅超过一定数量的品种k线行情不会更新.可订阅四种基本周期(分笔 一分钟 五分钟 日线)行情（如果有 Level-2 行情权限 也可以订 Level-2 的）, 同一品种订阅了不同周期累加计数(如订阅浦发银行 1分钟 5分钟 日线行情 算订阅3次). 复数策略订阅同一品种计数不会累加. Level-2 的订阅也会受限，但是和 Level 1 的互不影响。
· unsubscribe_quote 按订阅号反订阅行情, 释放可订阅数.
· get_market_data_ex 取订阅/本地数据接口。用subscribe_quote在init函数中先订阅后subscribe参数为True时，取本地数据和订阅的最新行情。subscribe参数传False时,可以用来取本地数据，不会订阅。 如股票池超过一定数量，可用 down_history_data + get_local_data + get_full_tick 拼接历史和最新数据替代get_market_data_ex。
注意
gmd系列函数在init中运行时，只能读取到本地数据，不会取到最新行情数据，因子，不建议在init中调用使用gmd系列函数
警告
不再推荐使用!
set_universe, get_history_data, get_market_data 是早期订阅股票池, 取订阅的行情数据接口. 因为set_universe订阅的品种没有订阅号 无法在策略中反订阅, 只能通过停止策略释放订阅数。
#


