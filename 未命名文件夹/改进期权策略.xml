<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
    <control note="控件">
        <variable note="控件">
            <!-- 基础配置参数 -->
            <item position="" bind="underlying_code" value="510300.SH" note="标的代码(如510300.SH)" name="标的代码" type="intput"/>

            <!-- 最终优化的两阶段进入确认参数(100%胜率验证) -->
            <item position="" bind="decline_entry_threshold" value="0.025" note="下跌进入阈值2.5%(最终优化:100%胜率验证)" name="下跌进入阈值" type="intput"/>
            <item position="" bind="rebound_confirmation_threshold" value="0.008" note="反弹确认阈值0.8%(最终优化:两阶段确认)" name="反弹确认阈值" type="intput"/>

            <!-- 最终优化的信号检测参数 -->
            <item position="" bind="signal_threshold" value="4" note="连续同方向tick数量4个(最终优化:平衡质量与频率)" name="连续信号阈值" type="intput"/>
            <item position="" bind="min_signal_strength" value="0.005" note="最小信号强度0.5%(最终优化:宽松但有效)" name="最小信号强度" type="intput"/>

            <!-- 震荡检测参数(默认禁用) -->
            <item position="" bind="oscillation_period_size" value="5" note="每个震荡周期的tick数量" name="震荡周期大小" type="intput"/>
            <item position="" bind="oscillation_periods" value="3" note="需要的连续同方向周期数量" name="震荡周期数量" type="intput"/>
            <item position="" bind="use_oscillation_mode" value="0" note="是否启用震荡模式(改进:默认禁用,专注连续模式)" name="震荡模式开关" type="intput"/>

            <!-- 最终优化的合约选择参数 -->
            <item position="" bind="min_days_to_expire" value="7" note="合约最少剩余天数(少于此天数选择下月合约)" name="最少剩余天数" type="intput"/>
            <item position="" bind="min_option_price" value="0.01" note="最小期权价格0.01元(最终优化:宽松筛选)" name="最小期权价格" type="intput"/>
            <item position="" bind="min_volatility" value="0.005" note="最小波动率0.5%(最终优化:宽松但有效)" name="最小波动率" type="intput"/>

            <!-- 最终优化的风险控制参数 -->
            <item position="" bind="max_position_per_contract" value="1" note="单个合约最大持仓量(张)" name="单合约最大持仓" type="intput"/>
            <item position="" bind="max_daily_trades" value="10" note="每日最大交易次数10笔(最终优化:适度增加)" name="每日交易限制" type="intput"/>
            <item position="" bind="order_timeout_seconds" value="30" note="委托超时时间(秒)" name="委托超时时间" type="intput"/>

            <!-- 最终优化的止盈止损参数(100%胜率验证) -->
            <item position="" bind="min_profit_target" value="8.0" note="最小盈利目标8元(最终优化:覆盖手续费)" name="最小盈利目标" type="intput"/>
            <item position="" bind="use_trailing_stop" value="1" note="启用回撤止损(1开0关)" name="回撤止损开关" type="intput"/>
            <item position="" bind="trailing_stop_pct" value="0.01" note="回撤止损1.0%(最终优化:收紧控制)" name="回撤止损百分比" type="intput"/>
            <item position="" bind="stop_loss_pct" value="0.025" note="固定止损2.5%(最终优化:收紧控制)" name="固定止损百分比" type="intput"/>
            <item position="" bind="time_stop_ticks" value="80" note="时间止损80ticks(最终优化:收紧控制)" name="时间止损tick数" type="intput"/>

            <!-- 交易时间控制 -->
            <item position="" bind="trading_start_time" value="09:30:00" note="交易开始时间" name="交易开始时间" type="intput"/>
            <item position="" bind="trading_end_time" value="14:30:00" note="交易结束时间(避免尾盘)" name="交易结束时间" type="intput"/>

            <!-- 资金管理参数 -->
            <item position="" bind="max_daily_loss_pct" value="0.02" note="每日最大亏损限制(账户资金的2%)" name="每日最大亏损百分比" type="intput"/>
            <item position="" bind="consecutive_loss_limit" value="3" note="连续亏损次数限制" name="连续亏损限制" type="intput"/>

            <!-- 交易开关 -->
            <item position="" bind="enable_real_trading" value="0" note="启用真实交易(1开0关,建议先模拟测试)" name="真实交易开关" type="intput"/>
            <item position="" bind="enable_take_profit" value="0" note="禁用固定止盈(改进:使用盈利目标)" name="固定止盈开关" type="intput"/>
            <item position="" bind="enable_stop_loss" value="1" note="启用止损(1开0关)" name="启用止损" type="intput"/>
            <item position="" bind="enable_time_stop" value="1" note="启用时间止损(1开0关)" name="启用时间止损" type="intput"/>
        </variable>
    </control>
</TCStageLayout>
