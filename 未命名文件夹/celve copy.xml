<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
    <control note="控件">
        <variable note="控件">
            <!-- ==================== 核心配置参数 ==================== -->
            <item position="" bind="underlying_code" value="510300.SH" note="标的代码(如510300.SH)" name="标的代码" type="intput"/>
            <item position="" bind="enable_real_trading" value="0" note="启用真实交易(1开0关,谨慎使用)" name="真实交易开关" type="intput"/>
            <item position="" bind="max_position_per_contract" value="1" note="单个合约最大持仓量(张)" name="单合约最大持仓" type="intput"/>
            <item position="" bind="order_timeout_seconds" value="30" note="委托超时时间(秒)" name="委托超时时间" type="intput"/>
            <item position="" bind="min_days_to_expire" value="7" note="合约最少剩余天数" name="最少剩余天数" type="intput"/>

            <!-- ==================== 策略模式选择 ==================== -->
            <item position="" bind="enable_dual_mode" value="1" note="启用双模式策略(1开0关,推荐)" name="双模式策略" type="intput"/>

            <!-- ==================== 双模式策略参数 ==================== -->
            <!-- 突破模式：价格向上穿过VWAP后继续上涨触发买入 -->
            <item position="" bind="breakthrough_threshold" value="3.0" note="突破阈值(%,建议2-5,越大越保守)" name="突破阈值" type="intput"/>
            <item position="" bind="max_drawdown_from_high" value="2.0" note="最大回调(%,建议1-3,越小越保守)" name="最大回调" type="intput"/>
            
            <!-- 反弹模式：价格向下穿过VWAP后反弹触发买入 -->
            <item position="" bind="rebound_threshold" value="5.0" note="反弹阈值(%,建议3-8,越大越保守)" name="反弹阈值" type="intput"/>

            <!-- ==================== 传统策略参数 ==================== -->
            <!-- 连续趋势检测 -->
            <item position="" bind="signal_threshold" value="5" note="连续同方向tick数量(建议3-8)" name="连续信号阈值" type="intput"/>
            
            <!-- 震荡检测 -->
            <item position="" bind="oscillation_period_size" value="5" note="震荡周期大小(建议3-8)" name="震荡周期大小" type="intput"/>
            <item position="" bind="oscillation_periods" value="3" note="震荡周期数量(建议2-5)" name="震荡周期数量" type="intput"/>

            <!-- ==================== 趋势过滤开关 ==================== -->
            <item position="" bind="enable_trend_filter" value="1" note="启用趋势过滤(1开0关)" name="趋势过滤" type="intput"/>
            <item position="" bind="enable_above_avg_up" value="1" note="高于均价向上趋势(1开0关)" name="高于均价向上" type="intput"/>
            <item position="" bind="enable_below_avg_up" value="1" note="低于均价向上趋势(1开0关)" name="低于均价向上" type="intput"/>
            <item position="" bind="enable_above_avg_oscillation" value="1" note="高于均价震荡(1开0关)" name="高于均价震荡" type="intput"/>
            <item position="" bind="enable_below_avg_oscillation" value="1" note="低于均价震荡(1开0关)" name="低于均价震荡" type="intput"/>
            <item position="" bind="enable_above_avg_down" value="0" note="高于均价向下趋势(1开0关,不推荐)" name="高于均价向下" type="intput"/>
            <item position="" bind="enable_below_avg_down" value="0" note="低于均价向下趋势(1开0关,不推荐)" name="低于均价向下" type="intput"/>

            <!-- ==================== 调试开关 ==================== -->
            <item position="" bind="enable_tick_log" value="0" note="Tick详细日志(1开0关,影响性能)" name="Tick日志" type="intput"/>
            <item position="" bind="enable_signal_log" value="1" note="信号日志(1开0关,推荐开启)" name="信号日志" type="intput"/>
        </variable>
    </control>
</TCStageLayout>
