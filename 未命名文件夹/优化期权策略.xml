<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
  <control>
    <variable>
      <!-- 核心信号参数 -->
      <item position="0,0,100,20" bind="signal_threshold" value="4" note="连续信号阈值(tick)" name="连续信号阈值" type="int"/>
      <item position="0,25,100,20" bind="oscillation_period_size" value="8" note="震荡周期大小(tick)" name="震荡周期大小" type="int"/>
      <item position="0,50,100,20" bind="oscillation_periods" value="4" note="震荡周期数量" name="震荡周期数量" type="int"/>
      
      <!-- 下跌反弹参数 -->
      <item position="0,75,100,20" bind="decline_threshold_min" value="0.02" note="最小下跌阈值(2%)" name="最小下跌阈值" type="float"/>
      <item position="0,100,100,20" bind="decline_threshold_max" value="0.05" note="最大下跌阈值(5%)" name="最大下跌阈值" type="float"/>
      
      <!-- 止盈止损参数 -->
      <item position="0,125,100,20" bind="take_profit_strategy" value="dynamic" note="止盈策略:fixed/dynamic/trailing" name="止盈策略" type="string"/>
      <item position="0,150,100,20" bind="take_profit_pct_1" value="0.025" note="第一档止盈(2.5%)" name="第一档止盈" type="float"/>
      <item position="0,175,100,20" bind="take_profit_pct_2" value="0.045" note="第二档止盈(4.5%)" name="第二档止盈" type="float"/>
      <item position="0,200,100,20" bind="take_profit_pct_3" value="0.08" note="第三档止盈(8%)" name="第三档止盈" type="float"/>
      <item position="0,225,100,20" bind="stop_loss_pct" value="0.002" note="止损比例(0.2%)" name="止损比例" type="float"/>
      <item position="0,250,100,20" bind="time_stop_ticks" value="80" note="时间止损(tick)" name="时间止损" type="int"/>
      
      <!-- 跟踪止损参数 -->
      <item position="0,275,100,20" bind="trailing_stop_trigger" value="0.03" note="跟踪止损触发点(3%)" name="跟踪止损触发" type="float"/>
      <item position="0,300,100,20" bind="trailing_stop_pct" value="0.015" note="跟踪止损幅度(1.5%)" name="跟踪止损幅度" type="float"/>
      
      <!-- 基础交易参数 -->
      <item position="0,325,100,20" bind="underlying_code" value="510300.SH" note="标的代码" name="标的代码" type="string"/>
      <item position="0,350,100,20" bind="max_position_per_contract" value="1" note="单合约最大持仓" name="最大持仓" type="int"/>
      <item position="0,375,100,20" bind="order_timeout_seconds" value="30" note="委托超时时间(秒)" name="委托超时" type="int"/>
      <item position="0,400,100,20" bind="min_days_to_expire" value="7" note="最少剩余天数" name="最少剩余天数" type="int"/>
      <item position="0,425,100,20" bind="enable_real_trading" value="false" note="真实交易开关" name="真实交易" type="bool"/>
    </variable>
  </control>
</TCStageLayout>
