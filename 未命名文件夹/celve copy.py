#coding:gbk
from datetime import datetime
import time
import xml.etree.ElementTree as ET
import os
import logging

# 简化日志配置 - 只记录重要事件
logging.basicConfig(level=logging.WARNING, format='%(asctime)s %(message)s', datefmt='%H:%M:%S')

# ==================== 参数管理模块 ====================
class ParameterManager:
    """参数管理器 - 内嵌版本"""
    def __init__(self):
        # 核心可配置参数（与XML中的bind名称对应）
        self.params = {
            # 核心配置参数
            'underlying_code': '510300.SH',    # 标的代码
            'signal_threshold': 5,             # 连续同方向tick数量

            # 震荡检测参数
            'oscillation_period_size': 5,      # 每周期tick数量
            'oscillation_periods': 3,          # 需要的周期数量

            # 合约选择参数
            'min_days_to_expire': 7,           # 最少剩余天数

            # 交易参数
            'max_position_per_contract': 10,    # 单合约最大持仓量
            'order_timeout_seconds': 30,       # 委托超时时间(秒)
            'enable_real_trading': False,      # 真实交易开关

            # 双模式策略参数
            'enable_dual_mode': True,          # 启用双模式策略
            'avg_price_window': 50,            # 平均价格窗口(增加到50个tick)
            'avg_price_period': 'tick',        # 平均价格周期(使用分笔数据)

            # 突破模式参数
            'breakthrough_threshold': 3.0,     # 向上突破阈值(%)
            'max_drawdown_from_high': 2.0,     # 从最高点最大回调(%)

            # 反弹模式参数
            'rebound_threshold': 5.0,          # 反弹阈值(%)

            # 移动平均价API优先级
            'use_official_ma_api': True,       # 优先使用QMT官方MA API

            # 兼容旧参数（逐步废弃）
            'enable_trend_filter': False,      # 禁用旧的趋势过滤
            'enable_above_avg_up': False,      # 禁用旧的趋势类型
            'enable_below_avg_up': False,      # 禁用旧的趋势类型
            'enable_above_avg_oscillation': False,  # 禁用旧的趋势类型
            'enable_below_avg_oscillation': False,  # 禁用旧的趋势类型
            'enable_above_avg_down': False,    # 禁用旧的趋势类型
            'enable_below_avg_down': False,    # 禁用旧的趋势类型
        }

        # 硬编码参数（不需要配置）
        self.fixed_params = {
            # 数据过滤参数
            'enable_duplicate_filter': True,   # 启用重复tick过滤
            'price_precision': 4,              # 价格精度

            # 价格链参数
            'max_chain_length': 30,            # 价格链最大长度
            'display_timestamp': True,         # 显示时间戳

            # 趋势检测参数
            'enable_trend_detection': True,    # 启用趋势检测
            'reset_after_signal': True,        # 信号触发后重置

            # 期权选择参数
            'select_call_count': 1,            # 选择1个认购期权
            'select_put_count': 1,             # 选择1个认沽期权
            'prefer_nearest_expiry': True,     # 优先最近到期
            'prefer_nearest_strike': True,     # 优先最近行权价

            # 日志参数
            'enable_tick_log': False,          # 禁用详细tick日志
            'enable_signal_log': True,         # 启用信号日志
        }
        
        # 尝试从XML加载参数
        self.xml_path = r"C:\国金证券QMT交易端\python\formulaLayout\新期权策略.xml"
        self.load_from_xml()
    
    def load_from_xml(self):
        """从QMT标准格式XML文件加载参数"""
        try:
            if os.path.exists(self.xml_path):
                tree = ET.parse(self.xml_path)
                root = tree.getroot()

                # 解析QMT标准格式: TCStageLayout/control/variable/item
                for item in root.findall('.//item'):
                    bind_name = item.get('bind')
                    param_value = item.get('value')

                    if bind_name and param_value is not None and bind_name in self.params:
                        # 类型转换 - 根据参数名称决定转换方式
                        if bind_name in ['enable_real_trading', 'enable_trend_filter',
                                       'enable_above_avg_up', 'enable_below_avg_up',
                                       'enable_above_avg_oscillation', 'enable_below_avg_oscillation',
                                       'enable_above_avg_down', 'enable_below_avg_down']:
                            # 布尔类型参数
                            if param_value in ['0', '1']:
                                param_value = param_value == '1'
                            elif param_value.lower() in ['true', 'false']:
                                param_value = param_value.lower() == 'true'
                        elif param_value.isdigit():
                            # 数字参数保持为整数
                            param_value = int(param_value)
                        elif param_value.lower() in ['true', 'false']:
                            # 其他布尔参数
                            param_value = param_value.lower() == 'true'
                        elif '.' in param_value and param_value.replace('.', '').replace('-', '').isdigit():
                            param_value = float(param_value)

                        self.params[bind_name] = param_value
                        print(f"  ?? 加载参数: {bind_name} = {param_value}")

                print(f"? 成功从QMT格式XML加载参数")
            else:
                print(f"?? XML文件不存在，使用默认参数")
        except Exception as e:
            print(f"?? XML加载失败，使用默认参数: {e}")
    
    def get(self, param_name, default_value=None):
        """获取参数值（先查找可配置参数，再查找硬编码参数）"""
        if param_name in self.params:
            return self.params[param_name]
        elif param_name in self.fixed_params:
            return self.fixed_params[param_name]
        else:
            return default_value
    
    def set(self, param_name, value):
        """设置参数值"""
        self.params[param_name] = value
    
    def print_params(self):
        """打印所有参数"""
        logging.info("=== 当前参数配置 ===")
        logging.info("?? 可配置参数:")
        for key, value in self.params.items():
            logging.info(f"  {key} = {value}")
        logging.info("?? 硬编码参数:")
        for key, value in self.fixed_params.items():
            logging.info(f"  {key} = {value}")
        logging.info("==================")

# ==================== 数据存储模块 ====================
class OptionBatchManager:
    """期权批次管理类 - 精细化管理每个批次的开仓（持久化版本）"""
    def __init__(self, data_dir="qmt_batch_data"):
        import os
        import json

        self.data_dir = data_dir
        self.batch_file = os.path.join(data_dir, "option_batches.json")

        # 确保数据目录存在
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            print(f"?? 创建数据目录: {data_dir}")

        # 加载历史批次数据
        self.batches = self.load_batches()
        self.pending_orders = {}  # 待成交委托

    def add_pending_order(self, order_id, option_code, target_price, target_quantity):
        """记录待成交委托"""
        self.pending_orders[order_id] = {
            'option_code': option_code,
            'target_price': target_price,
            'target_quantity': target_quantity,
            'timestamp': time.time(),
            'status': 'pending'
        }
        print(f"?? 记录委托: {order_id} {option_code} 目标价格:{target_price:.4f} 数量:{target_quantity}")

    def load_batches(self):
        """加载历史批次数据"""
        import json
        try:
            if os.path.exists(self.batch_file):
                with open(self.batch_file, 'r', encoding='utf-8') as f:
                    batches = json.load(f)
                print(f"?? 加载历史批次数据: {len(batches)} 个批次")
                return batches
            else:
                print("?? 创建新的批次数据文件")
                return []
        except Exception as e:
            print(f"? 加载批次数据失败: {e}")
            return []

    def save_batches(self):
        """保存批次数据到文件"""
        import json
        try:
            with open(self.batch_file, 'w', encoding='utf-8') as f:
                json.dump(self.batches, f, ensure_ascii=False, indent=2)
            print(f"?? 批次数据已保存: {len(self.batches)} 个批次")
        except Exception as e:
            print(f"? 保存批次数据失败: {e}")

    def add_batch_from_deal(self, dealInfo):
        """
        从成交回调记录实际成交批次（持久化版本）
        基于QMT官方文档的Deal对象结构
        """
        from datetime import datetime

        batch = {
            'batch_id': len(self.batches) + 1,
            'option_code': f"{dealInfo.m_strInstrumentID}.{dealInfo.m_strExchangeID}",
            'order_id': dealInfo.m_strOrderSysID,

            # 核心价格信息 - 来自QMT实际成交数据
            'entry_price': float(dealInfo.m_dPrice),           # 实际成交价格
            'quantity': int(dealInfo.m_nVolume),               # 实际成交数量
            'cost': float(dealInfo.m_dTradeAmount),            # 实际成交金额
            'commission': float(dealInfo.m_dCommission),       # 实际手续费

            # 时间信息
            'trade_time': dealInfo.m_strTradeTime,             # QMT成交时间
            'trade_date': getattr(dealInfo, 'm_strTradeDate', time.strftime('%Y-%m-%d')),
            'record_timestamp': time.time(),                   # 记录时间戳
            'record_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

            # 附加信息
            'instrument_name': getattr(dealInfo, 'm_strInstrumentName', ''),
            'exchange_name': getattr(dealInfo, 'm_strExchangeName', ''),
            'remark': getattr(dealInfo, 'm_strRemark', ''),

            # 数据来源标记
            'data_source': 'qmt_deal_callback',
            'data_reliability': 'high',  # 来自QMT官方回调，可靠性高

            # 计算字段
            'total_cost': float(dealInfo.m_dTradeAmount) + float(dealInfo.m_dCommission)
        }

        self.batches.append(batch)

        # ?? 关键：立即保存到文件
        self.save_batches()

        print(f"?? 新增批次#{batch['batch_id']}: {batch['option_code']} "
              f"实际价格:{batch['entry_price']:.4f} 数量:{batch['quantity']} "
              f"成本:{batch['cost']:.2f}")

        # 清理对应的待成交委托 - 根据真实委托号查找并删除临时委托记录
        real_order_id = dealInfo.m_strOrderSysID
        temp_order_to_remove = None

        for temp_order_id, order_info in self.pending_orders.items():
            if order_info.get('real_order_id') == real_order_id:
                temp_order_to_remove = temp_order_id
                break

        if temp_order_to_remove:
            del self.pending_orders[temp_order_to_remove]
            print(f"?? 清理待成交委托: {temp_order_to_remove} -> 真实委托号:{real_order_id}")
        else:
            print(f"?? 未找到对应的待成交委托记录: 真实委托号:{real_order_id}")

        print(f"? 新增批次#{batch['batch_id']}: {batch['option_code']} "
              f"实际价格:{batch['entry_price']:.4f} "
              f"数量:{batch['quantity']} "
              f"成本:{batch['cost']:.2f}")

        return batch

    def get_batch_by_id(self, batch_id):
        """根据批次ID查询批次信息"""
        for batch in self.batches:
            if batch['batch_id'] == batch_id:
                return batch
        return None

    def get_batches_by_date(self, date_str):
        """查询指定日期的所有批次"""
        return [batch for batch in self.batches if batch['trade_date'] == date_str]

    def query_batch_price(self, batch_id):
        """查询特定批次的成交价格"""
        batch = self.get_batch_by_id(batch_id)
        if batch:
            return {
                'batch_id': batch_id,
                'option_code': batch['option_code'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'trade_time': f"{batch['trade_date']} {batch['trade_time']}",
                'data_source': batch['data_source'],
                'reliability': batch['data_reliability']
            }
        return None

    def show_all_batches(self):
        """显示所有批次信息"""
        if not self.batches:
            print("?? 暂无批次记录")
            return

        print(f"\n?? 批次记录总览 (共{len(self.batches)}个批次):")
        print("-" * 80)
        for batch in self.batches:
            print(f"批次#{batch['batch_id']}: {batch['option_code']}")
            print(f"  成交价格: {batch['entry_price']:.4f} 元")
            print(f"  成交数量: {batch['quantity']} 张")
            print(f"  成交时间: {batch['trade_date']} {batch['trade_time']}")
            print(f"  数据来源: {batch['data_source']} (可靠性: {batch['data_reliability']})")
            print("-" * 40)

    def get_batches_for_option(self, option_code):
        """获取指定期权的所有批次"""
        return [batch for batch in self.batches if batch['option_code'] == option_code]

    def calculate_batch_pnl(self, option_code, current_price):
        """计算指定期权所有批次的盈亏"""
        batches = self.get_batches_for_option(option_code)
        batch_pnl = []

        for batch in batches:
            pnl = (current_price - batch['entry_price']) * batch['quantity']
            pnl_rate = (current_price - batch['entry_price']) / batch['entry_price']

            batch_pnl.append({
                'batch_id': batch['batch_id'],
                'entry_price': batch['entry_price'],
                'quantity': batch['quantity'],
                'timestamp': batch['timestamp'],
                'pnl': pnl,
                'pnl_rate': pnl_rate,
                'current_price': current_price
            })

        return batch_pnl

    def get_total_position(self, option_code):
        """获取指定期权的总持仓"""
        batches = self.get_batches_for_option(option_code)
        return sum(batch['quantity'] for batch in batches)

    def get_average_cost(self, option_code):
        """计算平均成本（仅用于参考）"""
        batches = self.get_batches_for_option(option_code)
        if not batches:
            return 0

        total_cost = sum(batch['cost'] for batch in batches)
        total_quantity = sum(batch['quantity'] for batch in batches)
        return total_cost / total_quantity if total_quantity > 0 else 0

    def handle_cancelled_order(self, order_id):
        """处理撤单情况"""
        if order_id in self.pending_orders:
            order_info = self.pending_orders[order_id]
            print(f"? 委托撤销: {order_id} {order_info['option_code']} "
                  f"目标价格:{order_info['target_price']:.4f} "
                  f"数量:{order_info['target_quantity']}")
            del self.pending_orders[order_id]
            return order_info
        return None

    def clear_option_batches(self, option_code):
        """清除指定期权的所有批次记录"""
        try:
            original_count = len(self.batches)
            self.batches = [batch for batch in self.batches if batch['option_code'] != option_code]
            removed_count = original_count - len(self.batches)

            if removed_count > 0:
                self.save_batches()
                print(f"?? 已清除 {option_code} 的 {removed_count} 个批次记录")
            else:
                print(f"?? {option_code} 无批次记录需要清除")

        except Exception as e:
            print(f"? 清除批次记录失败: {option_code} {e}")

    def create_estimated_batch(self, option_code, quantity, estimated_price):
        """创建估算批次记录（用于同步）"""
        try:
            from datetime import datetime

            batch = {
                'batch_id': len(self.batches) + 1,
                'option_code': option_code,
                'order_id': f"sync_{option_code}_{int(time.time())}",

                # 估算信息
                'entry_price': estimated_price,
                'quantity': quantity,
                'cost': estimated_price * quantity * 10000,  # 期权合约乘数
                'commission': 3.4,  # 估算手续费

                # 时间信息
                'trade_time': time.strftime('%H:%M:%S'),
                'trade_date': time.strftime('%Y-%m-%d'),
                'record_timestamp': time.time(),
                'record_datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),

                # 标记为同步创建
                'data_source': 'sync_estimated',
                'data_reliability': 'estimated',
                'remark': '持仓同步时创建的估算批次',

                # 计算字段
                'total_cost': estimated_price * quantity * 10000 + 3.4
            }

            self.batches.append(batch)
            self.save_batches()

            print(f"?? 创建估算批次#{batch['batch_id']}: {option_code} "
                  f"估算价格:{estimated_price:.4f} 数量:{quantity}")

            return batch

        except Exception as e:
            print(f"? 创建估算批次失败: {option_code} {e}")
            return None

    def adjust_batches_for_partial_close(self, option_code, closed_quantity):
        """调整批次记录以反映部分平仓"""
        try:
            batches = self.get_batches_for_option(option_code)
            if not batches:
                print(f"?? 无批次记录可调整: {option_code}")
                return

            remaining_to_close = closed_quantity

            # 从最新的批次开始减少（LIFO原则）
            for batch in reversed(batches):
                if remaining_to_close <= 0:
                    break

                if batch['quantity'] <= remaining_to_close:
                    # 整个批次都被平仓
                    remaining_to_close -= batch['quantity']
                    self.batches.remove(batch)
                    print(f"??? 移除完全平仓的批次#{batch['batch_id']}: 数量{batch['quantity']}")
                else:
                    # 部分平仓
                    batch['quantity'] -= remaining_to_close
                    batch['cost'] = batch['entry_price'] * batch['quantity'] * 10000
                    batch['total_cost'] = batch['cost'] + batch.get('commission', 0)
                    print(f"?? 调整部分平仓的批次#{batch['batch_id']}: 剩余数量{batch['quantity']}")
                    remaining_to_close = 0

            if remaining_to_close > 0:
                print(f"?? 平仓数量超出记录: 超出{remaining_to_close}张")

            self.save_batches()
            print(f"? 批次调整完成: {option_code}")

        except Exception as e:
            print(f"? 调整批次记录失败: {option_code} {e}")

class OptionDataStore:
    """数据存储类"""
    def __init__(self, param_manager):
        self.pm = param_manager

        # 系统配置（从参数管理器获取，可通过XML配置）
        self.underlying_code = self.pm.get('underlying_code', '510300.SH')
        self.selected_options = []

        # 数据存储
        self.last_prices = {}
        self.last_tick_time = {}
        self.price_chains = {}
        self.trend_count = {}
        self.trend_direction = {}
        self.trend_prices = {}
        self.signals = {}

        # 震荡检测数据
        self.oscillation_data = {}
        self.current_tick_id = {}  # 跟踪每个合约的tick ID

        # 双模式策略数据
        self.cross_tracking = {}   # 穿线追踪数据
        self.price_extremes = {}   # 价格极值追踪

        # 交易相关数据 - 使用新的批次管理器
        self.batch_manager = OptionBatchManager()
        self.pending_buy_quantities = {}  # 记录未完成的买入数量

# ==================== 核心功能模块 ====================
class OptionMonitor:
    """期权监控核心类"""
    def __init__(self):
        self.pm = ParameterManager()
        self.data = OptionDataStore(self.pm)
    
    def get_underlying_price(self, C):
        """获取标的当前价格"""
        try:
            tick_data = C.get_full_tick([self.data.underlying_code])
            if self.data.underlying_code in tick_data:
                price = tick_data[self.data.underlying_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)
            return None
        except Exception as e:
            logging.error(f"获取标的价格失败: {e}")
            return None
    
    def is_trading_time(self):
        """检查是否在交易时间内"""
        try:
            now = datetime.now()
            current_time = now.time()

            # 交易时间：9:30-11:30, 13:00-15:00
            morning_start = datetime.strptime("09:30", "%H:%M").time()
            morning_end = datetime.strptime("11:30", "%H:%M").time()
            afternoon_start = datetime.strptime("13:00", "%H:%M").time()
            afternoon_end = datetime.strptime("15:00", "%H:%M").time()

            is_morning = morning_start <= current_time <= morning_end
            is_afternoon = afternoon_start <= current_time <= afternoon_end

            return is_morning or is_afternoon

        except Exception as e:
            print(f"?? 交易时间检查失败: {e}")
            return False

    def select_best_options(self, C):
        """选择最优期权合约"""
        try:
            print("?? 开始选择期权合约...")

            # 检查交易时间
            if not self.is_trading_time():
                current_time = datetime.now().strftime("%H:%M:%S")
                print(f"? 当前时间 {current_time} 不在交易时间内")
                print("?? 交易时间: 9:30-11:30, 13:00-15:00")
                print("?? 建议: 请在交易时间内运行策略，或使用历史数据测试")
                return []

            underlying_price = self.get_underlying_price(C)
            if underlying_price is None:
                logging.error("无法获取标的价格")
                return []

            print(f"标的 {self.data.underlying_code} 当前价格: {underlying_price}")

            all_options = C.get_option_undl_data(self.data.underlying_code)
            if not all_options:
                print("未找到期权合约")
                return []

            call_options = []
            put_options = []
            current_date = datetime.now()
            min_days_to_expire = self.pm.get('min_days_to_expire', 7)

            for option_code in all_options:
                try:
                    option_detail = C.get_option_detail_data(option_code)
                    if option_detail:
                        strike_price = option_detail.get('OptExercisePrice', 0)
                        option_type = option_detail.get('optType', '')
                        expire_date = option_detail.get('ExpireDate', 0)

                        try:
                            expire_datetime = datetime.datetime.strptime(str(expire_date), '%Y%m%d')
                            days_to_expire = (expire_datetime - current_date).days
                            if days_to_expire <= 0:
                                continue
                            # 过滤到期日不足的合约
                            if days_to_expire < min_days_to_expire:
                                continue
                        except:
                            continue

                        if option_type == 'CALL' and strike_price > underlying_price:
                            call_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': strike_price - underlying_price
                            })
                        elif option_type == 'PUT' and strike_price < underlying_price:
                            put_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': underlying_price - strike_price
                            })
                except Exception as e:
                    continue

            call_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))
            put_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))

            selected = []
            call_count = self.pm.get('select_call_count', 1)
            put_count = self.pm.get('select_put_count', 1)

            for i in range(min(call_count, len(call_options))):
                selected.append(call_options[i]['code'])
                print(f"选中认购期权: {call_options[i]['code']}, 行权价: {call_options[i]['strike']}, 距离: {call_options[i]['price_distance']:.4f}, 到期: {call_options[i]['days_to_expire']}天")

            for i in range(min(put_count, len(put_options))):
                selected.append(put_options[i]['code'])
                print(f"选中认沽期权: {put_options[i]['code']}, 行权价: {put_options[i]['strike']}, 距离: {put_options[i]['price_distance']:.4f}, 到期: {put_options[i]['days_to_expire']}天")

            return selected

        except Exception as e:
            logging.error(f"选择期权合约失败: {e}")
            return []
    
    def filter_duplicate_ticks(self, option_code, current_price, current_time):
        """过滤相邻重复价格的tick"""
        try:
            if not self.pm.get('enable_duplicate_filter', True):
                return True
            
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(current_price, 'item'):
                current_price = float(current_price.item())
            else:
                current_price = float(current_price)
            
            current_price_rounded = round(current_price, price_precision)
            
            if option_code not in self.data.last_prices:
                self.data.last_prices[option_code] = current_price_rounded
                self.data.last_tick_time[option_code] = current_time
                return True
            
            last_price_rounded = round(self.data.last_prices[option_code], price_precision)
            if last_price_rounded == current_price_rounded:
                return False
            
            self.data.last_prices[option_code] = current_price_rounded
            self.data.last_tick_time[option_code] = current_time
            return True
            
        except Exception as e:
            logging.error(f"过滤tick错误: {e}")
            return False
    
    def update_price_chain(self, option_code, price):
        """更新价格链"""
        try:
            max_length = self.pm.get('max_chain_length', 30)
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(price, 'item'):
                price = float(price.item())
            else:
                price = float(price)
            
            price_rounded = round(price, price_precision)
            
            if option_code not in self.data.price_chains:
                self.data.price_chains[option_code] = []
            
            self.data.price_chains[option_code].append(price_rounded)
            
            if len(self.data.price_chains[option_code]) > max_length:
                self.data.price_chains[option_code] = self.data.price_chains[option_code][-max_length:]
            
            return self.data.price_chains[option_code]
            
        except Exception as e:
            logging.error(f"更新价格链错误: {e}")
            return []
    
    def detect_trend_direction(self, option_code, current_price):
        """检测趋势方向"""
        try:
            if option_code not in self.data.trend_prices:
                self.data.trend_prices[option_code] = []
                self.data.trend_count[option_code] = 0
                self.data.trend_direction[option_code] = 0
            
            self.data.trend_prices[option_code].append(current_price)
            
            if len(self.data.trend_prices[option_code]) < 2:
                return 0, 0
            
            prev_price = self.data.trend_prices[option_code][-2]
            if current_price > prev_price:
                current_direction = 1
            elif current_price < prev_price:
                current_direction = -1
            else:
                return 0, 0
            
            if self.data.trend_direction[option_code] == current_direction:
                self.data.trend_count[option_code] += 1
            else:
                # 方向改变，需要重置趋势检测并启动震荡检测
                old_direction = self.data.trend_direction[option_code]
                old_count = self.data.trend_count[option_code]

                # 记录连续模式结束的价格序列
                if old_direction != 0:
                    end_sequence = self.data.trend_prices[option_code][-old_count-1:] if len(self.data.trend_prices[option_code]) > old_count else self.data.trend_prices[option_code]
                    timestamp = self.get_current_timestamp()
                    print(f"?? 连续模式结束: {option_code} [{timestamp}] {'↑' if old_direction == 1 else '↓'}{old_count} 序列:{end_sequence}")

                # 连续模式重置：当前tick是新方向的第一个tick
                self.data.trend_direction[option_code] = current_direction
                self.data.trend_count[option_code] = 1  # 当前tick计为新方向的第1个
                print(f"?? 连续模式重置: {option_code} 新方向{'↑' if current_direction == 1 else '↓'}从当前tick开始计数")

                # 只有在之前有方向的情况下才启动震荡检测（避免初始化时启动）
                if old_direction != 0:
                    print(f"?? 方向改变: {option_code} {'↑' if old_direction == 1 else '↓'}{old_count} → {'↑' if current_direction == 1 else '↓'}1")
                    self.try_start_oscillation_detection(option_code, current_price)

            return current_direction, self.data.trend_count[option_code]
            
        except Exception as e:
            logging.error(f"趋势检测错误: {e}")
            return 0, 0
    
    def check_trend_signal(self, option_code):
        """检查趋势信号"""
        signal_threshold = self.pm.get('signal_threshold', 5)

        if option_code not in self.data.trend_count:
            return False, None

        count = self.data.trend_count[option_code]
        direction = self.data.trend_direction[option_code]

        # 添加调试日志
        if count >= signal_threshold - 1:  # 接近触发时显示调试信息
            print(f"?? 连续信号检查: {option_code} 计数{count}/{signal_threshold} 方向{'↑' if direction == 1 else '↓' if direction == -1 else '无'}")

        if count >= signal_threshold:
            signal_type = "买入" if direction == 1 else "卖出"
            print(f"?? 连续信号触发: {option_code} {signal_type} 计数{count}/{signal_threshold}")
            return True, signal_type

        return False, None
    
    def reset_trend_detection(self, option_code, current_price):
        """重置趋势检测（信号触发后调用）"""
        self.data.trend_count[option_code] = 0
        self.data.trend_direction[option_code] = 0
        self.data.trend_prices[option_code] = [current_price]

        print(f"?? 连续信号触发后重置: {option_code} 价格:{current_price:.4f}")
        print(f"   连续信号触发后不启动震荡检测，等待方向改变时启动")

    def get_moving_average_price_qmt(self, C, option_code):
        """获取真实的当日累积均价（从开盘到现在的VWAP）"""
        try:
            # 使用增强的精确VWAP计算方法（包含重试和降级策略）
            current_vwap = self.calculate_precise_vwap_enhanced(C, option_code)
            if current_vwap is not None:
                return current_vwap

            # 如果所有方法都失败，返回None让策略等待下一次tick
            logging.error(f"无法计算{option_code}的VWAP，策略将等待下一次tick")
            return None

        except Exception as e:
            logging.error(f"获取VWAP出错: {e}")
            return None

    def calculate_precise_vwap_enhanced(self, C, option_code):
        """增强的精确VWAP计算（包含重试和降级策略）"""
        try:
            current_time = datetime.now()
            today_str = current_time.strftime('%Y%m%d')
            current_time_str = current_time.strftime('%Y%m%d%H%M%S')

            # 尝试多种起始时间，从最精确到最宽松
            start_times = [
                ('093200', '从9:32开始（避开开盘异常）'),
                ('093100', '从9:31开始'),
                ('093000', '从9:30开始（包含开盘）')
            ]

            for start_suffix, description in start_times:
                start_time_str = today_str + start_suffix

                # 重试机制：最多重试3次
                for retry in range(3):
                    try:
                        vwap = self.calculate_vwap_with_retry(C, option_code, start_time_str, current_time_str)
                        if vwap is not None:
                            logging.info(f"{option_code} VWAP成功: {vwap:.4f} ({description}, 重试{retry}次)")
                            return vwap
                    except Exception as e:
                        if retry < 2:  # 不是最后一次重试
                            logging.warning(f"VWAP计算重试 {retry+1}/3: {e}")
                            time.sleep(0.1)  # 短暂等待后重试
                        else:
                            logging.error(f"VWAP计算失败 {description}: {e}")

            return None

        except Exception as e:
            logging.error(f"增强VWAP计算失败: {e}")
            return None

    def calculate_vwap_with_retry(self, C, option_code, start_time_str, end_time_str):
        """带重试的VWAP计算"""
        # 获取分笔数据
        tick_data = C.get_market_data_ex(
            fields=['lastPrice', 'volume', 'amount'],
            stock_code=[option_code],
            period='tick',
            start_time=start_time_str,
            end_time=end_time_str,
            count=-1,
            subscribe=True
        )

        if option_code in tick_data and not tick_data[option_code].empty:
            df = tick_data[option_code]

            if len(df) > 0:
                # 使用增强的成交记录计算VWAP
                vwap = self.calculate_vwap_from_trades_enhanced(df)
                return vwap

        return None

    def calculate_vwap_from_trades_enhanced(self, df):
        """增强的成交记录VWAP计算（更宽松的条件）"""
        try:
            # 方法1: 尝试使用成交量增量计算（最精确）
            vwap1 = self.try_vwap_with_volume_delta(df)
            if vwap1 is not None:
                return vwap1

            # 方法2: 使用所有有效价格的简单加权平均（降级方案）
            vwap2 = self.try_vwap_simple_weighted(df)
            if vwap2 is not None:
                return vwap2

            # 方法3: 使用价格简单平均（最后手段）
            vwap3 = self.try_vwap_simple_average(df)
            if vwap3 is not None:
                return vwap3

            return None

        except Exception as e:
            logging.error(f"增强成交记录VWAP计算失败: {e}")
            return None

    def try_vwap_with_volume_delta(self, df):
        """尝试使用成交量增量计算VWAP"""
        try:
            # 只保留有成交的记录
            trades_df = df[(df['volume'] > 0) & (df['lastPrice'] > 0)].copy()

            if len(trades_df) < 2:  # 至少需要2条记录才能计算增量
                return None

            # 计算成交量增量
            trades_df['volume_delta'] = trades_df['volume'].diff().fillna(trades_df['volume'])

            # 过滤掉负增量，但保持宽松条件
            valid_trades = trades_df[trades_df['volume_delta'] > 0]

            if len(valid_trades) > 0:
                total_value = (valid_trades['lastPrice'] * valid_trades['volume_delta']).sum()
                total_volume = valid_trades['volume_delta'].sum()

                if total_volume > 0:
                    return total_value / total_volume

            return None

        except Exception as e:
            logging.debug(f"成交量增量VWAP计算失败: {e}")
            return None

    def try_vwap_simple_weighted(self, df):
        """尝试简单加权平均"""
        try:
            valid_df = df[(df['lastPrice'] > 0)].copy()

            if len(valid_df) == 0:
                return None

            # 使用当前成交量作为权重（如果有的话）
            if 'volume' in valid_df.columns:
                weights = valid_df['volume'].fillna(1)  # 无成交量时权重为1
                weights = weights.replace(0, 1)  # 避免0权重

                weighted_sum = (valid_df['lastPrice'] * weights).sum()
                total_weight = weights.sum()

                if total_weight > 0:
                    return weighted_sum / total_weight

            return None

        except Exception as e:
            logging.debug(f"简单加权VWAP计算失败: {e}")
            return None

    def try_vwap_simple_average(self, df):
        """尝试简单价格平均（最后手段）"""
        try:
            valid_prices = df[df['lastPrice'] > 0]['lastPrice']

            if len(valid_prices) >= 2:  # 至少需要2个价格点
                avg_price = valid_prices.mean()
                logging.warning(f"使用简单价格平均: {avg_price:.4f} (基于{len(valid_prices)}个价格点)")
                return avg_price

            return None

        except Exception as e:
            logging.debug(f"简单价格平均计算失败: {e}")
            return None

    def calculate_precise_vwap(self, C, option_code):
        """计算精确的当日VWAP"""
        try:
            current_time = datetime.now()
            today_str = current_time.strftime('%Y%m%d')
            current_time_str = current_time.strftime('%Y%m%d%H%M%S')

            # 获取分笔数据，从9:32开始避开开盘异常波动
            tick_data = C.get_market_data_ex(
                fields=['lastPrice', 'volume', 'amount'],
                stock_code=[option_code],
                period='tick',
                start_time=today_str + '093200',  # 从9:32开始
                end_time=current_time_str,
                count=-1,
                subscribe=True
            )

            if option_code in tick_data and not tick_data[option_code].empty:
                df = tick_data[option_code]

                if len(df) > 0:
                    # 使用真实成交记录计算VWAP
                    vwap = self.calculate_vwap_from_trades(df)
                    if vwap is not None:
                        logging.info(f"{option_code} 精确VWAP: {vwap:.4f}")
                        return vwap

            return None

        except Exception as e:
            logging.error(f"计算精确VWAP失败: {e}")
            return None

    def calculate_vwap_from_trades(self, df):
        """从真实成交记录计算VWAP"""
        try:
            # 只保留有成交的记录
            trades_df = df[(df['volume'] > 0) & (df['lastPrice'] > 0)].copy()

            if len(trades_df) == 0:
                return None

            # 计算成交量增量
            trades_df['volume_delta'] = trades_df['volume'].diff().fillna(trades_df['volume'])

            # 过滤掉负增量（可能的数据重置）
            trades_df = trades_df[trades_df['volume_delta'] > 0]

            if len(trades_df) == 0:
                return None

            # 计算VWAP = Σ(价格 × 成交量增量) / Σ(成交量增量)
            total_value = (trades_df['lastPrice'] * trades_df['volume_delta']).sum()
            total_volume = trades_df['volume_delta'].sum()

            if total_volume > 0:
                vwap = total_value / total_volume
                return vwap

            return None

        except Exception as e:
            logging.error(f"成交记录VWAP计算失败: {e}")
            return None

    # 已删除 calculate_intraday_vwap_backup() - 未被使用的历史遗留方法

    # 已删除 calculate_vwap_from_tick_data() - 未被使用的历史遗留方法

    # 已删除 calculate_vwap_from_kline_data() - 未被使用的历史遗留方法

    # 已删除 get_fallback_average_price() - 用增强的VWAP计算替代备选方案

    # 已删除 call_qmt_ma_indicator() - 未被使用的实验性方法

    # 已删除 get_moving_average_from_ticks_backup() - 未被使用的备选方法

    def analyze_dual_mode_signal(self, C, option_code, current_price):
        """双模式策略信号分析"""
        try:
            # 获取移动平均价
            avg_price = self.get_moving_average_price_qmt(C, option_code)
            if avg_price is None:
                return None

            # 计算当前偏离度
            deviation = (current_price - avg_price) / avg_price * 100

            # 更新穿线追踪数据
            self.update_cross_tracking(option_code, current_price, avg_price)

            # 模式1: 向上突破模式
            breakthrough_signal = self.check_breakthrough_mode(option_code, current_price, avg_price, deviation)
            if breakthrough_signal:
                return {
                    'signal_type': '突破买入',
                    'mode': 'breakthrough',
                    'avg_price': avg_price,
                    'deviation': deviation,
                    'details': breakthrough_signal
                }

            # 模式2: 向下反弹模式
            rebound_signal = self.check_rebound_mode(option_code, current_price, avg_price, deviation)
            if rebound_signal:
                return {
                    'signal_type': '反弹买入',
                    'mode': 'rebound',
                    'avg_price': avg_price,
                    'deviation': deviation,
                    'details': rebound_signal
                }

            return None

        except Exception as e:
            logging.error(f"双模式策略分析出错: {e}")
            return None

    def update_cross_tracking(self, option_code, current_price, avg_price):
        """更新穿线追踪数据"""
        if option_code not in self.cross_tracking:
            self.cross_tracking[option_code] = {
                'last_cross_direction': None,  # 'up' or 'down'
                'cross_time': None,
                'cross_price': None,
                'high_after_cross_up': None,
                'low_after_cross_down': None,
                'price_history': [],
                'last_high_above_avg': None,  # 最后一个在均线上方的高点
                'last_low_below_avg': None    # 最后一个在均线下方的低点
            }

        tracking = self.cross_tracking[option_code]
        tracking['price_history'].append((current_price, avg_price))

        # 保持历史数据在合理范围内
        if len(tracking['price_history']) > 200:
            tracking['price_history'] = tracking['price_history'][-100:]

        # 更新在均线上方/下方的极值点
        if current_price > avg_price:
            # 在均线上方，更新最高点
            if tracking['last_high_above_avg'] is None:
                tracking['last_high_above_avg'] = current_price
            else:
                tracking['last_high_above_avg'] = max(tracking['last_high_above_avg'], current_price)
        elif current_price < avg_price:
            # 在均线下方，更新最低点
            if tracking['last_low_below_avg'] is None:
                tracking['last_low_below_avg'] = current_price
            else:
                tracking['last_low_below_avg'] = min(tracking['last_low_below_avg'], current_price)

        # 检测穿线事件（基于您的定义）
        if len(tracking['price_history']) >= 2:
            prev_price, prev_avg = tracking['price_history'][-2]
            curr_price, curr_avg = tracking['price_history'][-1]

            # 向上穿线检测：从均线下方的低点向上穿过到均线上方
            if prev_price < prev_avg and curr_price > curr_avg:
                tracking['last_cross_direction'] = 'up'
                tracking['cross_time'] = len(tracking['price_history'])
                tracking['cross_price'] = curr_price
                tracking['high_after_cross_up'] = curr_price
                tracking['low_after_cross_down'] = None
                # 重置均线下方的低点记录
                tracking['last_low_below_avg'] = None

            # 向下穿线检测：从均线上方的高点向下穿过到均线下方
            elif prev_price > prev_avg and curr_price < curr_avg:
                tracking['last_cross_direction'] = 'down'
                tracking['cross_time'] = len(tracking['price_history'])
                tracking['cross_price'] = curr_price
                tracking['low_after_cross_down'] = curr_price
                tracking['high_after_cross_up'] = None
                # 重置均线上方的高点记录
                tracking['last_high_above_avg'] = None

        # 更新穿线后的极值
        if tracking['last_cross_direction'] == 'up' and tracking['high_after_cross_up']:
            tracking['high_after_cross_up'] = max(tracking['high_after_cross_up'], current_price)
        elif tracking['last_cross_direction'] == 'down' and tracking['low_after_cross_down']:
            tracking['low_after_cross_down'] = min(tracking['low_after_cross_down'], current_price)

    def check_breakthrough_mode(self, option_code, current_price, avg_price, deviation):
        """检查突破模式信号"""
        try:
            breakthrough_threshold = self.pm.get('breakthrough_threshold', 3.0)
            max_drawdown = self.pm.get('max_drawdown_from_high', 2.0)

            # 必须向上突破阈值
            if deviation <= breakthrough_threshold:
                return None

            tracking = self.cross_tracking.get(option_code)
            if not tracking or tracking['last_cross_direction'] != 'up':
                return None

            # 检查是否从最高点回调过多
            if tracking['high_after_cross_up']:
                drawdown = (tracking['high_after_cross_up'] - current_price) / tracking['high_after_cross_up'] * 100
                if drawdown > max_drawdown:
                    return None

                return {
                    'breakthrough_pct': deviation,
                    'high_after_cross': tracking['high_after_cross_up'],
                    'drawdown_from_high': drawdown,
                    'cross_price': tracking['cross_price']
                }

            return None

        except Exception as e:
            logging.error(f"突破模式检查出错: {e}")
            return None

    def check_rebound_mode(self, option_code, current_price, avg_price, deviation):
        """检查反弹模式信号"""
        try:
            rebound_threshold = self.pm.get('rebound_threshold', 5.0)

            # 必须低于均线
            if deviation >= 0:
                return None

            tracking = self.cross_tracking.get(option_code)
            if not tracking or tracking['last_cross_direction'] != 'down':
                return None

            # 检查是否有足够的反弹
            if tracking['low_after_cross_down']:
                # 检查反弹幅度（无论之前跌了多少，只要反弹够就买入）
                rebound_pct = (current_price - tracking['low_after_cross_down']) / tracking['low_after_cross_down'] * 100
                if rebound_pct >= rebound_threshold:
                    return {
                        'rebound_pct': rebound_pct,
                        'low_after_cross': tracking['low_after_cross_down'],
                        'cross_price': tracking['cross_price'],
                        'current_deviation': deviation
                    }

            return None

        except Exception as e:
            logging.error(f"反弹模式检查出错: {e}")
            return None

    def print_dual_mode_signal_alert(self, option_code, signal_data, price, timestamp, sequence):
        """打印双模式信号警报"""
        try:
            sequence_str = "->".join([f"{p:.4f}" for p in sequence])
            signal_type = signal_data['signal_type']
            mode = signal_data['mode']
            avg_price = signal_data['avg_price']
            deviation = signal_data['deviation']
            details = signal_data['details']

            # 基础信息
            print(f"\n?? {timestamp} 信号触发: {option_code}.SHO {signal_type} {price:.4f}")
            print(f"   序列: {sequence_str}")
            print(f"   均价: {avg_price:.4f}, 偏离: {deviation:+.2f}%")

            # 模式特定信息
            if mode == 'breakthrough':
                print(f"   突破模式: 突破{details['breakthrough_pct']:.2f}%, "
                      f"最高{details['high_after_cross']:.4f}, "
                      f"回调{details['drawdown_from_high']:.2f}%")
            elif mode == 'rebound':
                print(f"   反弹模式: 反弹{details['rebound_pct']:.2f}%, "
                      f"最低{details['low_after_cross']:.4f}, "
                      f"当前偏离{details['current_deviation']:+.2f}%")

            print()

        except Exception as e:
            logging.error(f"打印双模式信号警报出错: {e}")
            print(f"?? {timestamp} 信号触发: {option_code}.SHO {signal_data.get('signal_type', '未知')} {price:.4f}")

    def should_allow_trend_type(self, trend_analysis):
        """根据趋势分析结果判断是否允许该趋势类型的买入"""
        try:
            if not self.pm.get('enable_trend_filter', True):
                return True  # 如果未启用趋势过滤，则允许所有类型

            if not trend_analysis:
                return True  # 如果无法分析趋势，则允许买入

            price_vs_avg = trend_analysis['price_vs_avg']

            # 根据趋势类型检查对应的开关
            trend_switches = {
                '高于均价向上': self.pm.get('enable_above_avg_up', True),
                '低于均价向上': self.pm.get('enable_below_avg_up', True),
                '高于均价震荡': self.pm.get('enable_above_avg_oscillation', True),
                '低于均价震荡': self.pm.get('enable_below_avg_oscillation', True),
                '高于均价向下': self.pm.get('enable_above_avg_down', False),
                '低于均价向下': self.pm.get('enable_below_avg_down', False),
            }

            return trend_switches.get(price_vs_avg, True)

        except Exception as e:
            logging.error(f"趋势类型检查错误: {e}")
            return True  # 出错时允许买入
    
    def record_signal(self, option_code, signal_type, price, timestamp, sequence, trend_analysis=None):
        """记录信号"""
        if option_code not in self.data.signals:
            self.data.signals[option_code] = []

        signal_info = {
            'type': signal_type,
            'price': price,
            'time': timestamp,
            'sequence': sequence.copy(),
            'trend_analysis': trend_analysis
        }

        self.data.signals[option_code].append(signal_info)
        return signal_info

    # ==================== 震荡检测方法 ====================

    def try_start_oscillation_detection(self, option_code, current_price):
        """尝试启动震荡检测（仅在未激活时启动）"""
        if self.is_oscillation_active(option_code):
            print(f"   震荡检测已激活，跳过重复启动: {option_code}")
            return  # 已经在震荡检测中，不重复启动

        current_tick_id = self.data.current_tick_id.get(option_code, 0)
        print(f"   准备启动震荡检测: {option_code}")
        self.init_oscillation_detection(option_code, current_tick_id, current_price)

    def init_oscillation_detection(self, option_code, start_tick_id, trigger_price):
        """初始化震荡检测"""
        # 震荡检测从下一个tick开始
        next_tick_id = start_tick_id + 1
        self.data.oscillation_data[option_code] = {
            'active': True,
            'start_tick_id': next_tick_id,  # 从下一个tick开始
            'current_period': 1,
            'periods': [],
            'period_size': self.pm.get('oscillation_period_size', 5),
            'required_periods': self.pm.get('oscillation_periods', 3),
            'current_period_ticks': [],  # 空数组，等待下一个tick
            'current_period_start_id': next_tick_id,
            'trigger_price': trigger_price  # 记录触发价格
        }
        period_size = self.data.oscillation_data[option_code]['period_size']
        required_periods = self.data.oscillation_data[option_code]['required_periods']
        print(f"?? 启动震荡检测: {option_code} 从tick#{next_tick_id} (需要{required_periods}个周期,每周期{period_size}tick)")
        print(f"   触发价格: {trigger_price:.4f}, 等待下一个tick开始收集")

    def process_oscillation_tick(self, option_code, tick_id, price):
        """处理震荡模式的tick数据"""
        if not self.is_oscillation_active(option_code):
            return False, None

        data = self.data.oscillation_data[option_code]

        # 只处理start_tick_id及之后的tick
        if tick_id < data['start_tick_id']:
            print(f"?? 震荡检测跳过历史tick: {option_code} tick#{tick_id} < start#{data['start_tick_id']}")
            return False, None

        # 添加到当前周期
        data['current_period_ticks'].append(price)
        print(f"?? 震荡收集tick: {option_code} tick#{tick_id}:{price:.4f}")

        # 显示震荡进度
        current_ticks = len(data['current_period_ticks'])
        period_size = data['period_size']
        current_period = data['current_period']
        completed_periods = len(data['periods'])
        required_periods = data['required_periods']

        # 显示当前周期的价格序列
        current_sequence = data['current_period_ticks'][-min(4, len(data['current_period_ticks'])):]
        print(f"?? 震荡进度: {option_code} 周期{current_period}({current_ticks}/{period_size}tick) 已完成{completed_periods}/{required_periods}周期 当前序列:{current_sequence}")

        # 检查当前周期是否完成
        if len(data['current_period_ticks']) >= data['period_size']:
            period_result = self.complete_current_period(option_code, tick_id)
            if period_result is None:
                return False, None

            # 检查是否达到所需周期数
            if len(data['periods']) >= data['required_periods']:
                signal_type = self.check_oscillation_signal(option_code)
                if signal_type:
                    self.reset_oscillation_detection(option_code)
                    return True, signal_type

        return False, None

    def complete_current_period(self, option_code, tick_id):
        """完成当前周期的检测"""
        data = self.data.oscillation_data[option_code]
        ticks = data['current_period_ticks']

        if len(ticks) < 2:
            self.end_oscillation_detection(option_code, "周期tick数不足")
            return None

        # 计算周期方向：首价格 vs 尾价格
        start_price = ticks[0]
        end_price = ticks[-1]

        if start_price == end_price:
            self.end_oscillation_detection(option_code, "周期首尾价格相等")
            return None

        direction = 1 if end_price > start_price else -1
        direction_name = "上涨" if direction == 1 else "下跌"

        # 检查与前一周期方向是否一致
        if data['periods'] and data['periods'][-1]['direction'] != direction:
            self.end_oscillation_detection(option_code, f"周期方向改变: {direction_name}")
            return None

        # 记录周期结果
        period_info = {
            'period_num': data['current_period'],
            'start_id': data['current_period_start_id'],
            'end_id': tick_id,
            'start_price': start_price,
            'end_price': end_price,
            'direction': direction,
            'direction_name': direction_name,
            'tick_count': len(ticks)
        }

        data['periods'].append(period_info)

        timestamp = self.get_current_timestamp()
        print(f"?? 周期{data['current_period']}完成: {option_code} [{timestamp}] (不重叠设计)")
        print(f"   tick范围:[{data['current_period_start_id']}-{tick_id}] {start_price:.4f}→{end_price:.4f} {direction_name}")
        print(f"   完整序列:{ticks}")
        print(f"   下一周期将从tick#{tick_id + 1}开始")

        # 准备下一周期（不重叠设计）
        data['current_period'] += 1
        data['current_period_ticks'] = []  # 空数组，等待下一个tick
        data['current_period_start_id'] = tick_id + 1  # 从下一个tick开始

        return period_info

    def check_oscillation_signal(self, option_code):
        """检查震荡信号"""
        data = self.data.oscillation_data[option_code]
        periods = data['periods']

        if len(periods) < data['required_periods']:
            return None

        # 检查所有周期方向是否一致
        first_direction = periods[0]['direction']
        if all(p['direction'] == first_direction for p in periods):
            signal_type = "买入" if first_direction == 1 else "卖出"

            print(f"?? 震荡信号触发: {option_code} {signal_type}")
            for i, period in enumerate(periods):
                print(f"  周期{i+1}: [{period['start_id']}-{period['end_id']}] "
                      f"{period['start_price']:.4f}→{period['end_price']:.4f} "
                      f"{period['direction_name']}")

            return signal_type

        return None

    def is_oscillation_active(self, option_code):
        """检查震荡检测是否激活"""
        return (option_code in self.data.oscillation_data and
                self.data.oscillation_data[option_code]['active'])

    def end_oscillation_detection(self, option_code, reason):
        """结束震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            current_period = data['current_period']
            current_ticks = len(data['current_period_ticks'])
            current_sequence = data['current_period_ticks']

            timestamp = self.get_current_timestamp()
            self.data.oscillation_data[option_code]['active'] = False
            print(f"?? 震荡检测结束: {option_code} [{timestamp}] - {reason}")
            print(f"   最终状态: 完成{completed_periods}个周期, 当前周期{current_period}({current_ticks}tick)")
            print(f"   当前周期序列: {current_sequence}")
            if reason == "周期首尾价格相等" and len(current_sequence) >= 2:
                print(f"   首尾价格: {current_sequence[0]:.4f} == {current_sequence[-1]:.4f}")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")

    def reset_oscillation_detection(self, option_code):
        """重置震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            reason = "信号触发成功" if completed_periods >= data['required_periods'] else "连续信号中断"

            del self.data.oscillation_data[option_code]
            print(f"?? 震荡检测重置: {option_code} - {reason}")
            print(f"   完成状态: {completed_periods}个周期完成")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")
    
    def get_current_timestamp(self):
        """获取当前时间戳"""
        return datetime.now().strftime('%H:%M:%S.%f')[:-3]
    
    def print_price_update(self, option_code, timestamp=None):
        """打印价格更新"""
        display_timestamp = self.pm.get('display_timestamp', True)
        signal_threshold = self.pm.get('signal_threshold', 5)
        enable_tick_log = self.pm.get('enable_tick_log', False)

        if timestamp is None:
            timestamp = self.get_current_timestamp()

        if option_code not in self.data.price_chains:
            return

        price_str = "->".join([f"{p:.4f}" for p in self.data.price_chains[option_code]])

        trend_info = ""
        if (option_code in self.data.trend_count and
            self.data.trend_count[option_code] > 0):
            direction_symbol = "↑" if self.data.trend_direction[option_code] == 1 else "↓"
            trend_info = f" [{direction_symbol}{self.data.trend_count[option_code]}/{signal_threshold}]"

        # 根据配置决定是否显示tick日志
        if enable_tick_log:
            if display_timestamp:
                logging.info(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                logging.info(f"{option_code}: {price_str}{trend_info}")
        else:
            # 只在控制台显示，不记录到日志
            if display_timestamp:
                print(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                print(f"{option_code}: {price_str}{trend_info}")
    
    def print_signal_alert(self, option_code, signal_type, price, timestamp, sequence, trend_analysis=None):
        """打印信号警报"""
        sequence_str = "->".join([f"{p:.4f}" for p in sequence])

        # 添加趋势分析信息
        trend_info = ""
        if trend_analysis:
            trend_info = f" 趋势:{trend_analysis['price_vs_avg']} (偏离均价{trend_analysis['price_deviation']:+.2f}%)"

        alert_msg = f"?? [{timestamp}] 触发{signal_type}信号！{option_code}: {sequence_str} (价格: {price:.4f}){trend_info}"

        # 信号警报：重要事件，记录到日志
        logging.warning(f"信号触发: {option_code} {signal_type} {price:.4f}{trend_info}")
        print(alert_msg)
    
    def validate_tick_timestamp(self, option_code, current_time):
        """验证tick时间戳顺序"""
        try:
            if option_code not in self.data.last_tick_time:
                self.data.last_tick_time[option_code] = current_time
                return True

            last_time = self.data.last_tick_time[option_code]

            # 简单的时间戳验证（假设时间戳是字符串格式）
            if isinstance(current_time, str) and isinstance(last_time, str):
                if current_time < last_time:
                    print(f"?? 时间戳倒退: {option_code} {last_time} -> {current_time}")
                    return False

            self.data.last_tick_time[option_code] = current_time
            return True

        except Exception as e:
            logging.error(f"时间戳验证错误: {e}")
            return True  # 验证失败时允许通过，避免阻塞

    def process_tick_data(self, C, option_code, current_price, current_time):
        """处理tick数据的完整流程"""
        try:
            # 0. 验证时间戳顺序
            if not self.validate_tick_timestamp(option_code, current_time):
                return

            # 1. 过滤重复tick
            if not self.filter_duplicate_ticks(option_code, current_price, current_time):
                return

            # 2. 分配有效的tick ID（过滤后才分配）
            if option_code not in self.data.current_tick_id:
                self.data.current_tick_id[option_code] = 0
            self.data.current_tick_id[option_code] += 1
            current_tick_id = self.data.current_tick_id[option_code]

            # 2. 更新价格链
            price_chain = self.update_price_chain(option_code, current_price)
            if not price_chain:
                return

            current_price_rounded = price_chain[-1]

            # 初始化信号变量
            has_continuous_signal = False
            has_oscillation_signal = False
            continuous_signal_type = None
            oscillation_signal_type = None

            # 3. 双模式策略信号检测
            if self.pm.get('enable_dual_mode', True):
                # 使用新的双模式策略
                dual_mode_signal = self.analyze_dual_mode_signal(C, option_code, current_price_rounded)

                if dual_mode_signal:
                    signal_type = dual_mode_signal['signal_type']

                    # 记录信号
                    self.record_signal(option_code, signal_type, current_price_rounded,
                                     current_time, price_chain[-5:], dual_mode_signal)

                    # 打印信号警报
                    self.print_dual_mode_signal_alert(option_code, dual_mode_signal,
                                                    current_price_rounded, current_time, price_chain[-5:])

                    # 执行买入
                    self.execute_buy_order(C, option_code, signal_type, current_price_rounded, dual_mode_signal)

                    return

            # 兼容旧策略（逐步废弃）
            else:
                # 3. 检测连续趋势方向
                direction, count = self.detect_trend_direction(option_code, current_price_rounded)

                # 5. 检查连续趋势信号
                has_continuous_signal, continuous_signal_type = self.check_trend_signal(option_code)

                # 6. 并行检查震荡信号（两种模式独立运行）
                has_oscillation_signal, oscillation_signal_type = self.process_oscillation_tick(
                    option_code, current_tick_id, current_price_rounded)

                # 7. 趋势分析和过滤（使用QMT API）
                trend_analysis = None
                if has_continuous_signal or has_oscillation_signal:
                    trend_analysis = self.analyze_price_trend_qmt(C, option_code, current_price_rounded)

                    # 检查是否允许该趋势类型的买入
                    if not self.should_allow_trend_type(trend_analysis):
                        if trend_analysis:
                            data_source = trend_analysis.get('data_source', 'UNKNOWN')
                        print(f"?? 趋势过滤({data_source}): {option_code} {trend_analysis['price_vs_avg']} 被过滤 "
                              f"(偏离均价{trend_analysis['price_deviation']:+.2f}%)")
                    # 重置信号，不执行买入
                    has_continuous_signal = False
                    has_oscillation_signal = False
                elif trend_analysis:
                    print(f"? 趋势通过: {option_code} {trend_analysis['price_vs_avg']} "
                          f"(偏离均价{trend_analysis['price_deviation']:+.2f}%)")

            # 8. 获取时间戳
            timestamp = self.get_current_timestamp()

            # 9. 处理信号（允许两种信号同时触发）
            if has_continuous_signal:
                trigger_sequence = self.data.trend_prices[option_code][-5:]
                self.record_signal(option_code, continuous_signal_type, current_price_rounded, timestamp, trigger_sequence, trend_analysis)
                self.print_signal_alert(option_code, f"连续{continuous_signal_type}", current_price_rounded, timestamp, trigger_sequence, trend_analysis)

                # 执行买入交易（只处理买入信号）
                if continuous_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"连续{continuous_signal_type}", current_price_rounded, trend_analysis)

                self.reset_trend_detection(option_code, current_price_rounded)
                # 连续信号触发时，也重置震荡检测
                if self.is_oscillation_active(option_code):
                    self.reset_oscillation_detection(option_code)

            if has_oscillation_signal:
                # 震荡信号触发（可与连续信号同时触发）
                trigger_sequence = [current_price_rounded]
                self.record_signal(option_code, oscillation_signal_type, current_price_rounded, timestamp, trigger_sequence, trend_analysis)
                self.print_signal_alert(option_code, f"震荡{oscillation_signal_type}", current_price_rounded, timestamp, trigger_sequence, trend_analysis)

                # 执行买入交易（只处理买入信号）
                if oscillation_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded, trend_analysis)

            # 10. 打印价格更新
            self.print_price_update(option_code, timestamp)

        except Exception as e:
            logging.error(f"处理tick数据错误: {e}")

    # ==================== 交易功能模块 ====================
    def execute_buy_order(self, C, option_code, signal_type, current_price, trend_analysis=None):
        """执行买入委托 - 支持部分成交和批次管理"""
        try:
            # 检查是否启用真实交易
            enable_trading = self.pm.get('enable_real_trading', False)
            print(f"?? 交易开关检查: enable_real_trading = {enable_trading} (类型: {type(enable_trading)})")

            # 添加趋势信息到日志
            trend_info = ""
            if trend_analysis:
                trend_info = f" 趋势:{trend_analysis['price_vs_avg']} (偏离均价{trend_analysis['price_deviation']:+.2f}%)"

            if not enable_trading:
                print(f"?? 模拟交易: {option_code} {signal_type} 价格:{current_price:.4f}{trend_info} (真实交易未启用)")
                return

            print(f"?? 准备执行买入: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 获取交易参数 - 强制类型转换
            max_position_raw = self.pm.get('max_position_per_contract', 10)
            print(f"?? 原始参数: max_position_per_contract = {max_position_raw} (类型: {type(max_position_raw)})")

            # 强制转换为整数
            if isinstance(max_position_raw, bool):
                max_position = 10  # 布尔类型时使用默认值
            elif isinstance(max_position_raw, str):
                max_position = int(max_position_raw) if max_position_raw.isdigit() else 10
            elif isinstance(max_position_raw, (int, float)):
                max_position = int(max_position_raw)
            else:
                max_position = 10

            print(f"?? 转换后参数: max_position_per_contract = {max_position} (类型: {type(max_position)})")

            # 执行买入前检查
            if not self.check_buy_conditions_with_batch(C, option_code, max_position):
                return

            # 获取市场数据
            market_data = self.get_market_data(C, option_code)
            if not market_data:
                print(f"? 无法获取市场数据: {option_code}")
                return

            # 检查卖1数据有效性
            if market_data['ask_price'] <= 0 or market_data['ask_volume'] <= 0:
                print(f"? 卖1数据无效: {option_code} 价格:{market_data['ask_price']} 量:{market_data['ask_volume']}")
                return

            # 计算买入数量 - 考虑部分成交情况
            buy_quantity = self.calculate_buy_quantity_with_batch(C, option_code, market_data, max_position)
            if buy_quantity <= 0:
                print(f"? 买入数量为0: {option_code}")
                return

            # 执行买入委托
            self.place_buy_order_with_batch_management(C, option_code, buy_quantity, market_data, signal_type, max_position)

        except Exception as e:
            print(f"? 买入委托异常: {option_code} {e}")
            logging.error(f"买入委托异常: {option_code} {e}")

    def check_buy_conditions_with_batch(self, C, option_code, max_position):
        """检查买入条件 - 使用批次管理器 + 实际持仓验证"""
        try:
            print(f"?? 检查买入条件: {option_code}")

            # 1. 获取本地记录的持仓
            local_position = self.data.batch_manager.get_total_position(option_code)

            # 2. 获取QMT实际持仓进行验证
            real_position = self.get_current_position(C, option_code)

            # 3. 持仓同步逻辑
            if local_position != real_position:
                print(f"?? 检测到持仓不一致: 本地记录{local_position} vs QMT实际{real_position}")
                self.sync_position_records(C, option_code, local_position, real_position)
                # 更新后重新获取本地持仓
                local_position = self.data.batch_manager.get_total_position(option_code)
                print(f"?? 同步完成，当前本地持仓: {local_position}")

            # 4. 使用同步后的持仓进行判断
            current_position = local_position
            pending_quantity = len(self.data.batch_manager.pending_orders)

            print(f"   当前持仓:{current_position} 待成交:{pending_quantity} 最大:{max_position}")

            if current_position + pending_quantity >= max_position:
                print(f"? 持仓已满: {option_code} 当前:{current_position} 待成交:{pending_quantity} 最大:{max_position}")
                return False

            print(f"? 买入条件检查通过: {option_code}")
            return True

        except Exception as e:
            print(f"? 买入条件检查异常: {option_code} {e}")
            return False

    def sync_position_records(self, C, option_code, local_position, real_position):
        """同步本地批次记录与实际持仓"""
        try:
            if real_position == 0 and local_position > 0:
                # 情况1: 实际已清仓，但本地还有记录 → 清除本地记录
                print(f"?? 实际已清仓，清除本地批次记录: {option_code}")
                self.data.batch_manager.clear_option_batches(option_code)

            elif real_position > 0 and local_position == 0:
                # 情况2: 实际有持仓，但本地无记录 → 创建估算批次
                print(f"?? 实际有持仓但本地无记录，创建估算批次: {option_code} 数量:{real_position}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, real_position, estimated_price)

            elif real_position > 0 and local_position > real_position:
                # 情况3: 部分平仓 → 调整本地记录
                closed_quantity = local_position - real_position
                print(f"?? 检测到部分平仓，调整本地记录: {option_code} 平仓数量:{closed_quantity}")
                self.data.batch_manager.adjust_batches_for_partial_close(option_code, closed_quantity)

            elif real_position > local_position:
                # 情况4: 实际持仓更多 → 补充记录
                additional_quantity = real_position - local_position
                print(f"?? 实际持仓更多，补充批次记录: {option_code} 补充数量:{additional_quantity}")
                estimated_price = self.get_current_market_price(C, option_code)
                self.data.batch_manager.create_estimated_batch(option_code, additional_quantity, estimated_price)

        except Exception as e:
            print(f"? 持仓同步异常: {option_code} {e}")

    def get_current_market_price(self, C, option_code):
        """获取当前市价（用于估算批次）"""
        try:
            # 尝试获取最新价格
            tick_data = C.get_full_tick([option_code])
            if option_code in tick_data:
                price = tick_data[option_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)

            # 如果获取失败，返回默认估算价格
            print(f"?? 无法获取 {option_code} 的当前价格，使用默认估算价格")
            return 0.05  # 默认估算价格

        except Exception as e:
            print(f"? 获取市价失败: {option_code} {e}")
            return 0.05  # 默认估算价格

    def calculate_buy_quantity_with_batch(self, C, option_code, market_data, max_position):
        """计算买入数量 - 考虑部分成交和未完成数量"""
        try:
            current_position = self.data.batch_manager.get_total_position(option_code)
            pending_quantity = len(self.data.batch_manager.pending_orders)

            # 检查是否有未完成的买入数量
            remaining_quantity = self.data.pending_buy_quantities.get(option_code, 0)
            if remaining_quantity > 0:
                print(f"?? 发现未完成买入: {option_code} 剩余数量:{remaining_quantity}")
                target_quantity = min(remaining_quantity, market_data['ask_volume'])
            else:
                available_quantity = max_position - current_position - pending_quantity
                target_quantity = min(available_quantity, market_data['ask_volume'])

            print(f"?? 买入数量计算: {option_code} 卖1量:{market_data['ask_volume']} "
                  f"当前持仓:{current_position} 最大:{max_position} 目标:{target_quantity}")

            return max(0, target_quantity)

        except Exception as e:
            print(f"? 计算买入数量异常: {option_code} {e}")
            return 0

    def place_buy_order_with_batch_management(self, C, option_code, quantity, market_data, signal_type, max_position):
        """下买入委托并管理批次"""
        try:
            # 执行买入委托 - passorder是全局函数，不是C对象的方法
            order_result = passorder(
                50,                              # op_type: 50=期权买入开仓
                1101,                            # order_mode: 1101=按股数
                account,                         # account_id: 全局账户变量
                option_code,                     # contract: 合约代码
                5,                               # price_type: 5=市价
                market_data['ask_price'],        # exec_price: 执行价格（市价单也需要填写）
                quantity,                        # volume: 数量
                "期权策略",                       # strategy_name: 策略名称
                2,                               # quicktrade: 2=立即下单
                f"期权买入-{option_code}",        # msg: 备注信息
                C                                # C: 上下文对象作为最后一个参数
            )

            print(f"? 委托下单请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")

            print(f"?? 委托返回结果: {order_result} (类型: {type(order_result)})")

            # 根据QMT官方文档：passorder返回值是'无'，不能依赖返回值判断成功失败
            # QMT使用异步交易机制，委托状态通过回调函数获取
            print(f"?? 委托请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")
            print("? 等待回调函数确认委托状态...")

            # 记录委托尝试（不依赖返回值）
            try:
                # 生成临时委托ID，等待回调函数更新为真实委托号
                temp_order_id = f"{option_code}_{int(time.time())}"

                self.data.batch_manager.add_pending_order(
                    temp_order_id, option_code, market_data['ask_price'], quantity
                )
                print(f"?? 委托记录成功: {temp_order_id}")
                print("?? 等待order_callback和deal_callback确认...")

                # 记录委托时间，用于后续验证
                if not hasattr(self.data, 'pending_verifications'):
                    self.data.pending_verifications = []

                self.data.pending_verifications.append({
                    'temp_order_id': temp_order_id,
                    'option_code': option_code,
                    'target_quantity': quantity,
                    'target_price': market_data['ask_price'],
                    'timestamp': time.time()
                })

            except Exception as e:
                print(f"?? 记录委托失败: {e}")

            # 处理未完成数量
            current_position = self.data.batch_manager.get_total_position(option_code)
            original_target = max_position - current_position
            if quantity < original_target:
                # 记录未完成的数量，等待下次信号
                self.data.pending_buy_quantities[option_code] = original_target - quantity
                print(f"?? 记录未完成买入: {option_code} 剩余:{self.data.pending_buy_quantities[option_code]}")
            else:
                # 清除未完成数量
                self.data.pending_buy_quantities.pop(option_code, None)

        except Exception as e:
            print(f"? 下单异常: {option_code} {e}")
            logging.error(f"下单异常: {option_code} {e}")



    def get_market_data(self, C, option_code):
        """获取市场数据"""
        try:
            # 方法1: 尝试使用get_market_data_ex获取五档行情
            try:
                market_data = C.get_market_data_ex([option_code], period='tick', count=1, subscribe=True)
                if market_data and option_code in market_data and len(market_data[option_code]) > 0:
                    tick_data = market_data[option_code][-1]

                    # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                    ask_prices = tick_data.get('askPrice', [])
                    ask_volumes = tick_data.get('askVol', [])
                    bid_prices = tick_data.get('bidPrice', [])
                    bid_volumes = tick_data.get('bidVol', [])

                    result = {
                        'ask_price': ask_prices[0] if ask_prices else 0,      # 卖1价
                        'ask_volume': ask_volumes[0] if ask_volumes else 0,    # 卖1量
                        'bid_price': bid_prices[0] if bid_prices else 0,      # 买1价
                        'bid_volume': bid_volumes[0] if bid_volumes else 0,    # 买1量
                        'last_price': tick_data.get('lastPrice', 0)           # 最新价
                    }

                    print(f"?? 市场数据(订阅): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                    return result
            except Exception as e1:
                print(f"?? 订阅方式获取行情失败: {e1}")

            # 方法2: 使用get_full_tick获取基础行情
            full_tick = C.get_full_tick([option_code])
            if full_tick and option_code in full_tick:
                tick_data = full_tick[option_code]

                # 根据官方文档，五档行情字段为askPrice/askVol (list类型)
                ask_prices = tick_data.get('askPrice', [])
                ask_volumes = tick_data.get('askVol', [])
                bid_prices = tick_data.get('bidPrice', [])
                bid_volumes = tick_data.get('bidVol', [])

                result = {
                    'ask_price': ask_prices[0] if ask_prices else tick_data.get('lastPrice', 0),      # 卖1价，如果没有则用最新价
                    'ask_volume': ask_volumes[0] if ask_volumes else 100,    # 卖1量，如果没有则假设100
                    'bid_price': bid_prices[0] if bid_prices else tick_data.get('lastPrice', 0),      # 买1价，如果没有则用最新价
                    'bid_volume': bid_volumes[0] if bid_volumes else 100,    # 买1量，如果没有则假设100
                    'last_price': tick_data.get('lastPrice', 0)      # 最新价
                }

                print(f"?? 市场数据(全推): {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
                return result

            print(f"? 无法获取市场数据: {option_code}")
            return None

        except Exception as e:
            print(f"? 获取市场数据异常: {option_code} {e}")
            return None





    def get_current_position(self, C, option_code):
        """获取当前持仓"""
        try:
            # 使用QMT API获取持仓 - get_trade_detail_data是全局函数
            positions = get_trade_detail_data(account, 'STOCK_OPTION', 'POSITION', C)
            if not positions:
                return 0

            for position in positions:
                if hasattr(position, 'm_strInstrumentID') and position.m_strInstrumentID == option_code.split('.')[0]:
                    return getattr(position, 'm_nVolume', 0)

            return 0

        except Exception as e:
            print(f"? 获取持仓异常: {option_code} {e}")
            return 0



    def get_available_cash(self, C):
        """获取可用资金"""
        try:
            # 使用QMT API获取账户资金 - get_trade_detail_data是全局函数
            accounts = get_trade_detail_data(account, 'STOCK_OPTION', 'ACCOUNT', C)
            if accounts and len(accounts) > 0:
                return getattr(accounts[0], 'm_dAvailable', 0)
            return 0

        except Exception as e:
            print(f"? 获取可用资金异常: {e}")
            return 0





    def check_pending_verifications(self, C):
        """检查待验证的委托（备用方案）"""
        if not hasattr(self.data, 'pending_verifications'):
            return

        # ?? 首先检查委托超时并撤单
        self.check_and_cancel_timeout_orders(C)

        current_time = time.time()

        for verification in self.data.pending_verifications[:]:  # 复制列表避免修改问题
            # 如果超过30秒还没有回调，主动查询
            if current_time - verification['timestamp'] > 30:
                print(f"? 委托超时，启动主动验证: {verification['temp_order_id']}")

                try:
                    # 查询持仓变化
                    option_code = verification['option_code']
                    current_position = self.get_current_position(option_code, C)

                    # 如果持仓增加，推断交易成功
                    if current_position > 0:
                        print(f"?? 检测到持仓增加: {option_code} 持仓:{current_position}")
                        print("?? 推断交易成功，手动创建批次记录")

                        # 手动创建批次记录
                        self.create_manual_batch(verification)

                    else:
                        print(f"?? 未检测到持仓变化: {option_code}")
                        print("?? 可能交易失败或还在处理中")

                except Exception as e:
                    print(f"? 主动验证失败: {e}")

                # 移除已检查的项目
                self.data.pending_verifications.remove(verification)

    def create_manual_batch(self, verification):
        """手动创建批次记录（当回调函数不工作时）"""
        try:
            # 创建手动批次
            batch = {
                'batch_id': len(self.data.batch_manager.batches) + 1,
                'option_code': verification['option_code'],
                'order_id': verification['temp_order_id'],
                'entry_price': verification['target_price'],  # 使用目标价格
                'quantity': verification['target_quantity'],   # 使用目标数量
                'cost': verification['target_price'] * verification['target_quantity'] * 10000,
                'commission': 5.0,  # 估算手续费
                'trade_time': time.strftime('%H:%M:%S'),
                'trade_date': time.strftime('%Y-%m-%d'),
                'timestamp': time.time(),
                'source': 'manual_verification'  # 标记为手动验证创建
            }

            self.data.batch_manager.batches.append(batch)

            print(f"?? 手动批次创建成功: {verification['option_code']}")
            print(f"   批次#{batch['batch_id']} 价格:{batch['entry_price']:.4f} 数量:{batch['quantity']}")
            print(f"   来源: 主动验证（回调函数未触发）")

            # 显示当前持仓汇总
            total_position = self.data.batch_manager.get_total_position(verification['option_code'])
            avg_cost = self.data.batch_manager.get_average_cost(verification['option_code'])
            print(f"?? 持仓汇总: {verification['option_code']}")
            print(f"   总持仓: {total_position}")
            print(f"   平均成本: {avg_cost:.4f}")

        except Exception as e:
            print(f"? 手动批次创建失败: {e}")

    def check_and_cancel_timeout_orders(self, C):
        """检查并撤销超时委托"""
        try:
            if not hasattr(self.data, 'batch_manager') or not self.data.batch_manager.pending_orders:
                return

            current_time = time.time()
            timeout_seconds = self.pm.get('order_timeout_seconds', 5)  # 获取超时设置，默认5秒
            timeout_orders = []

            # 检查所有待成交委托
            for order_id, order_info in self.data.batch_manager.pending_orders.items():
                if order_info['status'] == 'pending':
                    elapsed_time = current_time - order_info['timestamp']
                    if elapsed_time > timeout_seconds:
                        # ?? 额外安全检查：如果有真实委托号，先检查是否已经有对应的批次记录
                        real_order_id = order_info.get('real_order_id')
                        if real_order_id:
                            # 检查是否已经有该委托号的批次记录（说明已成交）
                            already_executed = any(
                                batch.get('order_id') == real_order_id
                                for batch in self.data.batch_manager.batches
                            )
                            if already_executed:
                                print(f"?? 跳过已成交委托的撤单: {order_id} 真实委托号:{real_order_id}")
                                # 直接清理该待成交记录，不执行撤单
                                self.data.batch_manager.pending_orders[order_id]['status'] = 'completed'
                                continue

                        timeout_orders.append((order_id, order_info, elapsed_time))

            # 撤销超时委托
            for order_id, order_info, elapsed_time in timeout_orders:
                option_code = order_info['option_code']
                print(f"? 检测到超时委托: {option_code} 委托:{order_id} 已等待:{elapsed_time:.1f}秒")

                # 获取真实委托号（如果有的话）
                real_order_id = order_info.get('real_order_id', order_id)

                # 尝试撤销委托
                if self.cancel_order(C, real_order_id):
                    print(f"? 撤销超时委托成功: {option_code} 真实委托号:{real_order_id}")
                    # 更新委托状态
                    self.data.batch_manager.pending_orders[order_id]['status'] = 'cancelled'
                    # 从待成交列表中移除
                    del self.data.batch_manager.pending_orders[order_id]
                else:
                    print(f"? 撤销委托失败: {option_code} 真实委托号:{real_order_id}")

            # ?? 清理已完成状态的委托记录
            completed_orders = [
                order_id for order_id, order_info in self.data.batch_manager.pending_orders.items()
                if order_info['status'] == 'completed'
            ]
            for order_id in completed_orders:
                del self.data.batch_manager.pending_orders[order_id]
                print(f"?? 清理已完成委托记录: {order_id}")

        except Exception as e:
            print(f"? 检查超时委托异常: {e}")

    def cancel_order(self, C, order_id):
        """撤销委托"""
        try:
            # 使用QMT API撤销委托 - cancel是全局函数
            cancel_result = cancel(order_id, account, 'STOCK_OPTION', C)

            if cancel_result:
                print(f"?? 撤单API调用成功: {order_id}")
                return True
            else:
                print(f"?? 撤单API调用失败: {order_id}")
                return False

        except Exception as e:
            print(f"? 撤销委托异常: {order_id} {e}")
            return False

# ==================== 全局监控对象 ====================
monitor = OptionMonitor()

# ==================== QMT全局变量 ====================
# 在QMT策略交易界面运行时，account的值会被自动赋值为策略配置中的账号
# 在编辑器界面运行时，需要手动赋值
account = "test"  # 这个值在实际运行时会被QMT自动替换

# ==================== QMT主程序 ====================
def init(C):
    """初始化函数"""
    # ?? 关键：设置账户以启用回调函数
    try:
        C.set_account(account)
        print(f"? 账户设置成功: {account}")
        print("?? 回调函数已启用 (order_callback, deal_callback)")
    except Exception as e:
        print(f"? 账户设置失败: {e}")
        print("?? 回调函数可能无法正常工作")

    # 初始化信息保留print以便在QMT控制台清楚显示
    print("=== QMT期权监控系统启动 (简化版) ===")
    print(f"?? 监控标的: {monitor.data.underlying_code}")
    print("?? 交易账号: 请在QMT策略交易界面选择")

    # 显示核心参数
    signal_threshold = monitor.pm.get('signal_threshold', 5)

    print(f"?? 核心参数:")
    print(f"  - 标的代码: {monitor.data.underlying_code}")
    print(f"  - 信号阈值: 连续{signal_threshold}个同方向tick触发信号")
    print(f"  - 配置文件: {monitor.pm.xml_path}")

    print(f"\n?? 固定设置:")
    print(f"  - 价格链长度: 30个tick (滑动窗口)")
    print(f"  - 重复过滤: 启用 (过滤相邻重复价格)")
    print(f"  - 时间戳显示: 启用")
    print(f"  - 期权选择: 1个认购 + 1个认沽 (最近到期+最近行权价)")

    print("\n?? QMT系统参数设置提醒:")
    print("  - 默认品种: 请在策略编辑器'基本信息'中设置为 510300")
    print("  - 默认周期: 请在策略编辑器'基本信息'中设置为 1分钟")
    print("  - 交易账号: 请在策略交易界面选择有期权权限的账号")

    print("=" * 50)

    # 记录启动信息到日志
    logging.info(f"系统启动 - 标的:{monitor.data.underlying_code} 阈值:{signal_threshold}")

def after_init(C):
    """初始化后执行"""
    try:
        print("?? 开始选择期权合约...")

        # 选择期权合约
        selected_options = monitor.select_best_options(C)
        if not selected_options:
            print("? 未能选择到合适的期权合约")
            return

        monitor.data.selected_options = selected_options
        print(f"? 成功选择 {len(selected_options)} 个期权合约")

        # 定义tick回调函数
        def tick_callback(data):
            """tick数据回调函数"""
            try:
                # 检查待验证的委托（备用方案）
                monitor.check_pending_verifications(C)

                for option_code in data:
                    if option_code in monitor.data.selected_options:
                        tick_info = data[option_code]
                        current_price = tick_info.get('lastPrice', 0)
                        current_time = tick_info.get('timetag', '')

                        # 处理tick数据
                        monitor.process_tick_data(C, option_code, current_price, current_time)

            except Exception as e:
                print(f"? tick回调错误: {e}")
                logging.error(f"tick回调错误: {e}")

        # 订阅期权合约
        success_count = 0
        for option_code in selected_options:
            try:
                C.subscribe_quote(option_code, period='tick', callback=tick_callback)
                print(f"? 成功订阅合约: {option_code}")
                success_count += 1
            except Exception as e:
                print(f"? 订阅合约 {option_code} 失败: {e}")
                logging.error(f"订阅失败: {option_code} - {e}")

        print(f"?? 订阅完成: {success_count}/{len(selected_options)} 个合约")
        print("?? 开始监控...")

        signal_threshold = monitor.pm.get('signal_threshold', 5)
        print(f"?? 监控规则: 连续{signal_threshold}个同方向tick触发信号")

    except Exception as e:
        print(f"? 初始化失败: {e}")
        logging.error(f"初始化失败: {e}")

def handlebar(C):
    """K线回调函数（在这个场景下不使用）"""
    pass

# ==================== 工具函数 ====================
def update_parameter(param_name, new_value):
    """运行时更新参数"""
    try:
        monitor.pm.set(param_name, new_value)
        print(f"? 参数已更新: {param_name} = {new_value}")
    except Exception as e:
        print(f"? 参数更新失败: {e}")

def show_parameters():
    """显示当前参数"""
    monitor.pm.print_params()

def get_strategy_status():
    """获取策略状态"""
    selected_count = len(monitor.data.selected_options)
    total_signals = sum(len(signals) for signals in monitor.data.signals.values())
    active_trends = sum(1 for count in monitor.data.trend_count.values() if count > 0)

    print(f"?? 策略状态:")
    print(f"  - 监控合约: {selected_count} 个")
    print(f"  - 总信号数: {total_signals} 个")
    print(f"  - 活跃趋势: {active_trends} 个")

    # 显示每个合约的信号统计
    for option_code in monitor.data.selected_options:
        if option_code in monitor.data.signals:
            signals = monitor.data.signals[option_code]
            buy_count = sum(1 for s in signals if s['type'] == '买入')
            sell_count = sum(1 for s in signals if s['type'] == '卖出')
            print(f"  - {option_code}: 买入{buy_count}次, 卖出{sell_count}次")

def emergency_stop():
    """紧急停止"""
    print("?? 执行紧急停止...")
    monitor.data.selected_options.clear()
    monitor.data.trend_count.clear()
    monitor.data.trend_direction.clear()
    monitor.data.trend_prices.clear()
    print("? 策略已停止，所有状态已清空")

# ==================== QMT回调函数 ====================
def deal_callback(ContextInfo, dealInfo):
    try:
        # 检查是否是我们的期权策略成交
        if hasattr(dealInfo, 'm_strRemark') and dealInfo.m_strRemark and "期权买入" in dealInfo.m_strRemark:

            # 获取成交信息
            option_code = dealInfo.m_strInstrumentID + "." + dealInfo.m_strExchangeID
            order_id = dealInfo.m_strOrderSysID
            deal_price = dealInfo.m_dPrice
            deal_volume = dealInfo.m_nVolume
            deal_amount = dealInfo.m_dTradeAmount
            commission = dealInfo.m_dCommission
            trade_time = dealInfo.m_strTradeTime

            print(f"?? 成交回调触发: {option_code}")
            print(f"   委托号: {order_id}")
            print(f"   成交价: {deal_price:.4f}")
            print(f"   成交量: {deal_volume}")
            print(f"   成交额: {deal_amount:.2f}")
            print(f"   手续费: {commission:.2f}")
            print(f"   成交时间: {trade_time}")

            # 记录实际成交批次
            batch = monitor.data.batch_manager.add_batch_from_deal(dealInfo)

            print(f"?? 期权成交确认: {option_code} 批次#{batch['batch_id']}")
            print(f"   实际价格: {batch['entry_price']:.4f}")
            print(f"   数量: {batch['quantity']}")
            print(f"   成交金额: {batch['cost']:.2f}")

            # 显示当前持仓汇总
            total_position = monitor.data.batch_manager.get_total_position(option_code)
            avg_cost = monitor.data.batch_manager.get_average_cost(option_code)
            print(f"?? 持仓汇总: {option_code}")
            print(f"   总持仓: {total_position}")
            print(f"   平均成本: {avg_cost:.4f}")

    except Exception as e:
        print(f"? 成交回调异常: {e}")
        logging.error(f"成交回调异常: {e}")

def order_callback(ContextInfo, orderInfo):
    try:
        # 检查是否是我们的期权策略委托
        if hasattr(orderInfo, 'm_strRemark') and orderInfo.m_strRemark and "期权买入" in orderInfo.m_strRemark:

            # 获取关键信息
            order_id = orderInfo.m_strOrderSysID
            option_code = orderInfo.m_strInstrumentID + "." + orderInfo.m_strExchangeID
            order_status = orderInfo.m_nOrderStatus
            volume_traded = orderInfo.m_nVolumeTraded
            volume_total = orderInfo.m_nVolumeTotal
            limit_price = orderInfo.m_dLimitPrice

            # 根据官方文档的委托状态枚举
            status_map = {
                49: "待报",
                50: "已报",
                51: "已报待撤",
                52: "部成待撤",
                53: "部撤",
                54: "已撤",
                55: "部成",
                56: "已成",
                57: "废单"
            }

            status_name = status_map.get(order_status, f"未知状态({order_status})")

            print(f"?? 委托状态变化: {option_code}")
            print(f"   委托号: {order_id}")
            print(f"   状态: {status_name} ({order_status})")
            print(f"   成交量: {volume_traded}/{volume_total}")
            print(f"   委托价: {limit_price:.4f}")

            # 处理不同状态
            if order_status == 56:  # 已成
                print(f"? 委托全部成交: {option_code} {order_id}")

            elif order_status == 55:  # 部成
                print(f"?? 委托部分成交: {option_code} {order_id} 已成交:{volume_traded}")

            elif order_status == 54:  # 已撤
                print(f"?? 委托已撤销: {option_code} {order_id}")
                cancelled_order = monitor.data.batch_manager.handle_cancelled_order(order_id)
                if cancelled_order:
                    print(f"   撤销数量: {cancelled_order['target_quantity']}")

            elif order_status == 57:  # 废单
                error_msg = getattr(orderInfo, 'm_strErrorMsg', '未知错误')
                print(f"? 委托废单: {option_code} {order_id}")
                print(f"   废单原因: {error_msg}")

            elif order_status == 50:  # 已报
                print(f"?? 委托已报: {option_code} {order_id} 等待成交")

                # ?? 关键：记录真实委托号，用于后续撤单
                # 查找对应的临时委托ID并更新为真实委托号
                remark = getattr(orderInfo, 'm_strRemark', '')
                if '期权买入' in remark:
                    # 更新pending_orders中的委托记录
                    for temp_order_id, order_info in monitor.data.batch_manager.pending_orders.items():
                        if (order_info['option_code'] == option_code and
                            order_info['status'] == 'pending'):
                            # 更新为真实委托号
                            order_info['real_order_id'] = order_id
                            order_info['order_status'] = 50  # 已报状态
                            print(f"?? 更新委托记录: {temp_order_id} -> 真实委托号:{order_id}")
                            break

    except Exception as e:
        print(f"? 委托回调异常: {e}")
        logging.error(f"委托回调异常: {e}")

def orderError_callback(ContextInfo, orderArgs, errMsg):
    try:
        print(f"?? 下单异常回调触发")
        print(f"   账户: {orderArgs.accountID}")
        print(f"   证券: {orderArgs.orderCode}")
        print(f"   操作类型: {orderArgs.opType}")
        print(f"   价格: {orderArgs.modelPrice}")
        print(f"   数量: {orderArgs.modelVolume}")
        print(f"   错误信息: {errMsg}")

        # 如果是期权策略的下单异常
        if "期权" in orderArgs.strategyName:
            print(f"? 期权策略下单异常: {orderArgs.orderCode}")
            print(f"   详细错误: {errMsg}")

    except Exception as e:
        print(f"? 下单异常回调处理异常: {e}")
        logging.error(f"下单异常回调处理异常: {e}")

# ==================== 全局变量 ====================
# 全局监控器实例
monitor = OptionMonitor()
