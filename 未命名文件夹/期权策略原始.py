# coding: gbk
import time
from datetime import datetime
import xml.etree.ElementTree as ET
import os
import pandas as pd
import random
import traceback

# 全局订单跟踪字典
order_tracker = {
    'orders': {},
    'positions': {}
}

# 1. 策略状态类
class StrategyState:
    def __init__(self, C):
        """策略状态初始化"""
        self.C = C  # 存储C
        self.subID = 0
        self.undl_code = '510300.SH'  # 标的代码
        self.monitors = {}
        self.account_manager = None
        self.last_check_time = 0

# 全局策略状态实例
strategy_state = None

# 工具函数
def show_data(data):
    """
    收集对象的关键数据
    @param data: 数据对象
    @return: dict 包含关键字段的字典
    """
    key_fields = {
        'm_strAccountID': '账户ID',
        'm_dAvailable': '可用资金',
        'm_dBalance': '账户余额',
        'm_dInstrumentValue': '持仓市值',
        'm_nOrderStatus': '订单状态',
        'm_strInstrumentID': '合约代码',
        'm_strInstrumentName': '合约名称',
        'm_dLimitPrice': '委托价格',
        'm_nVolumeTotal': '委托数量',
        'm_strOrderSysID': '系统委托号',
        'm_strErrorMsg': '错误信息'
    }
    
    result = {}
    for field, _ in key_fields.items():
        try:
            if hasattr(data, field):
                result[field] = getattr(data, field)
            else:
                result[field] = '<未知>'
        except Exception:
            result[field] = '<未知>'
    return result

# 2. 回调函数组
def account_callback(ContextInfo, accountInfo):
    """账户状态变化回调"""
    if not hasattr(ContextInfo, 'account_manager'):
        return
        
    try:
        # 让AccountManager处理更新
        ContextInfo.account_manager.update(ContextInfo)
    except Exception as e:
        print(f"账户回调处理异常: {str(e)}")

def order_callback(ContextInfo, orderInfo):
    print(f"订单回调触发: {show_data(orderInfo)}")
    try:
        if hasattr(ContextInfo, 'monitors'):
            contract_code = getattr(orderInfo, 'm_strInstrumentID', '')
            if contract_code in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract_code]
                monitor.update_order_status(orderInfo)
            else:
                print(f"未找到合约 {contract_code} 的监控器")
    except Exception as e:
        print(f"订单回调处理异常: {str(e)}")

def deal_callback(ContextInfo, dealInfo):
    """成交状态变化回调，同步成交价到 monitor 和 positions"""
    try:
        trade_id = getattr(dealInfo, 'm_strTradeID', '')
        order_id = getattr(dealInfo, 'm_strOrderSysID', '')
        contract = getattr(dealInfo, 'm_strInstrumentID', '')
        direction = '买入' if getattr(dealInfo, 'm_nOffsetFlag', 0) == 50 else '卖出'
        volume = getattr(dealInfo, 'm_nVolume', 0)
        price = getattr(dealInfo, 'm_dPrice', 0.0)
        trade_time = getattr(dealInfo, 'm_strTradeTime', '')
        
        print(f"\n>>> 新成交 <<< 合约: {contract}, 方向: {direction}, 数量: {volume}, 价格: {price:.4f}, 时间: {trade_time}")
        
        if hasattr(ContextInfo, 'account_manager'):
            ContextInfo.account_manager.update(ContextInfo)
            if hasattr(ContextInfo, 'monitors') and contract in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract]
                if direction == '买入':
                    monitor.entry_price = price                    
                    if contract in ContextInfo.account_manager.positions:
                        ContextInfo.account_manager.positions[contract]['OpenPrice'] = price
                        ContextInfo.account_manager.positions[contract]['Volume'] = volume
                        ContextInfo.account_manager.positions[contract]['PositionValue'] = volume * price * 10000
                    else:
                        ContextInfo.account_manager.positions[contract] = {
                            'Volume': volume,
                            'FrozenVolume': 0,
                            'AvailableVolume': volume,
                            'OpenPrice': price,
                            'PositionValue': volume * price * 10000,
                            'UnrealizedPL': 0.0
                        }
                    monitor.on_order_filled(price, volume, 'BUY')
                elif direction == '卖出' and contract in ContextInfo.account_manager.positions:
                    if ContextInfo.account_manager.positions[contract]['Volume'] == volume:
                        del ContextInfo.account_manager.positions[contract]
                        print(f"【{contract}】–>持仓全部平仓")
                    else:
                        ContextInfo.account_manager.positions[contract]['Volume'] -= volume
                        ContextInfo.account_manager.positions[contract]['PositionValue'] = ContextInfo.account_manager.positions[contract]['Volume'] * ContextInfo.account_manager.positions[contract]['OpenPrice'] * 10000
                        print(f"【{contract}】–>更新持仓量: {ContextInfo.account_manager.positions[contract]['Volume']}")
            
    except Exception as e:
        print(f"成交回调异常: {str(e)}")
        traceback.print_exc()

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """委托异常回调"""
    try:
        print(f"委托错误: {errMsg}")
    except Exception as e:
        print(f"订单错误回调异常: {str(e)}")

def check_pending_orders(account_id, contract_code):
    """检查是否有未完成的委托"""
    try:
        orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
        if orders and isinstance(orders, list):
            for order in orders:
                if (hasattr(order, 'm_strInstrumentID') and 
                    order.m_strInstrumentID == contract_code):
                    status = getattr(order, 'm_nOrderStatus', -1)
                    if status in [0, 1]:  # 未成交或部分成交
                        return True
        return False
    except Exception as e:
        print(f"检查未完成委托异常: {str(e)}")
        return True  # 发生异常时保守处理，返回True

def get_order_status(self, order_id):
    """
    获取订单状态
    @param order_id: 订单编号
    @return: dict 订单状态信息
    """
    try:
        if not order_id:
            return None
            
        # 直接从active_orders获取信息
        if order_id in self.active_orders:
            order_info = self.active_orders[order_id]
            return {
                'order_id': order_id,
                'status': order_info['status'],
                'filled_volume': order_info['traded'],
                'remaining_volume': order_info['volume'] - order_info['traded'],
                'price': order_info['price'],
                'direction': 'BUY' if order_info['direction'] == 50 else 'SELL',
                'contract': order_info['contract']
            }
                    
        print(f"未找到订单: {order_id}")
        return None
                
    except Exception as e:
        print(f"获取订单状态异常: {str(e)}")
        return None
        
def get_position_volume(self, contract_code):
    """获取合约持仓数量"""
    if contract_code in self.positions:
        return getattr(self.positions[contract_code], 'm_nVolume', 0)
    return 0
        
def get_available_volume(self, contract_code):
    """获取合约可用数量"""
    if contract_code in self.positions:
        return getattr(self.positions[contract_code], 'm_nCanUseVolume', 0)
    return 0

# 4. 合约监控类
class ContractMonitor:
    def __init__(self, C, contract_code, account_manager, max_position=30):
        """初始化合约监控器"""
        # 基础参数
        self.C = C
        self.contract_code = contract_code if contract_code.endswith('.SHO') else f"{contract_code}.SHO"
        self.account_manager = account_manager
        self.max_position = max_position
        
        # 新参数
        self.stop_loss = 0.03
        self.drawdown = 0.03
        self.trend_sensitivity = 3
        self.breakout_confirm = 1
        self.reversal_threshold = 0.05
        self.monitor_periods = 3
        self.commission_per_lot = 1.7
        self.trend_stop_enabled = 1
        
        # 价格相关
        self.current_price = 0.0  # 明确初始化为 0.0
        self.current_tick = None
        self.price_history = []
        self.initial_price = None
        self.daily_high = None
        self.daily_low = None
        self._price_initialized = False
        self.price_trend_points = []
        self.last_price_direction = None
        self.trend_direction = None
        
        # 持仓相关
        self.position = 0
        self.entry_price = 0.0
        self.original_entry_price = 0.0  # 明确初始化为 0.0
        self.entry_time = None
        self.in_position = False
        self.is_legacy_position = False
        self.highest_price = 0.0  # 明确初始化为 0.0
        self.lowest_price = 0.0   # 明确初始化为 0.0
        self.highest_since_entry = None
        self.lowest_since_entry = None
               
        # 订单相关
        self.active_orders = {}
        self.filled_orders = {}
        self.last_order_id = None
        self.last_order_check_time = time.time()
        self.order_timeout = 10
        
        # 交易状态
        self.pending_close = False
        self.close_reason = None
        self.trade_history = []
        
        # 趋势和动量指标（保留但不直接使用）
        self.trend = 0.0
        self.momentum = []
        self.volatility = 0.0
        
        # 添加已订阅合约集合（类变量）
        if not hasattr(ContractMonitor, '_subscribed_contracts'):
            ContractMonitor._subscribed_contracts = set()

    def calculate_momentum(self, prices):
        """计算动量指标（保留原始方法但不直接使用）"""
        if len(prices) < 2:
            return []
        momentum = [(prices[i] - prices[i-1]) / prices[i-1] 
                   for i in range(1, len(prices))]
        return momentum

    def calculate_volatility(self, prices):
        """计算波动率（保留原始方法但不直接使用）"""
        if len(prices) < 2:
            return 0.0
        price_changes = [(prices[i] - prices[i-1]) / prices[i-1] 
                        for i in range(1, len(prices))]
        if not price_changes:
            return 0.0
        mean = sum(price_changes) / len(price_changes)
        variance = sum((x - mean) ** 2 for x in price_changes) / len(price_changes)
        return (variance ** 0.5)

    def update_indicators(self):
        """更新指标（保留原始方法但不直接使用）"""
        if len(self.price_history) < 2:
            return
        prices = [p['price'] for p in self.price_history]
        self.trend = self.calculate_trend(prices)
        self.momentum = self.calculate_momentum(prices)
        self.volatility = self.calculate_volatility(prices)

    # 修改后的趋势计算逻辑，仅在连续上涨或下跌时返回明确方向
    def calculate_trend(self):
        """计算趋势：tick连续趋势与震荡模式并行，周期检测不影响连续tick监测"""
        if not hasattr(self, 'oscillation_ended'):
            self.oscillation_ended = False
        if self.oscillation_ended:
            return None, "震荡监测已结束"

        if len(self.price_history) < self.trend_sensitivity:
            return None, f"价格历史不足 ({len(self.price_history)}/{self.trend_sensitivity})"

        prices = [p['price'] for p in self.price_history]
        times = [p['time'] for p in self.price_history]

        def get_valid_changes(price_list):
            changes = []
            for i in range(1, len(price_list)):
                if price_list[i] > price_list[i-1]:
                    changes.append('上涨')
                elif price_list[i] < price_list[i-1]:
                    changes.append('下跌')
            return changes

        # 生成有效tick序列（连续相同价格只计为1次有效tick）
        effective_prices = []
        for price in prices:
            if not effective_prices or price != effective_prices[-1]:
                effective_prices.append(price)

        # 1. 连续趋势监测（基于有效tick）
        required_ticks = self.trend_sensitivity + self.breakout_confirm  # 4
        if len(effective_prices) <= required_ticks:  # 确保有足够有效tick来生成4次变化
            return None, f"等待更多有效tick变化 (当前 {len(effective_prices)-1}/{required_ticks})"

        recent_prices = effective_prices[-required_ticks-1:]
        changes = get_valid_changes(recent_prices)
        
        # 检查方向变化并重置
        reset_triggered = False
        for i in range(1, len(changes)):
            if changes[i] != changes[i-1]:
                reset_triggered = True
                break

        if len(changes) >= required_ticks and not reset_triggered:
            all_positive = all(c == '上涨' for c in changes[-required_ticks:])
            all_negative = all(c == '下跌' for c in changes[-required_ticks:])
            if all_positive:
                print(f"【{self.contract_code}】–>趋势触发: 连续上涨 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in recent_prices[-required_ticks:]]}")
                return "up", "continuous"
            if all_negative:
                print(f"【{self.contract_code}】–>趋势触发: 连续下跌 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in recent_prices[-required_ticks:]]}")
                return "down", "continuous"

        # 2. 震荡模式监测（仅在方向反转时触发）
        if reset_triggered:
            if not hasattr(self, 'period_trends'):  # 持久化 period_trends
                self.period_trends = []
            period_trends = self.period_trends
            current_idx = 0
            
            while current_idx < len(prices) and len(period_trends) < self.monitor_periods:
                period_prices = []
                period_changes = []
                start_price = None
                
                # 累积tick直到产生恰好4个变化
                while current_idx < len(prices) and len(period_changes) < required_ticks:
                    if not period_prices:
                        start_price = prices[current_idx]
                    period_prices.append(prices[current_idx])
                    if len(period_prices) > 1:
                        period_changes = get_valid_changes(period_prices)
                    
                    # 实时检查连续tick趋势（每次添加tick后）
                    for i in range(max(0, len(prices) - required_ticks)):
                        check_prices = prices[i:i + required_ticks + 1]
                        check_changes = get_valid_changes(check_prices)
                        if len(check_changes) == required_ticks:
                            all_positive = all(c == '上涨' for c in check_changes)
                            all_negative = all(c == '下跌' for c in check_changes)
                            if all_positive:
                                print(f"【{self.contract_code}】–>趋势触发: 连续上涨 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in check_prices]}")
                                return "up", "continuous"
                            if all_negative:
                                print(f"【{self.contract_code}】–>趋势触发: 连续下跌 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in check_prices]}")
                                return "down", "continuous"
                    current_idx += 1
                
                # 判断周期整体方向（需4次变化）
                if len(period_changes) == required_ticks:
                    end_price = period_prices[-1]
                    if end_price > start_price:
                        period_direction = "上涨"
                    elif end_price < start_price:
                        period_direction = "下跌"
                    else:
                        period_direction = "震荡"
                    
                    print(f"【{self.contract_code}】–>震荡周期 {len(period_trends)+1}: 方向 {period_direction}")
                    
                    # 检查震荡或方向不一致
                    if period_direction == "震荡":
                        print(f"【{self.contract_code}】–>结束震荡监测：周期 {len(period_trends)+1} 为震荡")
                        period_trends.clear()
                        self.oscillation_ended = True
                        return None, "结束震荡监测"
                    
                    if period_trends and period_trends[-1] != period_direction:
                        print(f"【{self.contract_code}】–>结束震荡监测：周期 {len(period_trends)+1} 方向改变 ({period_trends[-1]} -> {period_direction})")
                        period_trends.clear()
                        self.oscillation_ended = True
                        break
                    
                    period_trends.append(period_direction.lower())
                else:
                    break  # 到末尾不足4次变化，退出
                
                # 判断震荡趋势
                if len(period_trends) == self.monitor_periods and all(t == period_trends[0] for t in period_trends):
                    print(f"【{self.contract_code}】–>趋势触发: 震荡{period_trends[0]} {self.monitor_periods}周期")
                    return period_trends[0], "oscillation"

        # 3. 检查连续tick趋势（在周期检测中止后）
        if reset_triggered and period_trends:  # 确保至少有一个周期
            for i in range(max(0, len(prices) - required_ticks)):
                check_prices = prices[i:i + required_ticks + 1]
                check_changes = get_valid_changes(check_prices)
                if len(check_changes) == required_ticks:
                    all_positive = all(c == '上涨' for c in check_changes)
                    all_negative = all(c == '下跌' for c in check_changes)
                    if all_positive:
                        print(f"【{self.contract_code}】–>趋势触发: 连续上涨 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in check_prices]}")
                        return "up", "continuous"
                    if all_negative:
                        print(f"【{self.contract_code}】–>趋势触发: 连续下跌 {required_ticks} ticks, 序列: {[f'{p:.4f}' for p in check_prices]}")
                        return "down", "continuous"
        
        return None, "等待更多有效tick变化"

    def calculate_oscillation_trend(self):
        required_ticks = self.trend_sensitivity + self.breakout_confirm
        cycle_length = required_ticks + 1
        if len(self.price_history) < cycle_length * self.monitor_periods:
            return None, f"价格历史不足 ({len(self.price_history)}/{cycle_length * self.monitor_periods})"
        prices = [p['price'] for p in self.price_history]
        cycles = []
        for i in range(0, len(prices) - cycle_length + 1, cycle_length):
            cycle = prices[i:i + cycle_length]
            changes = [cycle[j] > cycle[j-1] and '上涨' or '下跌' for j in range(1, len(cycle)) if cycle[j] != cycle[j-1]]
            if len(changes) >= required_ticks:
                if all(c == '上涨' for c in changes):
                    cycles.append("UP")
                elif all(c == '下跌' for c in changes):
                    cycles.append("DOWN")
                else:
                    cycles.append("MIXED")
        if len(cycles) >= self.monitor_periods and all(c == "UP" for c in cycles[-self.monitor_periods:]):
            print(f"【{self.contract_code}】–>检测到震荡周期上涨趋势 ({self.monitor_periods} 个周期)")
            return "up", "oscillation"
        if len(cycles) >= self.monitor_periods and all(c == "DOWN" for c in cycles[-self.monitor_periods:]):
            print(f"【{self.contract_code}】–>检测到震荡周期下跌趋势 ({self.monitor_periods} 个周期)")
            return "down", "oscillation"
        return None, "震荡不一致"

    def update_order_status(self, order_info):
        try:
            current_time = time.time()
            
            if isinstance(order_info, str):
                order_id = order_info
                order_info = get_value_by_order_id(
                    order_id,
                    self.account_manager.account_id,
                    'STOCK_OPTION',
                    'ORDER'
                )
                if not order_info:
                    print(f"未找到订单 {order_id} 的信息")
                    if order_id in self.active_orders:
                        del self.active_orders[order_id]
                    return
            
            order_id = getattr(order_info, 'm_strOrderSysID', '')
            contract = getattr(order_info, 'm_strInstrumentID', '')
            order_status = getattr(order_info, 'm_nOrderStatus', -1)
            direction = getattr(order_info, 'm_nOffsetFlag', 0)
            volume = getattr(order_info, 'm_nVolumeTotalOriginal', 0)
            traded = getattr(order_info, 'm_nVolumeTraded', 0)
            price = float(getattr(order_info, 'm_dLimitPrice', 0.0))
            
            if order_id not in self.active_orders:
                return
                
            order_info_dict = self.active_orders[order_id]
            is_close_order = order_info_dict.get('is_close', False)
            timeout = 30 if is_close_order else 30  # 统一调整为 30 秒超时
            
            print(f"【{self.contract_code}】–>订单状态更新–>ID: {order_id}, 方向: {'买入开仓' if direction == 50 else '卖出平仓'}")
            print(f"数量: {traded}/{volume}, 价格: {price}, 状态: {order_status}")
            
            if order_status in [0, 1]:  # 未成交或部分成交
                print(f"【{self.contract_code}】–>订单活跃–>ID: {order_id}, 状态: {order_status}")
            elif order_status == 3:  # 假设 3 为已报（根据实际情况调整）
                print(f"【{self.contract_code}】–>订单已报–>ID: {order_id}")
            elif order_status == 5:  # 已撤单
                print(f"【{self.contract_code}】–>订单已撤–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print(f"【{self.contract_code}】–>平仓订单被撤销–>重新提交")
                    time.sleep(1)
                    self.close_position(self.position)
            elif order_status == 4:  # 已拒绝
                print(f"【{self.contract_code}】–>订单被拒绝–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print("【{self.contract_code}】–>平仓订单被拒绝–>重新提交")
                    time.sleep(1)
                    self.close_position(self.position)
            else:
                print(f"【{self.contract_code}】–>未知订单状态–>ID: {order_id}, 状态: {order_status}")
            
            order_time = order_info_dict['time']
            if current_time - order_time > timeout:
                print(f"【{self.contract_code}】–>订单超时–>ID: {order_id}, 平仓单: {is_close_order}")
                if self.cancel_order(self.C, order_id):
                    print(f"【{self.contract_code}】–>订单超时撤单成功–>ID: {order_id}")
                    del self.active_orders[order_id]
                    if is_close_order:
                        print(f"【{self.contract_code}】–>平仓订单超时–>重新提交")
                        time.sleep(1)
                        self.close_position(self.position)
                else:
                    print(f"【{self.contract_code}】–>订单超时撤单失败–>ID: {order_id}")
            else:
                remaining_time = timeout - (current_time - order_time)
                print(f"【{self.contract_code}】–>订单等待中–>剩余时间: {remaining_time:.1f}秒")
                if traded > 0:
                    print(f"【{self.contract_code}】–>订单部分成交–>{traded}/{volume}")
        except Exception as e:
            print(f"【{self.contract_code}】–>更新订单状态异常: {str(e)}")
            traceback.print_exc()

    def cancel_order(self, C, order_sys_id=None, contract_code=None):
        """撤销委托订单"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            active_states = [0, 1, 3, 50, 51]
            orders_cancelled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                current_order_id = getattr(order, 'm_strOrderSysID', '')
                current_contract = getattr(order, 'm_strInstrumentID', '')
                
                if order_sys_id and current_order_id != order_sys_id:
                    continue
                    
                if contract_code and current_contract != contract_code:
                    continue
                    
                if status in active_states:
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    
                    if volume_total > volume_traded:
                        result = cancel(
                            current_order_id,
                            self.account_manager.account_id,
                            'STOCK_OPTION',
                            C
                        )
                        
                        if result:
                            print(f"【{self.contract_code}】–>撤单成功–>ID: {current_order_id}")
                            orders_cancelled = True
                        else:
                            print(f"【{self.contract_code}】–>撤单失败–>ID: {current_order_id}")
                            
            return orders_cancelled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤单异常: {str(e)}")
            traceback.print_exc()
            return False

    def load_parameters(self):
        """从XML文件加载策略参数"""
        try:
            xml_path = r"C:\国金QMT交易端模拟\python\formulaLayout\期权策略.xml"
            print(f"【{self.contract_code}】–>加载策略参第–>路径: {xml_path}")
            
            if not os.path.exists(xml_path):
                raise FileNotFoundError(f"未找到配置文件: {xml_path}")
            
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            updated_params = []
            for item in root.findall('.//item'):
                bind = item.get('bind')
                value = item.get('value')
                
                if not bind or not value:
                    continue
                    
                try:
                    if bind == 'max_position':
                        self.max_position = int(value)
                        updated_params.append(('最大持仓', self.max_position))
                    elif bind == 'stop_loss':
                        self.stop_loss = float(value)
                        updated_params.append(('止损比例', self.stop_loss))
                    elif bind == 'drawdown':
                        self.drawdown = float(value)
                        updated_params.append(('回撤比例', self.drawdown))
                    elif bind == 'trend_sensitivity':
                        self.trend_sensitivity = int(value)
                        updated_params.append(('趋势敏感度', self.trend_sensitivity))
                    elif bind == 'breakout_confirm':
                        self.breakout_confirm = int(value)
                        updated_params.append(('突破确认', self.breakout_confirm))
                    elif bind == 'reversal_threshold':
                        self.reversal_threshold = float(value)
                        updated_params.append(('反转阈值', self.reversal_threshold))
                    elif bind == 'monitor_periods':
                        self.monitor_periods = int(value)
                        updated_params.append(('监测周期数', self.monitor_periods))
                    elif bind == 'commission_per_lot':
                        self.commission_per_lot = float(value)
                        updated_params.append(('每张手续费', self.commission_per_lot))
                    elif bind == 'trend_stop_enabled':
                        self.trend_stop_enabled = int(value)
                        updated_params.append(('趋势止损开关', self.trend_stop_enabled))
                except ValueError as e:
                    print(f"【{self.contract_code}】–>参数[{bind}]格式错误: {str(e)}")
                    continue
            
            if updated_params:
                print(f"【{self.contract_code}】–>成功更新参数:")
                for param_name, param_value in updated_params:
                    print(f"{param_name}: {param_value}")
            else:
                print(f"【{self.contract_code}】–>警告: 未能更新任何参数")
        except Exception as e:
            print(f"【{self.contract_code}】–>加载参数异常: {str(e)}")
            print(f"【{self.contract_code}】–>使用默认/parameters")

    def init_position(self, contract_code, volume, open_price, open_time=None):
        """初始化历史持仓信息"""
        try:
            if self.is_legacy_position and self.contract_code == contract_code:
                print(f"【{self.contract_code}】–>持仓已初始化，跳过")
                return
                
            print(f"【{self.contract_code}】–>初始化历史持仓")
            print(f"合约代码: {contract_code}")
            print(f"持仓量: {volume}")
            print(f"开仓价 (平均价): {open_price:.4f}")
            print(f"开仓时间: {open_time if open_time else time.strftime('%Y%m%d%H%M%S')}")
            
            self.contract_code = contract_code
            self.is_legacy_position = True
            
            if not self.update_price():
                print(f"【{self.contract_code}】–>无法获取最新价，使用平均开仓价作为替代")
                self.current_price = open_price
            
            self.entry_price = open_price  # 记录平均开仓价
            self.original_entry_price = self.current_price  # 使用最新价
            print(f"【{self.contract_code}】–>已有持仓，设置原始开仓价为最新价: {self.original_entry_price:.4f}")
            
            self.position = volume
            self.in_position = volume > 0
            self.highest_price = self.current_price
            self.lowest_price = self.current_price
            
        except Exception as e:
            print(f"【{self.contract_code}】–>初始化持仓信息异常: {str(e)}")
            traceback.print_exc()

    def update_price(self):
        """更新价格数据，记录所有tick，按时间排序，价格精度限制为小数点后4位"""
        try:
            current_time = time.time()
            if hasattr(self, '_last_update_time') and current_time - self._last_update_time < 1:
                return True
            self._last_update_time = current_time
            
            retries = 3
            for attempt in range(retries):
                tick_data = self.C.get_full_tick([self.contract_code])
                if not isinstance(tick_data, dict) or self.contract_code not in tick_data:
                    print(f"【{self.contract_code}】–>获取行情数据失败–>重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                    continue
                
                data = tick_data[self.contract_code]
                if not isinstance(data, dict):
                    print(f"【{self.contract_code}】–>数据格式错误: {type(data)}")
                    return False
                
                self.current_tick = {
                    'time': data.get('timetag', ''),
                    'lastPrice': data.get('lastPrice', 0.0),
                    'open': data.get('open', 0.0),
                    'high': data.get('high', 0.0),
                    'low': data.get('low', 0.0),
                    'lastClose': data.get('lastClose', 0.0),
                    'amount': data.get('amount', 0.0),
                    'volume': data.get('volume', 0),
                    'openInterest': data.get('openInt', 0),
                    'settlementPrice': data.get('settlementPrice', 0.0),
                    'lastSettlementPrice': data.get('lastSettlementPrice', 0.0),
                    'askPrice': data.get('askPrice', []),
                    'askVol': data.get('askVol', []),
                    'bidPrice': data.get('bidPrice', []),
                    'bidVol': data.get('bidVol', [])
                }
                
                price = self.current_tick['lastPrice']
                if price <= 0:
                    bid_price = self.current_tick['bidPrice'][0] if self.current_tick['bidPrice'] else 0
                    ask_price = self.current_tick['askPrice'][0] if self.current_tick['askPrice'] else 0
                    if bid_price > 0 and ask_price > 0:
                        price = (bid_price + ask_price) / 2
                    else:
                        price = self.current_tick['lastClose']
                
                if price <= 0:
                    print(f"【{self.contract_code}】–>尝试 {attempt+1}/{retries} 获取有效价格失败")
                    continue
                
                price = round(price, 4)
                self.current_price = price
                current_tick_time = self.current_tick.get('time', '')
                
                # 初始化有效tick计数器
                if not hasattr(self, 'effective_ticks'):
                    self.effective_ticks = []
                
                # 记录所有tick，但仅当价格变化时增加有效tick
                self.price_history.append({'price': price, 'time': current_tick_time})
                self.price_history.sort(key=lambda x: x['time'])
                if len(self.price_history) > 20:
                    self.price_history.pop(0)
                
                # 更新有效tick列表（去重相同价格）
                if not self.effective_ticks or self.effective_ticks[-1]['price'] != price:
                    self.effective_ticks.append({'price': price, 'time': current_tick_time})
                    print(f"【{self.contract_code}】–>价格变动: {price:.4f} at {current_tick_time}")
                
                if not self._price_initialized:
                    self.initial_price = price
                    self.daily_high = price
                    self.daily_low = price
                    if self.in_position:
                        self.highest_price = price
                    self._price_initialized = True
                else:
                    self.daily_high = max(self.daily_high, price) if self.daily_high is not None else price
                    self.daily_low = min(self.daily_low, price) if self.daily_low is not None else price
                
                return True
            
            print(f"【{self.contract_code}】–>无法获取有效价格–>已重试{retries}次")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>更新价格异常: {str(e)}")
            return False

    # 修改：仅在连续下跌趋势中触发趋势止损
    def check_exit_conditions(self, check_time=True):
        """检查是否满足出场条件"""
        if not self.in_position:
            return False
                        
        if check_time and not is_trade_time():
            return False
                        
        if not self.update_price():
            print(f"【{self.contract_code}】–>无法更新价格，跳过平仓检查")
            return False
        
        current_price = self.current_price if self.current_price is not None else 0.0
        original_entry_price = self.entry_price if self.entry_price is not None else 0.0
        highest_price = self.highest_price if self.highest_price is not None else 0.0
        
        profit_ratio = (current_price - original_entry_price) / original_entry_price if original_entry_price > 0 else 0
        current_drawdown = (highest_price - current_price) / highest_price if highest_price > 0 else 0
        
        if self.trend_stop_enabled:
            trend, trend_type = self.calculate_trend()
            if trend == "down":
                self.close_reason = "趋势止损"
                if profit_ratio <= -self.stop_loss:
                    self.close_reason += f" + 止损 {profit_ratio*100:.2f}%"
                    print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价: {current_price:.4f}")
                    return True
                if current_drawdown >= self.drawdown:
                    self.close_reason += f" + 回撤 {current_drawdown*100:.2f}%"
                    print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价: {current_price:.4f}")
                    return True
                return False
        else:
            if profit_ratio <= -self.stop_loss:
                self.close_reason = f"止损 {profit_ratio*100:.2f}%"
                print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价: {current_price:.4f}")
                return True
            if current_drawdown >= self.drawdown:
                self.close_reason = f"回撤 {current_drawdown*100:.2f}%"
                print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价: {current_price:.4f}")
                return True
        
        if highest_price == 0.0 or current_price > highest_price:
            self.highest_price = current_price
        
        return False

    def enter_position(self, C):
        """执行入场操作（买入开仓），仅使用 50"""
        try:
            if not self.current_tick and not self.update_price():
                print(f"【{self.contract_code}】–>无法获取当前行情数据")
                return False

            if self.contract_code not in ContractMonitor._subscribed_contracts:
                print(f"【{self.contract_code}】–>订阅合约")
                self.C.subscribe_quote(self.contract_code, "1", "0")
                ContractMonitor._subscribed_contracts.add(self.contract_code)

            bid_price = self.current_tick.get('bidPrice', [0])[0]
            ask_price = self.current_tick.get('askPrice', [0])[0]
            ask_vol = self.current_tick.get('askVol', [0])[0]
            if not bid_price or not ask_price or not ask_vol:
                print(f"【{self.contract_code}】–>无法获取有效买卖价格或卖一量")
                return False

            available_fund = self.account_manager.account_info.get('m_dAvailable', 0)
            max_by_fund = int(available_fund / (ask_price * 10000))
            order_volume = min(ask_vol, self.max_position - self.position, max_by_fund, 6)
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用入场数量")
                return False

            order_price = round(ask_price, 4)
            print(f"【{self.contract_code}】–>触发入场（买入开仓）–>价格: {order_price:.4f}, 数量: {order_volume}")

            timestamp = time.strftime('%Y%m%d_%H%M%S')
            random_num = random.randint(1000, 9999)
            order_remark = f"{self.contract_code}_BUY_{timestamp}_{random_num}"

            order_id = passorder(
                50, 1101, self.account_manager.account_id, self.contract_code,  # 买入开仓
                1, order_price, order_volume, "期权策略", 2, order_remark, C
            )

            temp_order_id = order_id if order_id else order_remark
            if order_id is not None:
                print(f"【{self.contract_code}】–>入场订单提交成功–>ID: {temp_order_id}")
                self.last_order_id = temp_order_id
                self.last_order_check_time = time.time()
                self.active_orders[temp_order_id] = {
                    'time': time.time(),
                    'is_close': False,
                    'price': order_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 50,
                    'contract': self.contract_code
                }
                return True
            print(f"【{self.contract_code}】–>入场订单提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行入场操作异常: {str(e)}")
            traceback.print_exc()
            return False

    def exit_position(self, reason):
        """执行出场交易（卖出平仓），仅使用 51"""
        if not self.check_exit_conditions():
            return False
        
        print(f"【{self.contract_code}】–>触发平仓: {reason}, 当前价: {self.current_price:.4f}")
        
        try:
            # 检查 self.C 是否有效
            if not hasattr(self, 'C') or self.C is None:
                raise AttributeError(f"【{self.contract_code}】–>上下文对象 'self.C' 未定义或为空")
            
            # 撤销现有订单
            self.cancel_order(self.C)
            
            # 更新账户信息
            self.account_manager.update(self.C)
            contract_key = self.contract_code.replace('.SHO', '')
            position_info = self.account_manager.positions.get(contract_key)
            
            if not position_info:
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract_key:
                            position_info = {
                                'AvailableVolume': getattr(pos, 'm_nVolume', 0) - getattr(pos, 'm_nFrozenVolume', 0)
                            }
                            break
                if not position_info:
                    print(f"【{self.contract_code}】–>无持仓信息")
                    return False
                    
            available_volume = position_info.get('AvailableVolume', 0)
            bid_vol = self.current_tick.get('bidVol', [0])[0] if self.current_tick else 0
            if available_volume <= 0 or not bid_vol:
                print(f"【{self.contract_code}】–>无可用持仓或买一量 (可用: {available_volume}, 买一量: {bid_vol})")
                return False
                
            order_volume = min(available_volume, bid_vol)
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用平仓数量")
                return False
            
            bid_price = self.current_tick.get('bidPrice', [0])[0] if self.current_tick else 0
            order_price = round(bid_price, 4)
            
            timestamp = time.strftime('%Y%m%d_%H%M%S')
            random_num = random.randint(1000, 9999)
            order_remark = f"{self.contract_code}_SELL_{timestamp}_{random_num}"

            order_id = passorder(
                51, 1101, self.account_manager.account_id, self.contract_code,  # 卖出平仓
                1, order_price, order_volume, "期权策略", 2, order_remark, self.C  # 使用 self.C
            )

            temp_order_id = order_id if order_id else order_remark
            if order_id is not None:
                print(f"【{self.contract_code}】–>平仓成功: 数量 {order_volume}, 价格 {order_price:.4f}, 订单ID: {temp_order_id}")
                self.active_orders[temp_order_id] = {
                    'time': time.time(),
                    'is_close': True,
                    'price': order_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 51,
                    'contract': self.contract_code
                }
                self.pending_close = False
                self.close_reason = None
                return True
            print(f"【{self.contract_code}】–>平仓失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行出场异常: {str(e)}")
            traceback.print_exc()
            return False

    def cancel_frozen_orders(self, C, is_open=False):
        """撤销冻结订单，支持入场和平仓"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            direction_flag = 50 if is_open else 51  # 50: 开仓, 51: 平仓
            active_states = [0, 1, 2, 3, 50, 51]  # 包括“已报待撤”(假设 2)
            canceled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus') or not hasattr(order, 'm_nOffsetFlag'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                direction = int(getattr(order, 'm_nOffsetFlag', 0))
                order_id = getattr(order, 'm_strOrderSysID', '')
                contract = getattr(order, 'm_strInstrumentID', '')
                
                if (contract == self.contract_code.replace('.SHO', '') and 
                    direction == direction_flag and status in active_states):
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    if volume_total > volume_traded:
                        result = cancel(order_id, self.account_manager.account_id, 'STOCK_OPTION', C)
                        if result:
                            print(f"【{self.contract_code}】–>撤销冻结订单成功–>ID: {order_id}, 状态: {status}")
                            canceled = True
                        else:
                            print(f"【{self.contract_code}】–>撤销冻结订单失败–>ID: {order_id}, 状态: {status}")
            
            return canceled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤销冻结订单异常: {str(e)}")
            traceback.print_exc()
            return False

    # 修改：仅在连续上涨趋势中触发入场
    def check_entry_conditions(self):
        """检查入场条件，仅依赖 calculate_trend 判断"""
        try:
            if self.active_orders:
                print(f"【{self.contract_code}】–>存在未完成订单，暂不入场")
                return False

            if not self.account_manager or not self.account_manager.account_info:
                print(f"【{self.contract_code}】–>等待账户信息初始化")
                return False
                
            if len(self.price_history) < self.trend_sensitivity + self.breakout_confirm:
                return False  # 静默等待
                
            if not self.current_price:
                return False

            if self.position >= self.max_position:
                print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position}")
                return False

            trend, trend_type = self.calculate_trend()
            if trend == "up":  # 连续或震荡上涨趋势
                print(f"【{self.contract_code}】–>触发入场: 趋势 {trend}, 类型 {trend_type}")
                return self.enter_position(self.C)
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>检查入场条件异常: {str(e)}")
            return False

    def check_trend_strength(self):
        """检查趋势强度（保留原始方法）"""
        try:
            if len(self.price_history) < 2:
                return 0.0
                
            prices = [p['price'] for p in self.price_history]
            price_changes = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    change = (prices[i] - prices[i-1]) / prices[i-1]
                    price_changes.append(change)
            
            if not price_changes:
                return 0.0
                
            avg_change = sum(price_changes) / len(price_changes)
            return abs(avg_change)
        except Exception as e:
            print(f"【{self.contract_code}】–>计算趋势强度异常: {str(e)}")
            return 0.0

    def on_tick(self):
        try:
            # 添加初始化检查以清空价格历史数据
            if not hasattr(self, '_initialized'):
                self.price_history = []  # 清空价格历史
                self.effective_ticks = []  # 清空有效tick列表
                self._initialized = True
                print(f"【{self.contract_code}】–>初始化价格历史数据")

            current_trade_status = is_trade_time()
            if not hasattr(self, '_last_trade_status'):
                self._last_trade_status = current_trade_status
            
            if current_trade_status != self._last_trade_status:
                if current_trade_status:
                    print(f"【{self.contract_code}】–>进入交易时间")
                else:
                    print(f"【{self.contract_code}】–>非交易时间，暂停")
                self._last_trade_status = current_trade_status

            if not current_trade_status:
                return

            current_time = time.time()
            self._last_tick_time = current_time
            
            if not self.update_price():
                return
                            
            if hasattr(self, 'account_manager'):
                self.account_manager.update(self.C)
            
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            pending_orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if positions and isinstance(positions, list):
                self.position = 0
                self.in_position = False
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        self.in_position = self.position > 0
                        if self.in_position and not hasattr(self, 'total_cost'):
                            contract_key = self.contract_code.replace('.SHO', '')
                            if hasattr(self.account_manager, 'positions') and contract_key in self.account_manager.positions:
                                open_price = self.account_manager.positions[contract_key]['OpenPrice']
                                multiplier = 10000
                                self.total_cost = open_price * self.position * multiplier
                                self.total_volume = self.position
                                self.entry_price = open_price
                                print(f"【{self.contract_code}】–>初始化持仓: 数量={self.total_volume}, 开仓价={self.entry_price:.4f}")
                            else:
                                multiplier = 10000
                                self.total_cost = self.current_price * self.position * multiplier
                                self.total_volume = self.position
                                self.entry_price = self.current_price
                                print(f"【{self.contract_code}】–>初始化持仓（无历史数据）: 数量={self.total_volume}, 开仓价={self.entry_price:.4f}")
                            break

            pending_volume = 0
            if pending_orders and isinstance(pending_orders, list):
                for order in pending_orders:
                    if (hasattr(order, 'm_strInstrumentID') and 
                        order.m_strInstrumentID == self.contract_code.replace('.SHO', '') and 
                        order.m_nOrderStatus in [50, 51]):
                        pending_volume += getattr(order, 'm_nVolumeTotal', 0) - getattr(order, 'm_nVolumeTraded', 0)

            if self.in_position and self.check_exit_conditions():
                self.exit_position(self.close_reason)
                self.in_position = False
                self.total_cost = 0
                self.total_volume = 0
                self.entry_price = None
                print(f"【{self.contract_code}】–>平仓完成")
            elif pending_volume == 0 and self.position + pending_volume < self.max_position:
                self.check_entry_conditions()
            else:
                if pending_volume > 0 and not hasattr(self, '_last_pending_logged'):
                    print(f"【{self.contract_code}】–>存在未完成订单，暂不入场")
                    self._last_pending_logged = True
                elif pending_volume == 0 and hasattr(self, '_last_pending_logged'):
                    delattr(self, '_last_pending_logged')  # 重置标记
        except Exception as e:
            print(f"【{self.contract_code}】–>行情处理异常: {str(e)}")
            traceback.print_exc()

    def update_entry_price(self, price, volume):
        """更新平均成本"""
        if not hasattr(self, 'total_cost'):
            self.total_cost = 0
            self.total_volume = 0
        multiplier = 10000
        self.total_cost += price * volume * multiplier
        self.total_volume += volume
        self.entry_price = self.total_cost / (self.total_volume * multiplier) if self.total_volume > 0 else 0
        print(f"【{self.contract_code}】–>更新成本: 数量={self.total_volume}, 开仓价={self.entry_price:.4f}")

    def on_order_filled(self, price, volume, direction):
        """订单成交回调"""
        if direction == 'BUY' and self.in_position:
            self.update_entry_price(price, volume)

# 5. 主要策略函数
def call_back(data):
    """主策略回调函数"""
    try:
        if isinstance(data, dict) and 'EventCode' in data and data['EventCode'] == 0:
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
                
            C = call_back.context
            
            print(f"订阅标的行情: {underlying}")
            C.subscribe_quote(underlying)
            
            call_code, put_code = select_options(C, underlying)
            
            if call_code and put_code:
                print(f"订阅期权合约: [{call_code}, {put_code}]")
                C.subscribe_quote(call_code)
                C.subscribe_quote(put_code)
                
                contract_monitor = ContractMonitor(C, call_code, account_manager)
                contract_monitor.init_contract(call_code)
                
                contract_monitor.call_option = call_code
                contract_monitor.put_option = put_code
                
            return
            
        if isinstance(data, dict):
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
                
            C = call_back.context
            
            for code, tick_data in data.items():
                if code in C.monitors:
                    monitor = C.monitors[code]
                    monitor.on_tick()

    except Exception as e:
        print(f"策略异常: {str(e)}")
        traceback.print_exc()

def init(C):
    """初始化策略"""
    global strategy_state
    
    strategy_state = StrategyState(C)
    
    strategy_state.subID = 0
    strategy_state.undl_code = '510300.SH'
    strategy_state.monitors = {}
    C.monitors = strategy_state.monitors
    
    print(f"订阅标的行情: {strategy_state.undl_code}")
    C.subscribe_quote(strategy_state.undl_code, "1", "0")
    
    account_id = '*********'
    strategy_state.account_manager = AccountManager(account_id)
    
    C.set_account(account_id)
    
    call_back.context = C

def after_init(C):
    """初始化后执行的操作"""
    try:
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
            positions = get_trade_detail_data(strategy_state.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            position_contracts = set()
            
            if positions and isinstance(positions, list):
                for pos in positions:
                    if hasattr(pos, 'm_strInstrumentID'):
                        contract_code = pos.m_strInstrumentID
                        pos_volume = getattr(pos, 'm_nVolume', 0)
                        if pos_volume > 0:
                            position_contracts.add(contract_code)
                            
                            if contract_code not in strategy_state.monitors:
                                monitor = ContractMonitor(C, contract_code, strategy_state.account_manager)
                                strategy_state.monitors[contract_code] = monitor
                                
                                entry_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                                monitor.position = pos_volume
                                monitor.entry_price = entry_price
                                monitor.in_position = True
                                monitor.is_legacy_position = True
                                monitor.load_parameters()  # 加载 XML 参数
                                print(f"【{contract_code}】–>初始化已有持仓–>持仓量: {pos_volume}, 开仓价: {entry_price}")
        
        tick_data = C.get_full_tick([strategy_state.undl_code])
        if not tick_data or strategy_state.undl_code not in tick_data:
            print("无法获取市场数据")
            return
            
        data = tick_data[strategy_state.undl_code]
        if not isinstance(data, dict):
            print("市场数据格式错误")
            return
            
        timetag = data.get('timetag', '')
        current_date = timetag.split()[0].replace('-', '') if timetag else ''
        
        target_price = data.get('lastPrice', 0)
        if target_price <= 0:
            target_price = data.get('lastClose', 0)
            if target_price > 0:
                print("使用昨收盘价")
            else:
                print("无法获取有效价格")
                return
                
        if not current_date:
            print("无法获取有效日期")
            return
            
        print(f"\n当前日期: {current_date}")
        print(f"标的{strategy_state.undl_code}当前价格: {target_price}")
        
        all_options = C.get_option_list(strategy_state.undl_code, current_date, "", True)
        if not all_options:
            print("没有可用期权")
            return
            
        expiry_groups = {}
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if not detail:
                continue
                
            expiry = detail['ExpireDate']
            if expiry not in expiry_groups:
                expiry_groups[expiry] = []
            expiry_groups[expiry].append((opt, detail))
        
        if not expiry_groups:
            print("无法获取期权详情")
            return
            
        nearest_expiry = min(expiry_groups.keys())
        print(f"\n选择到期日: {nearest_expiry}")
        
        selected_options = []
        min_call_diff = float('inf')
        min_put_diff = float('inf')
        closest_call = None
        closest_put = None
        
        for opt, detail in expiry_groups[nearest_expiry]:
            strike = float(detail['OptExercisePrice'])
            
            if detail['optType'] == 'CALL':
                if strike > target_price:
                    diff = abs(strike - target_price)
                    if diff < min_call_diff:
                        min_call_diff = diff
                        closest_call = (opt, detail)
            elif detail['optType'] == 'PUT':
                if strike < target_price:
                    diff = abs(strike - target_price)
                    if diff < min_put_diff:
                        min_put_diff = diff
                        closest_put = (opt, detail)
        
        if closest_call:
            opt, detail = closest_call
            if opt not in selected_options:
                selected_options.append(opt)
                print(f"\n选中看涨期权: {opt}")
                print(f"到期日: {detail['ExpireDate']}")
                print(f"行权价: {detail['OptExercisePrice']}")
                print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            
        if closest_put:
            opt, detail = closest_put
            if opt not in selected_options:
                selected_options.append(opt)
                print(f"\n选中看跌期权: {opt}")
                print(f"到期日: {detail['ExpireDate']}")
                print(f"行权价: {detail['OptExercisePrice']}")
                print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            
        if selected_options:
            print(f"\n订阅期权合约: {selected_options}")
            
            for opt in selected_options:
                if opt not in strategy_state.monitors:
                    monitor = ContractMonitor(C, opt, strategy_state.account_manager)
                    strategy_state.monitors[opt] = monitor
                    monitor.load_parameters()  # 加载 XML 参数
                    print(f"【{opt}】–>初始化策略合约")
                    monitor.update_price()
            
            active_contracts = list(set(selected_options) | position_contracts)
            if active_contracts:
                strategy_state.subID = C.subscribe_whole_quote(active_contracts, callback=call_back)
                if strategy_state.subID > 0:
                    print(f"行情订阅成功，订阅号: {strategy_state.subID}")
                else:
                    print("行情订阅失败")
        else:
            print("未找到合适期权合约")
            
    except Exception as e:
        print(f"初始化异常: {str(e)}")
        traceback.print_exc()
        strategy_state.subID = 0

def handlebar(C):
    """K线数据更新时执行"""
    try:
        if not hasattr(C, 'monitors'):
            return
            
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
        for code, monitor in C.monitors.items():
            try:
                monitor.on_tick()
            except Exception as e:
                print(f"处理合约 {code} 异常: {str(e)}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"handlebar异常: {str(e)}")
        traceback.print_exc()

def calculate_trade_volume(account_info, price, current_position, available_volume=None, max_position=None):
    """
    计算合适的交易数量
    """
    try:
        if not account_info:
            print("账户信息未初始化")
            return 0
            
        available_fund = account_info.get('m_dAvailable', 0)
        
        if available_fund <= 0:
            print(f"可用资金不足: {available_fund}")
            return 0
            
        remaining_position = max_position - current_position if max_position is not None else float('inf')
        if remaining_position <= 0:
            print(f"已达到最大持仓限制: {max_position}")
            return 0
            
        max_volume_by_fund = int(available_fund / (price * 10000))
        
        volume = min(
            available_volume if available_volume is not None else float('inf'),
            remaining_position,
            max_volume_by_fund
        )
        
        if volume <= 0:
            print(f"计算后的交易数量为0")
            return 0
            
        print(f"\n>>> 交易数量计算 <<<")
        print(f"当前持仓: {current_position}张")
        print(f"最大持仓限制: {max_position if max_position else '不限'}张")
        print(f"剩余可买: {remaining_position}张")
        print(f"可用资金: {available_fund:.2f}")
        print(f"合约价格: {price:.4f}")
        print(f"资金允许买入: {max_volume_by_fund}张")
        print(f"可成交量限制: {available_volume if available_volume else '不限'}张")
        print(f"最终计划买入: {volume}张")
        
        return volume
        
    except Exception as e:
        print(f"计算交易数量异常: {str(e)}")
        return 0

def is_trade_time():
    """检查是否在交易时段"""
    current_time = time.localtime()
    hour = current_time.tm_hour
    minute = current_time.tm_min
    
    if current_time.tm_wday >= 5:
        return False
        
    if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
        return True
        
    if (hour >= 13 and hour < 15) or (hour == 15 and minute == 0):
        return True
        
    return False

def execute_order(contract_code, direction, volume, price_type, price, account_id, C, account_info, is_open=True, current_tick=None):
    """执行订单"""
    try:
        if not is_trade_time():
            print("当前不在交易时段,不能下单")
            return None
            
        if not account_info:
            print("账户信息无效")
            return None
            
        if is_open:
            try:
                total_position = 0
                has_position = False
                positions = get_trade_detail_data(account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_nVolume'):
                            total_position += getattr(pos, 'm_nVolume', 0)
                            if (hasattr(pos, 'm_strInstrumentID') and 
                                pos.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                has_position = True
                                print(f"发现本合约已有持仓，不能开新仓")
                                return None
                
                pending_volume = 0
                has_pending_orders = False
                orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
                if orders and isinstance(orders, list):
                    for order in orders:
                        if (hasattr(order, 'm_nOffsetFlag') and 
                            getattr(order, 'm_nOffsetFlag') == 50):
                            status = getattr(order, 'm_nOrderStatus', -1)
                            if status in [0, 1, 3]:
                                if (hasattr(order, 'm_strInstrumentID') and 
                                    order.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                    print(f"发现本合约有未完成委托，不能开新仓")
                                    return None
                                pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                getattr(order, 'm_nVolumeTraded', 0))
                                has_pending_orders = True
                
                total_exposure = total_position + pending_volume + volume
                max_position = account_info.get('max_position', 30)
                
                if total_exposure > max_position:
                    print(f"\n>>> 持仓限制检查 <<<")
                    print(f"当前持仓: {total_position}")
                    print(f"未完成开仓: {pending_volume}")
                    print(f"本次委托: {volume}")
                    print(f"总暴露: {total_exposure}")
                    print(f"最大持仓限制: {max_position}")
                    print("超过最大持仓限制，取消委托")
                    return None
                    
                if has_pending_orders:
                    print("存在未完成的开仓委托，等待处理完成后再开新仓")
                    return None
                    
            except Exception as e:
                print(f"检查持仓限制异常: {str(e)}")
                return None
                
        if not is_open:
            # 平仓时，使用传入的 current_tick 数据
            if current_tick:
                bid_vol = current_tick.get('bidVol', [0])[0]
                print(f"【{contract_code}】–>使用传入tick数据，买一量: {bid_vol}")
                if bid_vol < volume:
                    print(f"市场可成交量不足: 需要{volume}, 可成交量{bid_vol}")
                    return None
            else:
                # 若无传入数据，重试获取
                retries = 3
                for attempt in range(retries):
                    tick_data = C.get_full_tick([f"{contract_code}.SHO"])
                    if tick_data and f"{contract_code}.SHO" in tick_data:
                        data = tick_data[f"{contract_code}.SHO"]
                        bid_vol = data.get('bidVol', [0])[0]
                        print(f"【{contract_code}】–>重试获取tick数据，买一量: {bid_vol}")
                        if bid_vol >= volume:
                            break
                    print(f"【{contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                else:
                    print(f"【{contract_code}】–>无法获取市场报价，假设市场无成交量")
                    return None
            
        # 获取价格，优先使用传入的 tick 数据
        if current_tick and direction == 'SELL':
            exec_price = current_tick.get('bidPrice', [0])[0] if current_tick else price
            print(f"【{contract_code}】–>使用传入tick价格: {exec_price}")
        else:
            subscribe_code = f"{contract_code}.SHO"
            tick_data = C.get_full_tick([subscribe_code])
            if tick_data and subscribe_code in tick_data:
                data = tick_data[subscribe_code]
                if isinstance(data, dict):
                    bid_price = data.get('bidPrice', [0])[0]
                    ask_price = data.get('askPrice', [0])[0]
                    if bid_price > 0 and ask_price > 0:
                        print(f"获取到有效行情 - 买价: {bid_price}, 卖价: {ask_price}")
                        exec_price = bid_price if direction == 'SELL' else ask_price
                    else:
                        print("行情价格无效，使用委托价格")
                        exec_price = price
                else:
                    print("行情数据格式错误，使用委托价格")
                    exec_price = price
            else:
                print(f"无法获取{contract_code}市场报价，使用委托价格")
                exec_price = price
            
        if direction == 'BUY':
            if not is_open:
                print("不支持买入平仓操作")
                return None
            op_type = 50
        else:
            if is_open:
                print("不支持卖出开仓操作")
                return None
            op_type = 51
            
        print("\n>>> 委托参数 <<<")
        print(f"合约代码: {contract_code}")
        print(f"交易方向: {direction}")
        print(f"开平标志: {'开仓' if is_open else '平仓'}")
        print(f"委托数量: {volume}")
        print(f"价格类型: {price_type}")
        print(f"委托价格: {exec_price}")
        
        print(f"\n>>> 开始执行委托 - {'开仓' if is_open else '平仓'} <<<")
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        random_num = random.randint(1000, 9999)
        order_remark = f"{contract_code}_{direction}_{timestamp}_{random_num}"
        
        exec_price = round(exec_price, 4)
        result = passorder(
            op_type,
            1101,
            account_id,
            contract_code,
            price_type,
            exec_price,
            volume,
            '期权交易',
            2,
            order_remark,
            C
        )
        
        if result is not None:
            print(f"\n>>> 委托已发送 - 投资备注: {order_remark} <<<")
            return order_remark
        else:
            print("委托发送失败，返回值为None")
            return None
            
    except Exception as e:
        print(f"订单执行异常: {str(e)}")
        traceback.print_exc()
        return None

def clear_pending_orders(C, account_id):
    try:
        orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
        if not orders:
            print("未找到任何订单")
            return
        for order in orders:
            status = int(getattr(order, 'm_nOrderStatus', -1))
            order_id = getattr(order, 'm_strOrderSysID', '')
            if status in [0, 1, 3]:  # 未成交、部分成交、已报
                result = cancel(order_id, account_id, 'STOCK_OPTION', C)
                if result:
                    print(f"清理订单成功: {order_id}")
                else:
                    print(f"清理订单失败: {order_id}")
    except Exception as e:
        print(f"清理订单异常: {str(e)}")

def get_option_expiry_dates(C):
    """获取期权到期日列表"""
    try:
        current_date = datetime.now().strftime('%Y%m%d')
        expiry_dates = C.get_option_expire_date()
        if not expiry_dates:
            return []
            
        valid_dates = [d for d in expiry_dates if d > current_date]
        valid_dates.sort()
        return valid_dates
        
    except Exception as e:
        print(f"获取到期日失败: {str(e)}")
        return []

def get_options_by_strike(C, underlying, expiry, option_type):
    """
    获取指定到期日和类型的期权列表
    """
    try:
        options = []
        all_options = C.get_option_list(underlying, expiry, option_type.upper(), True)
        
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if detail:
                options.append({
                    'code': opt,
                    'strike': float(detail['OptExercisePrice']),
                    'type': option_type,
                    'expiry': detail['ExpireDate']
                })
                
        return options
        
    except Exception as e:
        print(f"获取期权列表失败: {str(e)}")
        return []

# 3. 账户管理类
class AccountManager:
    def __init__(self, account_id):
        """账户管理器初始化"""
        self.account_id = account_id
        self.account_info = {
            'm_strAccountID': account_id,
            'm_dAvailable': 0.0,
            'm_dBalance': 0.0,
            'm_dInstrumentValue': 0.0
        }
        self.orders = {}
        self.positions = {}
        self.last_update_time = 0
        self.update_interval = 1
        print(f"\n>>> 账户管理器初始化 - {account_id} <<<")

    def update(self, C):
        """
        更新账户信息和持仓状态
        """
        try:
            current_time = time.time()
            if current_time - self.last_update_time < self.update_interval:
                return
                        
            self.last_update_time = current_time
            
            orders = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ORDER')
            if orders:
                new_orders = {}
                for order in orders:
                    order_id = getattr(order, 'm_strOrderSysID', '')
                    if not order_id:
                        continue
                        
                    status = int(getattr(order, 'm_nOrderStatus', -1))
                    contract = getattr(order, 'm_strInstrumentID', '')
                    direction = int(getattr(order, 'm_nOffsetFlag', 0))
                    
                    if direction not in [50, 51]:
                        continue
                        
                    if status in [0, 1]:
                        new_orders[order_id] = {
                            'contract': contract,
                            'direction': direction,
                            'price': float(getattr(order, 'm_dLimitPrice', 0.0)),
                            'volume': int(getattr(order, 'm_nVolumeTotalOriginal', 0)),
                            'traded': int(getattr(order, 'm_nVolumeTraded', 0)),
                            'status': status,
                            'submit_time': getattr(order, 'm_strInsertTime', '')
                        }
                        
                        if order_id not in self.orders:
                            if hasattr(C, 'monitors'):
                                monitor = C.monitors.get(contract)
                                if monitor:
                                    monitor.active_orders[order_id] = {
                                        'time': time.time(),
                                        'is_close': direction == 51,
                                        'price': new_orders[order_id]['price'],
                                        'volume': new_orders[order_id]['volume'],
                                        'traded': new_orders[order_id]['traded'],
                                        'direction': direction
                                    }
                        elif hasattr(C, 'monitors'):
                            monitor = C.monitors.get(contract)
                            if monitor and order_id in monitor.active_orders:
                                monitor.active_orders[order_id].update({
                                    'traded': new_orders[order_id]['traded']
                                })
                
                self.orders = new_orders
                
                if hasattr(C, 'monitors'):
                    for monitor in C.monitors.values():
                        to_delete = [order_id for order_id in monitor.active_orders 
                                    if order_id not in new_orders]
                        for order_id in to_delete:
                            del monitor.active_orders[order_id]
            
            account_info = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ACCOUNT')
            if account_info and len(account_info) > 0:
                account_obj = account_info[0]
                new_account_info = {
                    'm_strAccountID': self.account_id,
                    'm_dAvailable': float(getattr(account_obj, 'm_dAvailable', 0.0)),
                    'm_dBalance': float(getattr(account_obj, 'm_dBalance', 0.0)),
                    'm_dInstrumentValue': float(getattr(account_obj, 'm_dInstrumentValue', 0.0))
                }
                
                if self._has_account_changes(new_account_info):
                    self.account_info = new_account_info
                    self._log_account_changes(new_account_info)
            
            positions = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'POSITION')
            if positions:
                new_positions = {}
                for pos in positions:
                    contract = getattr(pos, 'm_strInstrumentID', '')
                    volume = getattr(pos, 'm_nVolume', 0)
                    frozen_volume = getattr(pos, 'm_nFrozenVolume', 0)
                    if volume > 0:
                        open_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                        new_positions[contract] = {
                            'Volume': volume,
                            'FrozenVolume': frozen_volume,
                            'AvailableVolume': volume - frozen_volume,
                            'OpenPrice': open_price,
                            'PositionValue': volume * open_price * 10000,
                            'UnrealizedPL': float(getattr(pos, 'm_dPositionProfit', 0.0))
                        }
                        
                        if hasattr(C, 'monitors'):
                            monitor = C.monitors.get(contract)
                            if monitor and monitor.contract_code == contract:
                                monitor.position = volume
                                monitor.in_position = volume > 0
                                if volume > 0:
                                    if not monitor.entry_price:  # 仅在未设置时更新 entry_price
                                        monitor.entry_price = new_positions[contract]['OpenPrice']
                                        monitor.entry_time = time.strftime("%Y%m%d%H%M%S")
                                    if not monitor.original_entry_price or not monitor.is_legacy_position:  # 首次或非历史持仓设置
                                        if not monitor.update_price():
                                            monitor.current_price = open_price
                                            print(f"【{contract}】–>无法获取最新价，使用平均开仓价: {open_price:.4f}")
                                        monitor.original_entry_price = monitor.current_price
                                        print(f"【{contract}】–>设置原始开仓价为最新价: {monitor.original_entry_price:.4f}")
                                print(f"【{contract}】–>同步 entry_price: {monitor.entry_price:.4f}, 原始开仓价: {monitor.original_entry_price:.4f}")
                
                if self._has_position_changes(new_positions):
                    self._log_position_changes(new_positions)
                    self.positions = new_positions
                            
        except Exception as e:
            print(f"更新账户信息异常: {str(e)}")
            traceback.print_exc()

    def _has_account_changes(self, new_account_info):
        """检查账户信息是否有变化"""
        if not self.account_info:
            return True
            
        thresholds = {
            'm_dAvailable': 0.01,
            'm_dBalance': 0.01,
            'm_dInstrumentValue': 0.01,
        }
        
        for key, threshold in thresholds.items():
            old_value = self.account_info.get(key, 0)
            new_value = new_account_info[key]
            if abs(new_value - old_value) >= threshold:
                return True
                
        if new_account_info.get('m_strStatus') != self.account_info.get('m_strStatus'):
            return True
            
        return False
        
    def _log_account_changes(self, new_account_info):
        """记录账户变化"""
        print("\n>>> 账户状态 <<<")
        print(f"账户ID: {new_account_info['m_strAccountID']}")
        print(f"可用资金: {new_account_info['m_dAvailable']:.2f}")
        print(f"总资产: {new_account_info['m_dBalance']:.2f}")
        print(f"持仓市值: {new_account_info['m_dInstrumentValue']:.2f}")
        print("")
        
    def _has_position_changes(self, new_positions):
        """检查持仓是否有显著变化"""
        if not self.positions:
            return bool(new_positions)
            
        if set(self.positions.keys()) != set(new_positions.keys()):
            return True
            
        for contract in self.positions:
            if contract not in new_positions:
                continue
                
            old_pos = self.positions[contract]
            new_pos = new_positions[contract]
            
            if old_pos['Volume'] != new_pos['Volume']:
                return True
                
            old_pnl_ratio = (old_pos['UnrealizedPL'] / old_pos['PositionValue'] * 100 
                        if old_pos['PositionValue'] > 0 else 0)
            new_pnl_ratio = (new_pos['UnrealizedPL'] / new_pos['PositionValue'] * 100 
                        if new_pos['PositionValue'] > 0 else 0)
            if abs(new_pnl_ratio - old_pnl_ratio) > 0.1:
                return True
                
        return False
        
    def _log_position_changes(self, new_positions):
        """记录持仓变化"""
        if not new_positions:
            return
            
        print("\n>>> 持仓状态 <<<")
        for contract_id, pos in new_positions.items():
            volume = pos.get('Volume', 0)
            open_price = pos.get('OpenPrice', 0.0)
            unrealized_pl = pos.get('UnrealizedPL', 0.0)
            
            market_value = volume * open_price * 10000
            if market_value > 0:
                pl_ratio = unrealized_pl / market_value if market_value != 0 else 0
                print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}, 盈亏比例: {pl_ratio*100:.2f}%")
            else:
                print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}")
                if 'OpenTime' in pos:
                    print(f"开仓时间: {pos['OpenTime']}")
                if 'Direction' in pos:
                    print(f"方向: {'多头' if pos['Direction'] == 1 else '空头'}")
                if 'MarketValue' in pos:
                    print(f"市值: {pos['MarketValue']:.2f}")
                print("")