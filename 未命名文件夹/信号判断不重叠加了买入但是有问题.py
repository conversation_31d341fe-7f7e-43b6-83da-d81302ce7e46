#coding:gbk
import datetime
import time
import xml.etree.ElementTree as ET
import os
import logging

# 简化日志配置 - 只记录重要事件
logging.basicConfig(level=logging.WARNING, format='%(asctime)s %(message)s', datefmt='%H:%M:%S')

# ==================== 参数管理模块 ====================
class ParameterManager:
    """参数管理器 - 内嵌版本"""
    def __init__(self):
        # 核心可配置参数（与XML中的bind名称对应）
        self.params = {
            # 核心配置参数
            'underlying_code': '510300.SH',    # 标的代码
            'signal_threshold': 5,             # 连续同方向tick数量

            # 震荡检测参数
            'oscillation_period_size': 5,      # 每周期tick数量
            'oscillation_periods': 3,          # 需要的周期数量

            # 合约选择参数
            'min_days_to_expire': 7,           # 最少剩余天数

            # 交易参数
            'max_position_per_contract': 10,    # 单合约最大持仓量
            'order_timeout_seconds': 30,       # 委托超时时间(秒)
            'enable_real_trading': False,      # 真实交易开关
        }

        # 硬编码参数（不需要配置）
        self.fixed_params = {
            # 数据过滤参数
            'enable_duplicate_filter': True,   # 启用重复tick过滤
            'price_precision': 4,              # 价格精度

            # 价格链参数
            'max_chain_length': 30,            # 价格链最大长度
            'display_timestamp': True,         # 显示时间戳

            # 趋势检测参数
            'enable_trend_detection': True,    # 启用趋势检测
            'reset_after_signal': True,        # 信号触发后重置

            # 期权选择参数
            'select_call_count': 1,            # 选择1个认购期权
            'select_put_count': 1,             # 选择1个认沽期权
            'prefer_nearest_expiry': True,     # 优先最近到期
            'prefer_nearest_strike': True,     # 优先最近行权价

            # 日志参数
            'enable_tick_log': False,          # 禁用详细tick日志
            'enable_signal_log': True,         # 启用信号日志
        }
        
        # 尝试从XML加载参数
        self.xml_path = r"C:\国金证券QMT交易端\python\formulaLayout\新期权策略.xml"
        self.load_from_xml()
    
    def load_from_xml(self):
        """从QMT标准格式XML文件加载参数"""
        try:
            if os.path.exists(self.xml_path):
                tree = ET.parse(self.xml_path)
                root = tree.getroot()

                # 解析QMT标准格式: TCStageLayout/control/variable/item
                for item in root.findall('.//item'):
                    bind_name = item.get('bind')
                    param_value = item.get('value')

                    if bind_name and param_value is not None and bind_name in self.params:
                        # 类型转换
                        if param_value in ['0', '1']:
                            # QMT开关格式：1=True, 0=False
                            param_value = param_value == '1'
                        elif param_value.lower() in ['true', 'false']:
                            param_value = param_value.lower() == 'true'
                        elif param_value.isdigit():
                            param_value = int(param_value)
                        elif '.' in param_value and param_value.replace('.', '').replace('-', '').isdigit():
                            param_value = float(param_value)

                        self.params[bind_name] = param_value
                        print(f"  ?? 加载参数: {bind_name} = {param_value}")

                print(f"? 成功从QMT格式XML加载参数")
            else:
                print(f"?? XML文件不存在，使用默认参数")
        except Exception as e:
            print(f"?? XML加载失败，使用默认参数: {e}")
    
    def get(self, param_name, default_value=None):
        """获取参数值（先查找可配置参数，再查找硬编码参数）"""
        if param_name in self.params:
            return self.params[param_name]
        elif param_name in self.fixed_params:
            return self.fixed_params[param_name]
        else:
            return default_value
    
    def set(self, param_name, value):
        """设置参数值"""
        self.params[param_name] = value
    
    def print_params(self):
        """打印所有参数"""
        logging.info("=== 当前参数配置 ===")
        logging.info("?? 可配置参数:")
        for key, value in self.params.items():
            logging.info(f"  {key} = {value}")
        logging.info("?? 硬编码参数:")
        for key, value in self.fixed_params.items():
            logging.info(f"  {key} = {value}")
        logging.info("==================")

# ==================== 数据存储模块 ====================
class OptionDataStore:
    """数据存储类"""
    def __init__(self, param_manager):
        self.pm = param_manager

        # 系统配置（从参数管理器获取，可通过XML配置）
        self.underlying_code = self.pm.get('underlying_code', '510300.SH')
        self.selected_options = []

        # 数据存储
        self.last_prices = {}
        self.last_tick_time = {}
        self.price_chains = {}
        self.trend_count = {}
        self.trend_direction = {}
        self.trend_prices = {}
        self.signals = {}

        # 震荡检测数据
        self.oscillation_data = {}
        self.current_tick_id = {}  # 跟踪每个合约的tick ID

        # 交易相关数据
        self.positions = {}         # 持仓信息 {option_code: quantity}
        self.pending_orders = {}    # 待成交委托 {order_id: order_info}
        self.order_history = {}     # 委托历史 {option_code: [order_list]}

# ==================== 核心功能模块 ====================
class OptionMonitor:
    """期权监控核心类"""
    def __init__(self):
        self.pm = ParameterManager()
        self.data = OptionDataStore(self.pm)
    
    def get_underlying_price(self, C):
        """获取标的当前价格"""
        try:
            tick_data = C.get_full_tick([self.data.underlying_code])
            if self.data.underlying_code in tick_data:
                price = tick_data[self.data.underlying_code]['lastPrice']
                if hasattr(price, 'item'):
                    return float(price.item())
                return float(price)
            return None
        except Exception as e:
            logging.error(f"获取标的价格失败: {e}")
            return None
    
    def select_best_options(self, C):
        """选择最优期权合约"""
        try:
            underlying_price = self.get_underlying_price(C)
            if underlying_price is None:
                logging.error("无法获取标的价格")
                return []

            print(f"标的 {self.data.underlying_code} 当前价格: {underlying_price}")

            all_options = C.get_option_undl_data(self.data.underlying_code)
            if not all_options:
                print("未找到期权合约")
                return []

            call_options = []
            put_options = []
            current_date = datetime.datetime.now()
            min_days_to_expire = self.pm.get('min_days_to_expire', 7)

            for option_code in all_options:
                try:
                    option_detail = C.get_option_detail_data(option_code)
                    if option_detail:
                        strike_price = option_detail.get('OptExercisePrice', 0)
                        option_type = option_detail.get('optType', '')
                        expire_date = option_detail.get('ExpireDate', 0)

                        try:
                            expire_datetime = datetime.datetime.strptime(str(expire_date), '%Y%m%d')
                            days_to_expire = (expire_datetime - current_date).days
                            if days_to_expire <= 0:
                                continue
                            # 过滤到期日不足的合约
                            if days_to_expire < min_days_to_expire:
                                continue
                        except:
                            continue

                        if option_type == 'CALL' and strike_price > underlying_price:
                            call_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': strike_price - underlying_price
                            })
                        elif option_type == 'PUT' and strike_price < underlying_price:
                            put_options.append({
                                'code': option_code,
                                'strike': strike_price,
                                'days_to_expire': days_to_expire,
                                'price_distance': underlying_price - strike_price
                            })
                except Exception as e:
                    continue

            call_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))
            put_options.sort(key=lambda x: (x['days_to_expire'], x['price_distance']))

            selected = []
            call_count = self.pm.get('select_call_count', 1)
            put_count = self.pm.get('select_put_count', 1)

            for i in range(min(call_count, len(call_options))):
                selected.append(call_options[i]['code'])
                print(f"选中认购期权: {call_options[i]['code']}, 行权价: {call_options[i]['strike']}, 距离: {call_options[i]['price_distance']:.4f}, 到期: {call_options[i]['days_to_expire']}天")

            for i in range(min(put_count, len(put_options))):
                selected.append(put_options[i]['code'])
                print(f"选中认沽期权: {put_options[i]['code']}, 行权价: {put_options[i]['strike']}, 距离: {put_options[i]['price_distance']:.4f}, 到期: {put_options[i]['days_to_expire']}天")

            return selected

        except Exception as e:
            logging.error(f"选择期权合约失败: {e}")
            return []
    
    def filter_duplicate_ticks(self, option_code, current_price, current_time):
        """过滤相邻重复价格的tick"""
        try:
            if not self.pm.get('enable_duplicate_filter', True):
                return True
            
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(current_price, 'item'):
                current_price = float(current_price.item())
            else:
                current_price = float(current_price)
            
            current_price_rounded = round(current_price, price_precision)
            
            if option_code not in self.data.last_prices:
                self.data.last_prices[option_code] = current_price_rounded
                self.data.last_tick_time[option_code] = current_time
                return True
            
            last_price_rounded = round(self.data.last_prices[option_code], price_precision)
            if last_price_rounded == current_price_rounded:
                return False
            
            self.data.last_prices[option_code] = current_price_rounded
            self.data.last_tick_time[option_code] = current_time
            return True
            
        except Exception as e:
            logging.error(f"过滤tick错误: {e}")
            return False
    
    def update_price_chain(self, option_code, price):
        """更新价格链"""
        try:
            max_length = self.pm.get('max_chain_length', 30)
            price_precision = self.pm.get('price_precision', 4)
            
            if hasattr(price, 'item'):
                price = float(price.item())
            else:
                price = float(price)
            
            price_rounded = round(price, price_precision)
            
            if option_code not in self.data.price_chains:
                self.data.price_chains[option_code] = []
            
            self.data.price_chains[option_code].append(price_rounded)
            
            if len(self.data.price_chains[option_code]) > max_length:
                self.data.price_chains[option_code] = self.data.price_chains[option_code][-max_length:]
            
            return self.data.price_chains[option_code]
            
        except Exception as e:
            logging.error(f"更新价格链错误: {e}")
            return []
    
    def detect_trend_direction(self, option_code, current_price):
        """检测趋势方向"""
        try:
            if option_code not in self.data.trend_prices:
                self.data.trend_prices[option_code] = []
                self.data.trend_count[option_code] = 0
                self.data.trend_direction[option_code] = 0
            
            self.data.trend_prices[option_code].append(current_price)
            
            if len(self.data.trend_prices[option_code]) < 2:
                return 0, 0
            
            prev_price = self.data.trend_prices[option_code][-2]
            if current_price > prev_price:
                current_direction = 1
            elif current_price < prev_price:
                current_direction = -1
            else:
                return 0, 0
            
            if self.data.trend_direction[option_code] == current_direction:
                self.data.trend_count[option_code] += 1
            else:
                # 方向改变，需要重置趋势检测并启动震荡检测
                old_direction = self.data.trend_direction[option_code]
                old_count = self.data.trend_count[option_code]

                # 记录连续模式结束的价格序列
                if old_direction != 0:
                    end_sequence = self.data.trend_prices[option_code][-old_count-1:] if len(self.data.trend_prices[option_code]) > old_count else self.data.trend_prices[option_code]
                    timestamp = self.get_current_timestamp()
                    print(f"?? 连续模式结束: {option_code} [{timestamp}] {'↑' if old_direction == 1 else '↓'}{old_count} 序列:{end_sequence}")

                # 连续模式重置：当前tick是新方向的第一个tick
                self.data.trend_direction[option_code] = current_direction
                self.data.trend_count[option_code] = 1  # 当前tick计为新方向的第1个
                print(f"?? 连续模式重置: {option_code} 新方向{'↑' if current_direction == 1 else '↓'}从当前tick开始计数")

                # 只有在之前有方向的情况下才启动震荡检测（避免初始化时启动）
                if old_direction != 0:
                    print(f"?? 方向改变: {option_code} {'↑' if old_direction == 1 else '↓'}{old_count} → {'↑' if current_direction == 1 else '↓'}1")
                    self.try_start_oscillation_detection(option_code, current_price)

            return current_direction, self.data.trend_count[option_code]
            
        except Exception as e:
            logging.error(f"趋势检测错误: {e}")
            return 0, 0
    
    def check_trend_signal(self, option_code):
        """检查趋势信号"""
        signal_threshold = self.pm.get('signal_threshold', 5)

        if option_code not in self.data.trend_count:
            return False, None

        count = self.data.trend_count[option_code]
        direction = self.data.trend_direction[option_code]

        # 添加调试日志
        if count >= signal_threshold - 1:  # 接近触发时显示调试信息
            print(f"?? 连续信号检查: {option_code} 计数{count}/{signal_threshold} 方向{'↑' if direction == 1 else '↓' if direction == -1 else '无'}")

        if count >= signal_threshold:
            signal_type = "买入" if direction == 1 else "卖出"
            print(f"?? 连续信号触发: {option_code} {signal_type} 计数{count}/{signal_threshold}")
            return True, signal_type

        return False, None
    
    def reset_trend_detection(self, option_code, current_price):
        """重置趋势检测（信号触发后调用）"""
        self.data.trend_count[option_code] = 0
        self.data.trend_direction[option_code] = 0
        self.data.trend_prices[option_code] = [current_price]

        print(f"?? 连续信号触发后重置: {option_code} 价格:{current_price:.4f}")
        print(f"   连续信号触发后不启动震荡检测，等待方向改变时启动")
    
    def record_signal(self, option_code, signal_type, price, timestamp, sequence):
        """记录信号"""
        if option_code not in self.data.signals:
            self.data.signals[option_code] = []
        
        signal_info = {
            'type': signal_type,
            'price': price,
            'time': timestamp,
            'sequence': sequence.copy()
        }
        
        self.data.signals[option_code].append(signal_info)
        return signal_info

    # ==================== 震荡检测方法 ====================

    def try_start_oscillation_detection(self, option_code, current_price):
        """尝试启动震荡检测（仅在未激活时启动）"""
        if self.is_oscillation_active(option_code):
            print(f"   震荡检测已激活，跳过重复启动: {option_code}")
            return  # 已经在震荡检测中，不重复启动

        current_tick_id = self.data.current_tick_id.get(option_code, 0)
        print(f"   准备启动震荡检测: {option_code}")
        self.init_oscillation_detection(option_code, current_tick_id, current_price)

    def init_oscillation_detection(self, option_code, start_tick_id, trigger_price):
        """初始化震荡检测"""
        # 震荡检测从下一个tick开始
        next_tick_id = start_tick_id + 1
        self.data.oscillation_data[option_code] = {
            'active': True,
            'start_tick_id': next_tick_id,  # 从下一个tick开始
            'current_period': 1,
            'periods': [],
            'period_size': self.pm.get('oscillation_period_size', 5),
            'required_periods': self.pm.get('oscillation_periods', 3),
            'current_period_ticks': [],  # 空数组，等待下一个tick
            'current_period_start_id': next_tick_id,
            'trigger_price': trigger_price  # 记录触发价格
        }
        period_size = self.data.oscillation_data[option_code]['period_size']
        required_periods = self.data.oscillation_data[option_code]['required_periods']
        print(f"?? 启动震荡检测: {option_code} 从tick#{next_tick_id} (需要{required_periods}个周期,每周期{period_size}tick)")
        print(f"   触发价格: {trigger_price:.4f}, 等待下一个tick开始收集")

    def process_oscillation_tick(self, option_code, tick_id, price):
        """处理震荡模式的tick数据"""
        if not self.is_oscillation_active(option_code):
            return False, None

        data = self.data.oscillation_data[option_code]

        # 只处理start_tick_id及之后的tick
        if tick_id < data['start_tick_id']:
            print(f"?? 震荡检测跳过历史tick: {option_code} tick#{tick_id} < start#{data['start_tick_id']}")
            return False, None

        # 添加到当前周期
        data['current_period_ticks'].append(price)
        print(f"?? 震荡收集tick: {option_code} tick#{tick_id}:{price:.4f}")

        # 显示震荡进度
        current_ticks = len(data['current_period_ticks'])
        period_size = data['period_size']
        current_period = data['current_period']
        completed_periods = len(data['periods'])
        required_periods = data['required_periods']

        # 显示当前周期的价格序列
        current_sequence = data['current_period_ticks'][-min(4, len(data['current_period_ticks'])):]
        print(f"?? 震荡进度: {option_code} 周期{current_period}({current_ticks}/{period_size}tick) 已完成{completed_periods}/{required_periods}周期 当前序列:{current_sequence}")

        # 检查当前周期是否完成
        if len(data['current_period_ticks']) >= data['period_size']:
            period_result = self.complete_current_period(option_code, tick_id)
            if period_result is None:
                return False, None

            # 检查是否达到所需周期数
            if len(data['periods']) >= data['required_periods']:
                signal_type = self.check_oscillation_signal(option_code)
                if signal_type:
                    self.reset_oscillation_detection(option_code)
                    return True, signal_type

        return False, None

    def complete_current_period(self, option_code, tick_id):
        """完成当前周期的检测"""
        data = self.data.oscillation_data[option_code]
        ticks = data['current_period_ticks']

        if len(ticks) < 2:
            self.end_oscillation_detection(option_code, "周期tick数不足")
            return None

        # 计算周期方向：首价格 vs 尾价格
        start_price = ticks[0]
        end_price = ticks[-1]

        if start_price == end_price:
            self.end_oscillation_detection(option_code, "周期首尾价格相等")
            return None

        direction = 1 if end_price > start_price else -1
        direction_name = "上涨" if direction == 1 else "下跌"

        # 检查与前一周期方向是否一致
        if data['periods'] and data['periods'][-1]['direction'] != direction:
            self.end_oscillation_detection(option_code, f"周期方向改变: {direction_name}")
            return None

        # 记录周期结果
        period_info = {
            'period_num': data['current_period'],
            'start_id': data['current_period_start_id'],
            'end_id': tick_id,
            'start_price': start_price,
            'end_price': end_price,
            'direction': direction,
            'direction_name': direction_name,
            'tick_count': len(ticks)
        }

        data['periods'].append(period_info)

        timestamp = self.get_current_timestamp()
        print(f"?? 周期{data['current_period']}完成: {option_code} [{timestamp}] (不重叠设计)")
        print(f"   tick范围:[{data['current_period_start_id']}-{tick_id}] {start_price:.4f}→{end_price:.4f} {direction_name}")
        print(f"   完整序列:{ticks}")
        print(f"   下一周期将从tick#{tick_id + 1}开始")

        # 准备下一周期（不重叠设计）
        data['current_period'] += 1
        data['current_period_ticks'] = []  # 空数组，等待下一个tick
        data['current_period_start_id'] = tick_id + 1  # 从下一个tick开始

        return period_info

    def check_oscillation_signal(self, option_code):
        """检查震荡信号"""
        data = self.data.oscillation_data[option_code]
        periods = data['periods']

        if len(periods) < data['required_periods']:
            return None

        # 检查所有周期方向是否一致
        first_direction = periods[0]['direction']
        if all(p['direction'] == first_direction for p in periods):
            signal_type = "买入" if first_direction == 1 else "卖出"

            print(f"?? 震荡信号触发: {option_code} {signal_type}")
            for i, period in enumerate(periods):
                print(f"  周期{i+1}: [{period['start_id']}-{period['end_id']}] "
                      f"{period['start_price']:.4f}→{period['end_price']:.4f} "
                      f"{period['direction_name']}")

            return signal_type

        return None

    def is_oscillation_active(self, option_code):
        """检查震荡检测是否激活"""
        return (option_code in self.data.oscillation_data and
                self.data.oscillation_data[option_code]['active'])

    def end_oscillation_detection(self, option_code, reason):
        """结束震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            current_period = data['current_period']
            current_ticks = len(data['current_period_ticks'])
            current_sequence = data['current_period_ticks']

            timestamp = self.get_current_timestamp()
            self.data.oscillation_data[option_code]['active'] = False
            print(f"?? 震荡检测结束: {option_code} [{timestamp}] - {reason}")
            print(f"   最终状态: 完成{completed_periods}个周期, 当前周期{current_period}({current_ticks}tick)")
            print(f"   当前周期序列: {current_sequence}")
            if reason == "周期首尾价格相等" and len(current_sequence) >= 2:
                print(f"   首尾价格: {current_sequence[0]:.4f} == {current_sequence[-1]:.4f}")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")

    def reset_oscillation_detection(self, option_code):
        """重置震荡检测"""
        if option_code in self.data.oscillation_data:
            data = self.data.oscillation_data[option_code]
            completed_periods = len(data['periods'])
            reason = "信号触发成功" if completed_periods >= data['required_periods'] else "连续信号中断"

            del self.data.oscillation_data[option_code]
            print(f"?? 震荡检测重置: {option_code} - {reason}")
            print(f"   完成状态: {completed_periods}个周期完成")
            print(f"   震荡检测将等待下次连续计数重置时重新启动")
    
    def get_current_timestamp(self):
        """获取当前时间戳"""
        return datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]
    
    def print_price_update(self, option_code, timestamp=None):
        """打印价格更新"""
        display_timestamp = self.pm.get('display_timestamp', True)
        signal_threshold = self.pm.get('signal_threshold', 5)
        enable_tick_log = self.pm.get('enable_tick_log', False)

        if timestamp is None:
            timestamp = self.get_current_timestamp()

        if option_code not in self.data.price_chains:
            return

        price_str = "->".join([f"{p:.4f}" for p in self.data.price_chains[option_code]])

        trend_info = ""
        if (option_code in self.data.trend_count and
            self.data.trend_count[option_code] > 0):
            direction_symbol = "↑" if self.data.trend_direction[option_code] == 1 else "↓"
            trend_info = f" [{direction_symbol}{self.data.trend_count[option_code]}/{signal_threshold}]"

        # 根据配置决定是否显示tick日志
        if enable_tick_log:
            if display_timestamp:
                logging.info(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                logging.info(f"{option_code}: {price_str}{trend_info}")
        else:
            # 只在控制台显示，不记录到日志
            if display_timestamp:
                print(f"[{timestamp}] {option_code}: {price_str}{trend_info}")
            else:
                print(f"{option_code}: {price_str}{trend_info}")
    
    def print_signal_alert(self, option_code, signal_type, price, timestamp, sequence):
        """打印信号警报"""
        sequence_str = "->".join([f"{p:.4f}" for p in sequence])
        alert_msg = f"?? [{timestamp}] 触发{signal_type}信号！{option_code}: {sequence_str} (价格: {price:.4f})"

        # 信号警报：重要事件，记录到日志
        logging.warning(f"信号触发: {option_code} {signal_type} {price:.4f}")
        print(alert_msg)
    
    def validate_tick_timestamp(self, option_code, current_time):
        """验证tick时间戳顺序"""
        try:
            if option_code not in self.data.last_tick_time:
                self.data.last_tick_time[option_code] = current_time
                return True

            last_time = self.data.last_tick_time[option_code]

            # 简单的时间戳验证（假设时间戳是字符串格式）
            if isinstance(current_time, str) and isinstance(last_time, str):
                if current_time < last_time:
                    print(f"?? 时间戳倒退: {option_code} {last_time} -> {current_time}")
                    return False

            self.data.last_tick_time[option_code] = current_time
            return True

        except Exception as e:
            logging.error(f"时间戳验证错误: {e}")
            return True  # 验证失败时允许通过，避免阻塞

    def process_tick_data(self, C, option_code, current_price, current_time):
        """处理tick数据的完整流程"""
        try:
            # 0. 验证时间戳顺序
            if not self.validate_tick_timestamp(option_code, current_time):
                return

            # 1. 过滤重复tick
            if not self.filter_duplicate_ticks(option_code, current_price, current_time):
                return

            # 2. 分配有效的tick ID（过滤后才分配）
            if option_code not in self.data.current_tick_id:
                self.data.current_tick_id[option_code] = 0
            self.data.current_tick_id[option_code] += 1
            current_tick_id = self.data.current_tick_id[option_code]

            # 2. 更新价格链
            price_chain = self.update_price_chain(option_code, current_price)
            if not price_chain:
                return

            current_price_rounded = price_chain[-1]

            # 3. 检测连续趋势方向
            direction, count = self.detect_trend_direction(option_code, current_price_rounded)

            # 4. 检查连续趋势信号
            has_continuous_signal, continuous_signal_type = self.check_trend_signal(option_code)

            # 5. 并行检查震荡信号（两种模式独立运行）
            has_oscillation_signal, oscillation_signal_type = self.process_oscillation_tick(
                option_code, current_tick_id, current_price_rounded)

            # 6. 获取时间戳
            timestamp = self.get_current_timestamp()

            # 7. 处理信号（允许两种信号同时触发）
            if has_continuous_signal:
                trigger_sequence = self.data.trend_prices[option_code][-5:]
                self.record_signal(option_code, continuous_signal_type, current_price_rounded, timestamp, trigger_sequence)
                self.print_signal_alert(option_code, f"连续{continuous_signal_type}", current_price_rounded, timestamp, trigger_sequence)

                # 执行买入交易（只处理买入信号）
                if continuous_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"连续{continuous_signal_type}", current_price_rounded)

                self.reset_trend_detection(option_code, current_price_rounded)
                # 连续信号触发时，也重置震荡检测
                if self.is_oscillation_active(option_code):
                    self.reset_oscillation_detection(option_code)

            if has_oscillation_signal:
                # 震荡信号触发（可与连续信号同时触发）
                trigger_sequence = [current_price_rounded]
                self.record_signal(option_code, oscillation_signal_type, current_price_rounded, timestamp, trigger_sequence)
                self.print_signal_alert(option_code, f"震荡{oscillation_signal_type}", current_price_rounded, timestamp, trigger_sequence)

                # 执行买入交易（只处理买入信号）
                if oscillation_signal_type == "买入":
                    self.execute_buy_order(C, option_code, f"震荡{oscillation_signal_type}", current_price_rounded)

            # 8. 打印价格更新
            self.print_price_update(option_code, timestamp)

        except Exception as e:
            logging.error(f"处理tick数据错误: {e}")

    # ==================== 交易功能模块 ====================
    def execute_buy_order(self, C, option_code, signal_type, current_price):
        """执行买入委托"""
        try:
            # 检查是否启用真实交易
            enable_trading = self.pm.get('enable_real_trading', False)
            print(f"?? 交易开关检查: enable_real_trading = {enable_trading} (类型: {type(enable_trading)})")

            if not enable_trading:
                print(f"?? 模拟交易: {option_code} {signal_type} 价格:{current_price:.4f} (真实交易未启用)")
                return

            print(f"?? 准备执行买入: {option_code} {signal_type} 价格:{current_price:.4f}")

            # 获取交易参数
            max_position = self.pm.get('max_position_per_contract', 10)

            # 执行买入前检查
            if not self.check_buy_conditions(C, option_code, max_position):
                return

            # 获取市场数据
            market_data = self.get_market_data(C, option_code)
            if not market_data:
                print(f"? 无法获取市场数据: {option_code}")
                return

            # 计算买入数量
            buy_quantity = self.calculate_buy_quantity(C, option_code, market_data, max_position)
            if buy_quantity <= 0:
                print(f"? 买入数量为0: {option_code}")
                return

            # 执行买入委托
            order_result = self.place_buy_order(C, option_code, buy_quantity, market_data)
            if order_result:
                print(f"? 买入委托成功: {option_code} 数量:{buy_quantity} 价格:{market_data['ask_price']:.4f}")
            else:
                print(f"? 买入委托失败: {option_code}")

        except Exception as e:
            print(f"? 买入委托异常: {option_code} {e}")
            logging.error(f"买入委托异常: {option_code} {e}")

    def check_buy_conditions(self, C, option_code, max_position):
        """检查买入条件"""
        try:
            print(f"?? 检查买入条件: {option_code}")

            # 条件c: 检查持仓量 + 待成交量是否超过最大持仓
            current_position = self.get_current_position(C, option_code)
            pending_buy_quantity = self.get_pending_buy_quantity(option_code)

            print(f"   当前持仓:{current_position} 待成交:{pending_buy_quantity} 最大:{max_position}")

            if current_position + pending_buy_quantity >= max_position:
                print(f"? 持仓已满: {option_code} 当前:{current_position} 待成交:{pending_buy_quantity} 最大:{max_position}")
                return False

            # 条件d: 检查并处理超时委托
            self.handle_timeout_orders(C, option_code)

            print(f"? 买入条件检查通过: {option_code}")
            return True

        except Exception as e:
            print(f"? 买入条件检查异常: {option_code} {e}")
            return False

    def get_market_data(self, C, option_code):
        """获取市场数据"""
        try:
            # 使用QMT API获取五档行情
            full_tick = C.get_full_tick([option_code])
            if not full_tick or option_code not in full_tick:
                print(f"? 无法获取五档行情: {option_code}")
                return None

            tick_data = full_tick[option_code]

            result = {
                'ask_price': tick_data.get('askPrice1', 0),      # 卖1价
                'ask_volume': tick_data.get('askVolume1', 0),    # 卖1量
                'bid_price': tick_data.get('bidPrice1', 0),      # 买1价
                'bid_volume': tick_data.get('bidVolume1', 0),    # 买1量
                'last_price': tick_data.get('lastPrice', 0)      # 最新价
            }

            print(f"?? 市场数据: {option_code} 卖1:{result['ask_price']:.4f}({result['ask_volume']}) 买1:{result['bid_price']:.4f}({result['bid_volume']})")
            return result

        except Exception as e:
            print(f"? 获取市场数据异常: {option_code} {e}")
            return None

    def calculate_buy_quantity(self, C, option_code, market_data, max_position):
        """计算买入数量"""
        try:
            # 条件a: 检查卖1量是否足够
            ask_volume = market_data['ask_volume']
            ask_price = market_data['ask_price']

            if ask_volume <= 0 or ask_price <= 0:
                print(f"? 卖1数据无效: {option_code} 价格:{ask_price} 量:{ask_volume}")
                return 0

            # 计算可买入的最大数量
            current_position = self.get_current_position(C, option_code)
            pending_buy_quantity = self.get_pending_buy_quantity(option_code)
            max_can_buy = max_position - current_position - pending_buy_quantity

            # 取卖1量和最大可买量的较小值
            target_quantity = min(ask_volume, max_can_buy)

            # 条件b: 检查资金是否足够
            required_amount = target_quantity * ask_price * 10000  # 期权合约乘数
            available_cash = self.get_available_cash(C)

            if available_cash < required_amount:
                # 根据可用资金计算最大买入量
                max_affordable = int(available_cash / (ask_price * 10000))
                target_quantity = min(target_quantity, max_affordable)
                print(f"?? 资金不足，调整买入量: {option_code} 需要:{required_amount:.2f} 可用:{available_cash:.2f} 调整为:{target_quantity}")

            print(f"?? 买入数量计算: {option_code} 卖1量:{ask_volume} 最大可买:{max_can_buy} 目标:{target_quantity}")
            return max(0, target_quantity)

        except Exception as e:
            print(f"? 计算买入数量异常: {option_code} {e}")
            return 0

    def place_buy_order(self, C, option_code, quantity, market_data):
        """下买入委托"""
        try:
            # 使用QMT API下单
            # 参考QMT文档：passorder(op_type, order_mode, account_id, contract, price_type, exec_price, volume, strategy_name, quicktrade, msg, C)

            order_result = passorder(
                50,                              # op_type: 50=期权买入开仓
                1101,                            # order_mode: 1101=按股数
                account,                         # account_id: 全局账户变量
                option_code,                     # contract: 合约代码
                5,                               # price_type: 5=市价
                market_data['ask_price'],        # exec_price: 执行价格（市价单也需要填写）
                quantity,                        # volume: 数量
                "期权策略",                       # strategy_name: 策略名称
                2,                               # quicktrade: 2=立即下单
                f"期权买入-{option_code}",        # msg: 备注信息
                C                                # C: 上下文对象
            )

            # QMT的passorder函数没有返回值，我们通过其他方式确认下单状态
            print(f"? 委托下单请求已发送: {option_code} 数量:{quantity} 价格:{market_data['ask_price']:.4f}")

            # 记录委托信息（使用时间戳作为临时ID）
            temp_order_id = f"{option_code}_{int(datetime.datetime.now().timestamp())}"
            order_info = {
                'order_id': temp_order_id,
                'option_code': option_code,
                'quantity': quantity,
                'price': market_data['ask_price'],
                'order_time': datetime.datetime.now(),
                'status': 'pending'
            }

            self.data.pending_orders[temp_order_id] = order_info

            # 记录到历史
            if option_code not in self.data.order_history:
                self.data.order_history[option_code] = []
            self.data.order_history[option_code].append(order_info)

            return True

        except Exception as e:
            print(f"? 下单异常: {option_code} {e}")
            return False

    def get_current_position(self, C, option_code):
        """获取当前持仓"""
        try:
            # 使用QMT API获取持仓
            positions = get_trade_detail_data(account, 'STOCK_OPTION', 'POSITION')
            if not positions:
                return 0

            for position in positions:
                if position.m_strInstrumentID == option_code.split('.')[0]:
                    return position.m_nVolume

            return 0

        except Exception as e:
            print(f"? 获取持仓异常: {option_code} {e}")
            return 0

    def get_pending_buy_quantity(self, option_code):
        """获取待成交买入数量"""
        try:
            total_pending = 0
            for order_info in self.data.pending_orders.values():
                if (order_info['option_code'] == option_code and
                    order_info['status'] == 'pending'):
                    total_pending += order_info['quantity']

            return total_pending

        except Exception as e:
            print(f"? 获取待成交数量异常: {option_code} {e}")
            return 0

    def get_available_cash(self, C):
        """获取可用资金"""
        try:
            # 使用QMT API获取账户资金
            accounts = get_trade_detail_data(account, 'STOCK_OPTION', 'ACCOUNT')
            if accounts and len(accounts) > 0:
                return accounts[0].m_dAvailable
            return 0

        except Exception as e:
            print(f"? 获取可用资金异常: {e}")
            return 0

    def handle_timeout_orders(self, C, option_code):
        """处理超时委托"""
        try:
            timeout_seconds = self.pm.get('order_timeout_seconds', 30)
            current_time = datetime.datetime.now()

            timeout_orders = []
            for order_id, order_info in self.data.pending_orders.items():
                if (order_info['option_code'] == option_code and
                    order_info['status'] == 'pending'):

                    elapsed_seconds = (current_time - order_info['order_time']).total_seconds()
                    if elapsed_seconds > timeout_seconds:
                        timeout_orders.append(order_id)

            # 撤销超时委托
            for order_id in timeout_orders:
                if self.cancel_order(C, order_id):
                    print(f"? 撤销超时委托: {option_code} 委托号:{order_id}")
                else:
                    print(f"? 撤销委托失败: {option_code} 委托号:{order_id}")

        except Exception as e:
            print(f"? 处理超时委托异常: {option_code} {e}")

    def cancel_order(self, C, order_id):
        """撤销委托"""
        try:
            # 使用QMT API撤销委托
            cancel_result = cancel(order_id, account, 'STOCK_OPTION', C)

            if cancel_result:
                # 更新委托状态
                if order_id in self.data.pending_orders:
                    self.data.pending_orders[order_id]['status'] = 'cancelled'
                    del self.data.pending_orders[order_id]
                return True
            else:
                return False

        except Exception as e:
            print(f"? 撤销委托异常: {order_id} {e}")
            return False

# ==================== 全局监控对象 ====================
monitor = OptionMonitor()

# ==================== QMT全局变量 ====================
# 在QMT策略交易界面运行时，account的值会被自动赋值为策略配置中的账号
# 在编辑器界面运行时，需要手动赋值
account = "test"  # 这个值在实际运行时会被QMT自动替换

# ==================== QMT主程序 ====================
def init(C):
    """初始化函数"""
    # 初始化信息保留print以便在QMT控制台清楚显示
    print("=== QMT期权监控系统启动 (简化版) ===")
    print(f"?? 监控标的: {monitor.data.underlying_code}")
    print("?? 交易账号: 请在QMT策略交易界面选择")

    # 显示核心参数
    signal_threshold = monitor.pm.get('signal_threshold', 5)

    print(f"?? 核心参数:")
    print(f"  - 标的代码: {monitor.data.underlying_code}")
    print(f"  - 信号阈值: 连续{signal_threshold}个同方向tick触发信号")
    print(f"  - 配置文件: {monitor.pm.xml_path}")

    print(f"\n?? 固定设置:")
    print(f"  - 价格链长度: 30个tick (滑动窗口)")
    print(f"  - 重复过滤: 启用 (过滤相邻重复价格)")
    print(f"  - 时间戳显示: 启用")
    print(f"  - 期权选择: 1个认购 + 1个认沽 (最近到期+最近行权价)")

    print("\n?? QMT系统参数设置提醒:")
    print("  - 默认品种: 请在策略编辑器'基本信息'中设置为 510300")
    print("  - 默认周期: 请在策略编辑器'基本信息'中设置为 1分钟")
    print("  - 交易账号: 请在策略交易界面选择有期权权限的账号")

    print("=" * 50)

    # 记录启动信息到日志
    logging.info(f"系统启动 - 标的:{monitor.data.underlying_code} 阈值:{signal_threshold}")

def after_init(C):
    """初始化后执行"""
    try:
        print("?? 开始选择期权合约...")

        # 选择期权合约
        selected_options = monitor.select_best_options(C)
        if not selected_options:
            print("? 未能选择到合适的期权合约")
            return

        monitor.data.selected_options = selected_options
        print(f"? 成功选择 {len(selected_options)} 个期权合约")

        # 定义tick回调函数
        def tick_callback(data):
            """tick数据回调函数"""
            try:
                for option_code in data:
                    if option_code in monitor.data.selected_options:
                        tick_info = data[option_code]
                        current_price = tick_info.get('lastPrice', 0)
                        current_time = tick_info.get('timetag', '')

                        # 处理tick数据
                        monitor.process_tick_data(C, option_code, current_price, current_time)

            except Exception as e:
                print(f"? tick回调错误: {e}")
                logging.error(f"tick回调错误: {e}")

        # 订阅期权合约
        success_count = 0
        for option_code in selected_options:
            try:
                C.subscribe_quote(option_code, period='tick', callback=tick_callback)
                print(f"? 成功订阅合约: {option_code}")
                success_count += 1
            except Exception as e:
                print(f"? 订阅合约 {option_code} 失败: {e}")
                logging.error(f"订阅失败: {option_code} - {e}")

        print(f"?? 订阅完成: {success_count}/{len(selected_options)} 个合约")
        print("?? 开始监控...")

        signal_threshold = monitor.pm.get('signal_threshold', 5)
        print(f"?? 监控规则: 连续{signal_threshold}个同方向tick触发信号")

    except Exception as e:
        print(f"? 初始化失败: {e}")
        logging.error(f"初始化失败: {e}")

def handlebar(C):
    """K线回调函数（在这个场景下不使用）"""
    pass

# ==================== 工具函数 ====================
def update_parameter(param_name, new_value):
    """运行时更新参数"""
    try:
        monitor.pm.set(param_name, new_value)
        print(f"? 参数已更新: {param_name} = {new_value}")
    except Exception as e:
        print(f"? 参数更新失败: {e}")

def show_parameters():
    """显示当前参数"""
    monitor.pm.print_params()

def get_strategy_status():
    """获取策略状态"""
    selected_count = len(monitor.data.selected_options)
    total_signals = sum(len(signals) for signals in monitor.data.signals.values())
    active_trends = sum(1 for count in monitor.data.trend_count.values() if count > 0)

    print(f"?? 策略状态:")
    print(f"  - 监控合约: {selected_count} 个")
    print(f"  - 总信号数: {total_signals} 个")
    print(f"  - 活跃趋势: {active_trends} 个")

    # 显示每个合约的信号统计
    for option_code in monitor.data.selected_options:
        if option_code in monitor.data.signals:
            signals = monitor.data.signals[option_code]
            buy_count = sum(1 for s in signals if s['type'] == '买入')
            sell_count = sum(1 for s in signals if s['type'] == '卖出')
            print(f"  - {option_code}: 买入{buy_count}次, 卖出{sell_count}次")

def emergency_stop():
    """紧急停止"""
    print("?? 执行紧急停止...")
    monitor.data.selected_options.clear()
    monitor.data.trend_count.clear()
    monitor.data.trend_direction.clear()
    monitor.data.trend_prices.clear()
    print("? 策略已停止，所有状态已清空")


    这是最新的运行日志，我们来分析一下。比如这一段'[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ?? 连续模式结束: 10009442.SHO [14:42:37.076] ↓1 序列:[0.0552, 0.0553]
?? 连续模式重置: 10009442.SHO 新方向↑从当前tick开始计数
?? 方向改变: 10009442.SHO ↓1 → ↑1
   震荡检测已激活，跳过重复启动: 10009442.SHO
?? 震荡收集tick: 10009442.SHO tick#145:0.0553
?? 震荡进度: 10009442.SHO 周期3(5/5tick) 已完成2/3周期 当前序列:[0.055, 0.0553, 0.0552, 0.0553]
?? 周期3完成: 10009442.SHO [14:42:37.076] (不重叠设计)
   tick范围:[141-145] 0.0552→0.0553 上涨
   完整序列:[0.0552, 0.055, 0.0553, 0.0552, 0.0553]
   下一周期将从tick#146开始
?? 震荡信号触发: 10009442.SHO 买入
  周期1: [131-135] 0.0551→0.0553 上涨
  周期2: [136-140] 0.0552→0.0553 上涨
  周期3: [141-145] 0.0552→0.0553 上涨
?? 震荡检测重置: 10009442.SHO - 信号触发成功
   完成状态: 3个周期完成
   震荡检测将等待下次连续计数重置时重新启动
14:42:37 信号触发: 10009442.SHO 震荡买入 0.0553

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ?? [14:42:37.076] 触发震荡买入信号！10009442.SHO: 0.0553 (价格: 0.0553)
[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] 
?? 交易开关检查: enable_real_trading = True (类型: <class 'bool'>)

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ?? 准备执行买入: 10009442.SHO 震荡买入 价格:0.0553

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ?? 检查买入条件: 10009442.SHO

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟]    当前持仓:0 待成交:0 最大:True

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ? 买入条件检查通过: 10009442.SHO

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ?? 市场数据: 10009442.SHO 卖1:0.0000(0) 买1:0.0000(0)

[2025-06-26 14:42:37][新期权策略][SH510300][1分钟] ? 卖1数据无效: 10009442.SHO 价格:0 量:0
? 买入数量为0: 10009442.SHO'这一段给出了买入信号，但是没有购买成功，银行获取到的卖1价和量都是0，这个可能是在获取该合约的5档行情时的问题还是参数问题，需要你监测。另外，我们设置的最大持仓量是1，日志中下单量也是1，但是期权1张代表10000份，我不知道卖1量后面的数字比如是1是否也代表1手，这样就不用乘以10000了。这些问题都需要你详细查看官方文档来找到答案。