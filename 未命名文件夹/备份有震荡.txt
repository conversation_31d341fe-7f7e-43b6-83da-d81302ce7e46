# coding: gbk
import time
from datetime import datetime
import xml.etree.ElementTree as ET
import os
import pandas as pd
import random
import traceback

# 全局订单跟踪字典
order_tracker = {
    'orders': {},
    'positions': {}
}

# 1. 策略状态类
class StrategyState:
    def __init__(self, C):
        """策略状态初始化"""
        self.C = C  # 存储C
        self.subID = 0
        self.undl_code = '510300.SH'  # 标的代码
        self.monitors = {}
        self.account_manager = None
        self.last_check_time = 0

# 全局策略状态实例
strategy_state = None

# 工具函数
def show_data(data):
    """
    收集对象的关键数据
    @param data: 数据对象
    @return: dict 包含关键字段的字典
    """
    key_fields = {
        'm_strAccountID': '账户ID',
        'm_dAvailable': '可用资金',
        'm_dBalance': '账户余额',
        'm_dInstrumentValue': '持仓市值',
        'm_nOrderStatus': '订单状态',
        'm_strInstrumentID': '合约代码',
        'm_strInstrumentName': '合约名称',
        'm_dLimitPrice': '委托价格',
        'm_nVolumeTotal': '委托数量',
        'm_strOrderSysID': '系统委托号',
        'm_strErrorMsg': '错误信息'
    }
    
    result = {}
    for field, _ in key_fields.items():
        try:
            if hasattr(data, field):
                result[field] = getattr(data, field)
            else:
                result[field] = '<未知>'
        except Exception:
            result[field] = '<未知>'
    return result

# 2. 回调函数组
def account_callback(ContextInfo, accountInfo):
    """账户状态变化回调"""
    if not hasattr(ContextInfo, 'account_manager'):
        return
        
    try:
        # 让AccountManager处理更新
        ContextInfo.account_manager.update(ContextInfo)
    except Exception as e:
        print(f"账户回调处理异常: {str(e)}")

def order_callback(ContextInfo, orderInfo):
    data = show_data(orderInfo)
    key_info = f"订单号: {data['m_strOrderSysID']}, 合约: {data['m_strInstrumentID']}, 状态: {data['m_nOrderStatus']}"
    print(f"订单回调触发: {key_info}")
    try:
        if hasattr(ContextInfo, 'monitors'):
            contract_code = getattr(orderInfo, 'm_strInstrumentID', '')
            if not contract_code.endswith('.SHO'):
                contract_code += '.SHO'  # 统一添加 .SHO 后缀
            if contract_code in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract_code]
                monitor.update_order_status(orderInfo)
            else:
                print(f"未找到合约 {contract_code} 的监控器")
    except Exception as e:
        print(f"订单回调处理异常: {str(e)}")

def deal_callback(ContextInfo, dealInfo):
    """成交状态变化回调，同步成交价到 monitor 和 positions"""
    try:
        trade_id = getattr(dealInfo, 'm_strTradeID', '')
        order_id = getattr(dealInfo, 'm_strOrderSysID', '')
        contract = getattr(dealInfo, 'm_strInstrumentID', '')
        if not contract.endswith('.SHO'):
            contract += '.SHO'
        direction = '买入' if getattr(dealInfo, 'm_nOffsetFlag', 0) == 48 else '卖出'
        volume = getattr(dealInfo, 'm_nVolume', 0)
        price = getattr(dealInfo, 'm_dPrice', 0.0)
        trade_time = getattr(dealInfo, 'm_strTradeTime', '')
        
        print(f"\n>>> 新成交 <<< 合约: {contract}, 方向: {direction}, 数量: {volume}, 价格: {price:.4f}, 时间: {trade_time}")
        
        if hasattr(ContextInfo, 'account_manager'):
            ContextInfo.account_manager.update(ContextInfo)
            if hasattr(ContextInfo, 'monitors') and contract in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract]
                # 修改：打印 OffsetFlag 以调试
                offset_flag = getattr(dealInfo, 'm_nOffsetFlag', 0)
                print(f"【{contract}】–>成交回调: OffsetFlag={offset_flag}")
                # 调用 on_order_filled 处理持仓更新
                if direction == '买入':
                    monitor.on_order_filled(price, volume, 50)
                    monitor.position += volume
                    monitor.in_position = True
                    if not monitor.entry_price:
                        monitor.entry_price = price
                        monitor.original_entry_price = price
                        monitor.entry_batches = [{'price': price, 'volume': volume}]
                    print(f"【{contract}】–>成交同步: 持仓={monitor.position}, 开仓价={monitor.entry_price:.4f}")
                elif direction == '卖出':
                    monitor.on_order_filled(price, volume, 51)
                    monitor.position -= volume  # 修改：直接更新 position
                    monitor.in_position = monitor.position > 0
                    if contract in ContextInfo.account_manager.positions:
                        if ContextInfo.account_manager.positions[contract]['Volume'] == volume:
                            del ContextInfo.account_manager.positions[contract]
                            print(f"【{contract}】–>持仓全部平仓")
                        else:
                            ContextInfo.account_manager.positions[contract]['Volume'] -= volume
                            ContextInfo.account_manager.positions[contract]['PositionValue'] = ContextInfo.account_manager.positions[contract]['Volume'] * ContextInfo.account_manager.positions[contract]['OpenPrice'] * 10000
                            print(f"【{contract}】–>更新持仓量: {ContextInfo.account_manager.positions[contract]['Volume']}")
                    # 修改：平仓完成后清理状态
                    if monitor.pending_close and monitor.position <= 0:
                        monitor.pending_close = False
                        monitor.total_cost = 0
                        monitor.total_volume = 0
                        monitor.entry_price = None
                        monitor.price_history = []
                        monitor.highest_price = 0.0
                        if hasattr(monitor, 'batches_to_close'):
                            print(f"【{contract}】–>清理 batches_to_close")
                            delattr(monitor, 'batches_to_close')
                        print(f"【{contract}】–>平仓完成，持仓清零")
                    print(f"【{contract}】–>成交后持仓同步: 当前持仓={monitor.position}")
    
    except Exception as e:
        print(f"成交回调异常: {str(e)}")
        traceback.print_exc()

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """委托异常回调"""
    try:
        print(f"委托错误: {errMsg}")
    except Exception as e:
        print(f"订单错误回调异常: {str(e)}")

# 4. 合约监控类
class ContractMonitor:
    def __init__(self, C, contract_code, account_manager, max_position=30):
        """初始化合约监控器"""
        # 基础参数
        self.C = C
        self.contract_code = contract_code if contract_code.endswith('.SHO') else f"{contract_code}.SHO"
        self.account_manager = account_manager
        self.max_position = max_position
        
        # 新参数
        self.stop_loss = 0.03
        self.drawdown = 0.03
        self.trend_sensitivity = 3
        self.breakout_confirm = 1
        self.reversal_threshold = 0.05
        self.monitor_periods = 3
        self.commission_per_lot = 1.7
        self.trend_stop_enabled = 1
        
        # 价格相关
        self.current_price = 0.0  # 明确初始化为 0.0
        self.current_tick = None
        self.price_history = []
        self.initial_price = None
        self.daily_high = None
        self.daily_low = None
        self._price_initialized = False
        self.price_trend_points = []
        self.last_price_direction = None
        self.trend_direction = None
        
        # 持仓相关
        self.position = 0
        self.entry_price = 0.0
        self.original_entry_price = 0.0  # 明确初始化为 0.0
        self.entry_time = None
        self.in_position = False
        self.is_legacy_position = False
        self.highest_price = 0.0  # 明确初始化为 0.0
        self.lowest_price = 0.0   # 明确初始化为 0.0
        self.highest_since_entry = None
        self.lowest_since_entry = None
            
        # 订单相关
        self.active_orders = {}
        self.filled_orders = {}
        self.last_order_id = None
        self.last_order_check_time = time.time()
        self.order_timeout = 10
        
        # 交易状态
        self.pending_close = False
        self.close_reason = None
        self.trade_history = []
        
        # 趋势和动量指标（保留但不直接使用）
        self.trend = 0.0
        self.momentum = []
        self.volatility = 0.0
        
        # 添加已订阅合约集合（类变量）
        if not hasattr(ContractMonitor, '_subscribed_contracts'):
            ContractMonitor._subscribed_contracts = set()
        
        # 修改：初始化 effective_ticks
        self.effective_ticks = []  # 确保 calculate_trend 不会因未初始化而出错

    # 修改后的趋势计算逻辑，仅在连续上涨或下跌时返回明确方向
    def calculate_trend(self):
        """计算趋势：tick连续趋势与震荡模式并行，周期检测不影响连续tick监测"""
        if not hasattr(self, 'oscillation_ended'):
            self.oscillation_ended = False
        if not hasattr(self, 'oscillation_active'):
            self.oscillation_active = False
        if not hasattr(self, 'period_trends'):
            self.period_trends = []
        if not hasattr(self, 'oscillation_idx'):
            self.oscillation_idx = 0
        if not hasattr(self, '_last_oscillation_prices_len'):
            self._last_oscillation_prices_len = -1
        if not hasattr(self, '_last_trend'):
            self._last_trend = None
        if not hasattr(self, '_last_trend_sequence'):
            self._last_trend_sequence = None

        current_time = time.time()

        if self.oscillation_ended and not self.oscillation_active:
            return None, "震荡监测已结束"

        prices = [p['price'] for p in self.effective_ticks]
        times = [p['time'] for p in self.effective_ticks]

        def get_valid_changes(price_list):
            changes = []
            for i in range(1, len(price_list)):
                if price_list[i] > price_list[i-1]:
                    changes.append('上涨')
                elif price_list[i] < price_list[i-1]:
                    changes.append('下跌')
            return changes

        effective_prices = prices

        required_changes = self.trend_sensitivity + self.breakout_confirm
        min_ticks = required_changes + 1
        continuous_result = None
        # 修改2：连续 tick 监测从第一个 tick 开始，直到达到 min_ticks
        if len(effective_prices) >= min_ticks:
            recent_prices = effective_prices[-min_ticks:]
            changes = get_valid_changes(recent_prices)
            
            reset_triggered = False
            reset_idx = -1
            for i in range(1, len(changes)):
                if changes[i] != changes[i-1]:
                    reset_triggered = True
                    reset_idx = i + 1
                    break

            if not reset_triggered:
                all_positive = all(c == '上涨' for c in changes)
                all_negative = all(c == '下跌' for c in changes)
                if all_positive:
                    trend_sequence = [f'{p:.4f}' for p in recent_prices]
                    if self._last_trend != "up" or self._last_trend_sequence != trend_sequence:
                        print(f"【{self.contract_code}】–>趋势触发: 连续上涨 {len(changes)} 次, 序列: {trend_sequence}")
                        self._last_trend = "up"
                        self._last_trend_sequence = trend_sequence
                    continuous_result = ("up", "continuous")
                    # 修改1：连续 tick 触发趋势时结束震荡监测
                    if self.oscillation_active:
                        self.oscillation_active = False
                        self.oscillation_idx = 0
                        self.period_trends.clear()
                        print(f"【{self.contract_code}】–>连续tick触发趋势，震荡监测结束")
                elif all_negative:
                    trend_sequence = [f'{p:.4f}' for p in recent_prices]
                    if self._last_trend != "down" or self._last_trend_sequence != trend_sequence:
                        print(f"【{self.contract_code}】–>趋势触发: 连续下跌 {len(changes)} 次, 序列: {trend_sequence}")
                        self._last_trend = "down"
                        self._last_trend_sequence = trend_sequence
                    continuous_result = ("down", "continuous")
                    # 修改1：连续 tick 触发趋势时结束震荡监测
                    if self.oscillation_active:
                        self.oscillation_active = False
                        self.oscillation_idx = 0
                        self.period_trends.clear()
                        print(f"【{self.contract_code}】–>连续tick触发趋势，震荡监测结束")

            # 修改2：方向改变时重置连续 tick 监测并激活震荡监测，仅在未激活时打印
            if reset_triggered and not self.oscillation_active:  # 添加条件：仅在未激活时打印
                self.oscillation_active = True
                self.oscillation_idx = len(effective_prices) - len(recent_prices) + reset_idx
                self._last_trend = None
                self._last_trend_sequence = None
                print(f"【{self.contract_code}】–>进入震荡监测，起点: {effective_prices[self.oscillation_idx-1]:.4f}")

        oscillation_result = None
        if self.oscillation_active:
            oscillation_prices = effective_prices[self.oscillation_idx-1:]
            current_idx = 0
            period_trends = self.period_trends
            
            # 初始化震荡日志行
            timestamp = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime())
            log_line = f"[{timestamp}][期权策略][SH510300][1分钟] 【{self.contract_code}】"
            cycle_count = 0

            while current_idx < len(oscillation_prices) and len(period_trends) < self.monitor_periods:
                # 修改3：每个周期需达到 min_ticks
                if len(oscillation_prices[current_idx:]) < min_ticks:
                    break  # 未达到 min_ticks 时等待更多数据
                period_prices = oscillation_prices[current_idx:current_idx + min_ticks]
                period_changes = get_valid_changes(period_prices)
                
                cycle_count += 1
                start_price = period_prices[0]
                end_price = period_prices[-1]
                if end_price > start_price:
                    period_direction = "上涨"
                elif end_price < start_price:
                    period_direction = "下跌"
                else:
                    period_direction = "震荡"
                
                # 在同一行追加周期信息
                cycle_info = f"–>震荡周期 {cycle_count}: 方向 {period_direction}, 序列: {[f'{p:.4f}' for p in period_prices]}"
                log_line += cycle_info
                
                if period_direction == "震荡":
                    log_line += f" –>结束震荡监测：周期 {cycle_count} 为震荡"
                    print(log_line)
                    period_trends.clear()
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_idx = 0
                    # 修改5：震荡监测结束不输出方向
                    break
                
                if period_trends and period_trends[-1] != period_direction:
                    log_line += f" –>结束震荡监测：周期 {cycle_count} 方向改变 ({period_trends[-1]} -> {period_direction})"
                    print(log_line)
                    period_trends.clear()
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_idx = 0
                    # 修改5：震荡监测结束不输出方向
                    break
                
                period_trends.append(period_direction.lower())
                self.oscillation_idx += current_idx
                current_idx += min_ticks  # 修改3：每个周期完整移动 min_ticks
                
                if len(period_trends) == self.monitor_periods and all(t == period_trends[0] for t in period_trends):
                    log_line += f" –>趋势触发: 震荡{period_trends[0]} {self.monitor_periods}周期"
                    print(log_line)
                    oscillation_result = (period_trends[0].replace('上涨', 'up').replace('下跌', 'down'), "oscillation")
                    self.oscillation_ended = False
                    self.oscillation_active = False
                    self.oscillation_idx = 0
                    # 修改1：震荡监测触发趋势时重置连续 tick 监测
                    self._last_trend = None
                    self._last_trend_sequence = None
                    print(f"【{self.contract_code}】–>震荡监测触发趋势，连续tick监测重置")
                    break

        # 修改1：根据哪个先达到条件返回结果
        if continuous_result:
            if hasattr(self, '_price_log') and self._price_log:
                print(f"[{(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))}][期权策略][SH510300][1分钟] {self._price_log}")
                self._price_log = ""
            return continuous_result
        if oscillation_result:
            if hasattr(self, '_price_log') and self._price_log:
                print(f"[{(time.strftime('%Y-%m-%d %H:%M:%S', time.localtime()))}][期权策略][SH510300][1分钟] {self._price_log}")
                self._price_log = ""
            return oscillation_result

        # 修改2 & 3：未达到 min_ticks 或震荡未完成时不返回结果
        return None, "等待更多有效tick变化"

    def update_order_status(self, order_info):
        try:
            current_time = time.time()
            
            if isinstance(order_info, str):
                order_id = order_info
                order_info = get_value_by_order_id(
                    order_id,
                    self.account_manager.account_id,
                    'STOCK_OPTION',
                    'ORDER'
                )
                if not order_info:
                    print(f"未找到订单 {order_id} 的信息")
                    if order_id in self.active_orders:
                        del self.active_orders[order_id]
                    return
            
            order_id = getattr(order_info, 'm_strOrderSysID', '')
            contract = getattr(order_info, 'm_strInstrumentID', '')
            order_status = getattr(order_info, 'm_nOrderStatus', -1)
            direction = getattr(order_info, 'm_nOffsetFlag', 0)
            volume = getattr(order_info, 'm_nVolumeTotalOriginal', 0)
            traded = getattr(order_info, 'm_nVolumeTraded', 0)
            price = float(getattr(order_info, 'm_dLimitPrice', 0.0))
            
            if order_id not in self.active_orders:
                return
                
            self.account_manager.update(self.C)
            
            order_info_dict = self.active_orders[order_id]
            is_close_order = order_info_dict.get('is_close', False)
            timeout = 30  # 统一超时为 30 秒
            
            print(f"【{self.contract_code}】–>订单状态更新–>ID: {order_id}, 方向: {'买入开仓' if direction == 50 else '卖出平仓'}")
            print(f"数量: {traded}/{volume}, 价格: {price}, 状态: {order_status}")
            
            if order_status in [0, 1]:  # 未成交或部分成交
                print(f"【{self.contract_code}】–>订单活跃–>ID: {order_id}, 状态: {order_status}")
            elif order_status == 3:  # 已报
                print(f"【{self.contract_code}】–>订单已报–>ID: {order_id}")
            elif order_status == 5:  # 已撤单
                print(f"【{self.contract_code}】–>订单已撤–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print(f"【{self.contract_code}】–>平仓订单被撤销–>重新提交")
                    time.sleep(1)
                    self.exit_position("订单状态更新触发平仓")
            elif order_status == 4:  # 已拒绝
                print(f"【{self.contract_code}】–>订单被拒绝–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print(f"【{self.contract_code}】–>平仓订单被拒绝–>重新提交")
                    time.sleep(1)
                    self.exit_position("订单状态更新触发平仓")
            else:
                print(f"【{self.contract_code}】–>未知订单状态–>ID: {order_id}, 状态: {order_status}")
            
            order_time = order_info_dict['time']
            if current_time - order_time > timeout:
                print(f"【{self.contract_code}】–>订单超时–>ID: {order_id}, 平仓单: {is_close_order}")
                if self.cancel_order(self.C, order_id):
                    print(f"【{self.contract_code}】–>订单超时撤单成功–>ID: {order_id}")
                    del self.active_orders[order_id]
                    if is_close_order:
                        print(f"【{self.contract_code}】–>平仓订单超时–>重新提交")
                        time.sleep(1)
                        self.exit_position("订单状态更新触发平仓")
                else:
                    print(f"【{self.contract_code}】–>订单超时撤单失败–>ID: {order_id}")
                    # 检查最新状态并移除
                    orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                    order_found = False
                    if orders and isinstance(orders, list):
                        for order in orders:
                            if getattr(order, 'm_strOrderSysID', '') == order_id:
                                status = int(getattr(order, 'm_nOrderStatus', -1))
                                if status not in [0, 1, 3, 50, 51]:
                                    print(f"【{self.contract_code}】–>订单已非活动状态，移除–>ID: {order_id}, 状态: {status}")
                                    del self.active_orders[order_id]
                                order_found = True
                                break
                    if not order_found and order_id in self.active_orders:
                        print(f"【{self.contract_code}】–>订单未在最新数据中找到，移除–>ID: {order_id}")
                        del self.active_orders[order_id]
            else:
                remaining_time = timeout - (current_time - order_time)
                print(f"【{self.contract_code}】–>订单等待中–>剩余时间: {remaining_time:.1f}秒")
                if traded > 0:
                    print(f"【{self.contract_code}】–>订单部分成交–>{traded}/{volume}")
        except Exception as e:
            print(f"【{self.contract_code}】–>更新订单状态异常: {str(e)}")
            traceback.print_exc()

    def cancel_order(self, C, order_sys_id=None, contract_code=None):
        """撤销委托订单"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            active_states = [0, 1, 3, 50, 51]
            orders_cancelled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                current_order_id = getattr(order, 'm_strOrderSysID', '')
                current_contract = getattr(order, 'm_strInstrumentID', '')
                
                if order_sys_id and current_order_id != order_sys_id:
                    continue
                    
                if contract_code and current_contract != contract_code:
                    continue
                    
                if status in active_states:
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    
                    if volume_total > volume_traded:
                        result = cancel(
                            current_order_id,
                            self.account_manager.account_id,
                            'STOCK_OPTION',
                            C
                        )
                        
                        if result:
                            print(f"【{self.contract_code}】–>撤单成功–>ID: {current_order_id}")
                            orders_cancelled = True
                            if current_order_id in self.active_orders:
                                del self.active_orders[current_order_id]
                        else:
                            print(f"【{self.contract_code}】–>撤单失败–>ID: {current_order_id}")
                            # 主动检查状态并移除已非活动订单
                            if status not in active_states or volume_total <= volume_traded:
                                if current_order_id in self.active_orders:
                                    print(f"【{self.contract_code}】–>订单已非活动状态，移除–>ID: {current_order_id}")
                                    del self.active_orders[current_order_id]
                            # 若仍在活动状态，记录失败但不移除
                            
            return orders_cancelled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤单异常: {str(e)}")
            traceback.print_exc()
            return False

    def load_parameters(self):
        """从XML文件加载策略参数"""
        try:
            xml_path = r"C:\国金QMT交易端模拟\python\formulaLayout\期权策略.xml"
            print(f"【{self.contract_code}】–>加载策略参第–>路径: {xml_path}")
            
            if not os.path.exists(xml_path):
                raise FileNotFoundError(f"未找到配置文件: {xml_path}")
            
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            updated_params = []
            for item in root.findall('.//item'):
                bind = item.get('bind')
                value = item.get('value')
                
                if not bind or not value:
                    continue
                    
                try:
                    if bind == 'max_position':
                        self.max_position = int(value)
                        updated_params.append(('最大持仓', self.max_position))
                    elif bind == 'stop_loss':
                        self.stop_loss = float(value)
                        updated_params.append(('止损比例', self.stop_loss))
                    elif bind == 'drawdown':
                        self.drawdown = float(value)
                        updated_params.append(('回撤比例', self.drawdown))
                    elif bind == 'trend_sensitivity':
                        self.trend_sensitivity = int(value)
                        updated_params.append(('趋势敏感度', self.trend_sensitivity))
                    elif bind == 'breakout_confirm':
                        self.breakout_confirm = int(value)
                        updated_params.append(('突破确认', self.breakout_confirm))
                    elif bind == 'reversal_threshold':
                        self.reversal_threshold = float(value)
                        updated_params.append(('反转阈值', self.reversal_threshold))
                    elif bind == 'monitor_periods':
                        self.monitor_periods = int(value)
                        updated_params.append(('监测周期数', self.monitor_periods))
                    elif bind == 'commission_per_lot':
                        self.commission_per_lot = float(value)
                        updated_params.append(('每张手续费', self.commission_per_lot))
                    elif bind == 'trend_stop_enabled':
                        self.trend_stop_enabled = int(value)
                        updated_params.append(('趋势止损开关', self.trend_stop_enabled))
                except ValueError as e:
                    print(f"【{self.contract_code}】–>参数[{bind}]格式错误: {str(e)}")
                    continue
            
            if updated_params:
                print(f"【{self.contract_code}】–>成功更新参数:")
                for param_name, param_value in updated_params:
                    print(f"{param_name}: {param_value}")
            else:
                print(f"【{self.contract_code}】–>警告: 未能更新任何参数")
        except Exception as e:
            print(f"【{self.contract_code}】–>加载参数异常: {str(e)}")
            print(f"【{self.contract_code}】–>使用默认/parameters")

    def init_position(self, contract_code, volume, open_price, open_time=None):
        """初始化持仓，复用 update_price 的结果"""
        if not self.update_price():
            print(f"【{self.contract_code}】–>无法获取实时tick价格，初始化失败")
            return False
        print(f"【{self.contract_code}】–>获取实时tick价格成功: {self.current_price:.4f}")
        self.entry_price = self.current_price  # 直接使用 update_price 的结果
        self.original_entry_price = self.current_price
        self._entry_price_fixed = True
        self.position = volume
        self.in_position = volume > 0
        self.highest_price = self.current_price
        self.lowest_price = self.current_price
        self.total_cost = self.entry_price * volume * 10000
        self.total_volume = volume
        self.entry_batches = [{'price': self.entry_price, 'volume': volume}]
        if self.account_manager and self.contract_code.replace('.SHO', '') in self.account_manager.positions:
            self.account_manager.positions[self.contract_code.replace('.SHO', '')]['OpenPrice'] = self.entry_price
            self.account_manager.positions[self.contract_code.replace('.SHO', '')]['CustomOpenPrice'] = self.entry_price
            print(f"【{self.contract_code}】–>同步开仓价到account_manager: {self.entry_price:.4f}")
        print(f"【{self.contract_code}】–>初始化总成本: {self.total_cost:.2f}, 总持仓量: {self.total_volume}")
        print(f"【{self.contract_code}】–>初始化入场批次: 价格={self.entry_price:.4f}, 数量={volume}")
        return True

    def update_price(self):
        """更新价格数据，简化打印格式"""
        try:
            current_time = time.time()
            self._last_update_time = current_time
            
            tick_data = self.C.get_full_tick([self.contract_code])
            if not isinstance(tick_data, dict) or self.contract_code not in tick_data:
                print(f"【{self.contract_code}】–>实时tick数据错误或无该合约")
                return False
            
            data = tick_data[self.contract_code]
            if not isinstance(data, dict):
                print(f"【{self.contract_code}】–>实时tick数据内容格式错误: {type(data)}")
                return False
            
            price = data.get('lastPrice', 0.0)
            if price <= 0:
                bid_price = data.get('bidPrice', [0])[0]
                ask_price = data.get('askPrice', [0])[0]
                price = (bid_price + ask_price) / 2 if bid_price > 0 and ask_price > 0 else data.get('lastClose', 0.0)
                if price <= 0:
                    print(f"【{self.contract_code}】–>无法获取有效实时价格")
                    return False
            
            price = round(price, 4)
            if self.current_price != price:
                self.current_price = price
                current_tick_time = data.get('timetag', '')
                self.price_history.append({'price': price, 'time': current_tick_time})
                self.price_history.sort(key=lambda x: x['time'])
                if len(self.price_history) > 20:
                    self.price_history.pop(0)
                
                if not hasattr(self, 'effective_ticks'):
                    self.effective_ticks = []
                if not self.effective_ticks or self.effective_ticks[-1]['price'] != price:
                    self.effective_ticks.append({'price': price, 'time': current_tick_time})
                    # 修改：基于 effective_ticks 更新 highest_price，仅在持仓期间
                    if self.in_position and hasattr(self, 'effective_ticks'):
                        self.highest_price = max([tick['price'] for tick in self.effective_ticks])
                
                if not self._price_initialized:
                    self.initial_price = price
                    self.daily_high = price
                    self.daily_low = price
                    # 移除：if self.in_position: self.highest_price = price
                    self._price_initialized = True
                else:
                    self.daily_high = max(self.daily_high, price) if self.daily_high is not None else price
                    self.daily_low = min(self.daily_low, price) if self.daily_low is not None else price
            
            return True
        except Exception as e:
            print(f"【{self.contract_code}】–>更新价格异常: {str(e)}")
            return False

    # 修改：仅在连续下跌趋势中触发趋势止损
    def check_exit_conditions(self, check_time=True):
        """检查是否满足出场条件，统一基于每批次开仓价计算止损/止盈"""
        if not self.in_position or (check_time and not is_trade_time()) or not self.update_price():
            print(f"【{self.contract_code}】–>未持仓或非交易时间或更新价格失败，跳过平仓检查")
            return False
                                                    
        # 修改：检查未完成订单
        if self.active_orders:
            for order_id, info in list(self.active_orders.items()):
                if info['direction'] == 51:  # 平仓订单（SELL 改为 51，与方向一致）
                    print(f"【{self.contract_code}】–>存在未完成平仓订单，撤销并重新提交: ID={order_id}")
                    if self.cancel_order(self.C, order_id):
                        del self.active_orders[order_id]
                    else:
                        # 修改：再次检查订单状态
                        orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                        order_found = False
                        for order in orders:
                            if getattr(order, 'm_strOrderSysID', '') == order_id:
                                status = getattr(order, 'm_nOrderStatus', -1)
                                if status not in [0, 1, 3, 50, 51]:
                                    print(f"【{self.contract_code}】–>订单已结束，移除: ID={order_id}")
                                    del self.active_orders[order_id]
                                order_found = True
                                break
                        if not order_found:
                            print(f"【{self.contract_code}】–>订单不存在，移除: ID={order_id}")
                            del self.active_orders[order_id]
                        else:
                            print(f"【{self.contract_code}】–>撤销失败，暂不平仓")
                            return False
                else:
                    print(f"【{self.contract_code}】–>存在未完成开仓订单，继续平仓")

        # 检查每批次持仓是否满足平仓条件
        if not hasattr(self, 'entry_batches') or not self.entry_batches:
            print(f"【{self.contract_code}】–>无入场批次记录，尝试同步持仓")
            self.account_manager.update(self.C)
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            contract_key = self.contract_code.replace('.SHO', '')
            for pos in positions:
                if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract_key:
                    self.position = getattr(pos, 'm_nVolume', 0)
                    self.in_position = self.position > 0
                    if self.in_position:
                        open_price = getattr(pos, 'm_dOpenPrice', self.current_price if self.update_price() else 0.0)
                        self.entry_price = open_price
                        self.entry_batches = [{'price': self.entry_price, 'volume': self.position}]
                        print(f"【{self.contract_code}】–>强制同步持仓: 数量={self.position}, 开仓价={self.entry_price:.4f}")
                    break
            if not self.in_position or not self.entry_batches:
                print(f"【{self.contract_code}】–>仍无有效持仓或批次信息，跳过平仓")
                return False

        batches_to_close = []
        trend = None
        # 初始化日志控制变量
        if not hasattr(self, '_last_trend_logged'):
            self._last_trend_logged = None
        if not hasattr(self, '_last_trend_log_time'):
            self._last_trend_log_time = 0
        current_time = time.time()
        log_interval = 60  # 每60秒最多记录一次相同的趋势状态

        # 修改：趋势开关逻辑与日志优化
        if self.trend_stop_enabled:
            trend, trend_type = self.calculate_trend()
            if trend != "down":
                return False
            else:
                # 仅在趋势变为下跌时输出一次，或满足时间间隔
                if trend != self._last_trend_logged or current_time - self._last_trend_log_time >= log_interval:
                    print(f"【{self.contract_code}】–>趋势开关开，检测到下跌趋势，开始检查批次")
                    self._last_trend_logged = trend
                    self._last_trend_log_time = current_time
        
        for batch in self.entry_batches:
            if batch['volume'] <= 0:
                continue
            batch_price = batch['price']
            current_price = self.current_price if self.current_price is not None else 0.0
            
            # 计算批次的盈亏比例（止损）和回撤（止盈）
            profit_ratio = (current_price - batch_price) / batch_price if batch_price > 0 else 0
            batch_drawdown = (self.highest_price - current_price) / self.highest_price if self.highest_price > 0 else 0
            
            # 平仓条件
            should_close = False
            if profit_ratio <= -self.stop_loss:
                self.close_reason = f"批次止损 {profit_ratio*100:.2f}% (价格={batch_price:.4f})OldHighestPrice:{self.highest_price}"
                should_close = True
            elif batch_drawdown >= self.drawdown:
                self.close_reason = f"批次回撤 {batch_drawdown*100:.2f}% (价格={batch_price:.4f})OldHighestPrice:{self.highest_price}"
                should_close = True
            
            if should_close:
                batches_to_close.append(batch)
                # 添加日志频率控制
                if not hasattr(self, '_last_exit_log_time'):
                    self._last_exit_log_time = 0
                if current_time - self._last_exit_log_time >= log_interval:
                    print(f"【{self.contract_code}】–>触发平仓: {self.close_reason}, 当前价={current_price:.4f}, 数量={batch['volume']}")
                    self._last_exit_log_time = current_time

        if batches_to_close:
            self.batches_to_close = batches_to_close
            return True
                                            
        return False

    def enter_position(self, C):
        """执行入场操作（买入开仓），调用 execute_order"""
        try:
            if C is None:
                print(f"【{self.contract_code}】–>交易客户端未初始化，无法入场")
                return False

            if not self.update_price():
                print(f"【{self.contract_code}】–>无法获取当前行情数据")
                return False
            self.current_tick = self.C.get_full_tick([self.contract_code]).get(self.contract_code)

            if not isinstance(self.current_tick, dict):
                print(f"【{self.contract_code}】–>当前 tick 数据无效: {type(self.current_tick)}")
                return False

            # 实时检查持仓和未完成订单
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            self.position = 0
            if positions and isinstance(positions, list):
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        break

            pending_volume = 0
            if self.active_orders:
                current_time = time.time()
                for order_id, info in list(self.active_orders.items()):
                    if info['direction'] == 50:  # 开仓订单
                        pending_volume += info['volume'] - info['traded']
                        # 添加：在下单前清理超时的开仓订单
                        if current_time - info['time'] > self.order_timeout:
                            print(f"【{self.contract_code}】–>入场前清理超时开仓订单–>ID: {order_id}")
                            if self.cancel_order(self.C, order_id):
                                print(f"【{self.contract_code}】–>超时开仓订单撤单成功–>ID: {order_id}")
                                del self.active_orders[order_id]
                                pending_volume -= (info['volume'] - info['traded'])
                            else:
                                print(f"【{self.contract_code}】–>超时开仓订单撤单失败–>ID: {order_id}")

            # 移除重复的持仓检查，依赖 check_entry_conditions 的验证
            # 原代码：
            # if self.position + pending_volume >= self.max_position:
            #     if not hasattr(self, '_max_position_logged') or not self._max_position_logged:
            #         print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position} (当前: {self.position}, 未完成: {pending_volume})")
            #         self._max_position_logged = True
            #     return False

            if self.contract_code not in ContractMonitor._subscribed_contracts:
                print(f"【{self.contract_code}】–>订阅合约")
                self.C.subscribe_quote(self.contract_code, "1", "0")
                ContractMonitor._subscribed_contracts.add(self.contract_code)

            bid_price = self.current_tick.get('bidPrice', [0])[0]
            ask_price = self.current_tick.get('askPrice', [0])[0]
            ask_vol = self.current_tick.get('askVol', [0])[0]
            if not bid_price or not ask_price or not ask_vol:
                print(f"【{self.contract_code}】–>无法获取有效买卖价格或卖一量")
                return False

            available_fund = self.account_manager.account_info.get('m_dAvailable', 0)
            max_by_fund = int(available_fund / (ask_price * 10000))
            remaining_capacity = self.max_position - self.position - pending_volume
            order_volume = min(ask_vol, remaining_capacity, max_by_fund, 6)
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用入场数量 (剩余容量: {remaining_capacity})")
                return False

            order_price = round(ask_price, 4)
            print(f"【{self.contract_code}】–>触发入场（买入开仓）–>价格: {order_price:.4f}, 数量: {order_volume}")

            # 调用 execute_order 执行下单
            order_remark = execute_order(
                self.contract_code, 'BUY', order_volume, 1, order_price,
                self.account_manager.account_id, C, self.account_manager.account_info,
                is_open=True, current_tick=self.current_tick
            )

            if order_remark:
                print(f"【{self.contract_code}】–>入场订单提交成功–>备注: {order_remark}")
                self.last_order_id = order_remark
                self.last_order_check_time = time.time()
                self.active_orders[order_remark] = {
                    'time': time.time(),
                    'is_close': False,
                    'price': order_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 50,
                    'contract': self.contract_code
                }
                return True
            print(f"【{self.contract_code}】–>入场订单提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行入场操作异常: {str(e)}")
            traceback.print_exc()
            return False

    def exit_position(self, reason):
        """执行出场交易（卖出平仓），调用 execute_order"""
        if not self.check_exit_conditions():
            return False
        
        print(f"【{self.contract_code}】–>触发平仓: {reason}, 当前价: {self.current_price:.4f}")
        
        try:
            if not hasattr(self, 'C') or self.C is None:
                raise AttributeError(f"【{self.contract_code}】–>上下文对象 'self.C' 未定义或为空")
            
            # 撤销现有订单
            self.cancel_order(self.C)
            
            # 更新账户信息
            self.account_manager.update(self.C)
            contract_key = self.contract_code.replace('.SHO', '')
            position_info = self.account_manager.positions.get(contract_key)
            
            if not position_info:
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_strInstrumentID') and pos.m_strInstrumentID == contract_key:
                            position_info = {
                                'AvailableVolume': getattr(pos, 'm_nVolume', 0) - getattr(pos, 'm_nFrozenVolume', 0)
                            }
                            break
                if not position_info:
                    print(f"【{self.contract_code}】–>无持仓信息")
                    # 若无持仓，清理状态
                    self.in_position = False
                    self.position = 0
                    self.total_cost = 0
                    self.total_volume = 0
                    self.entry_price = None
                    self.price_history = []
                    self.highest_price = 0.0
                    if hasattr(self, 'batches_to_close'):
                        print(f"【{self.contract_code}】–>清理 batches_to_close")
                        delattr(self, 'batches_to_close')
                    if self.is_legacy_position:
                        self.is_legacy_position = False
                        print(f"【{self.contract_code}】–>重置遗留持仓标记")
                    return True  # 无持仓时返回成功，避免重复尝试
            
            available_volume = position_info.get('AvailableVolume', 0)
            # 修改：添加 total_volume 校验，避免误判
            total_volume = position_info.get('Volume', self.position)
            if available_volume <= 0 and total_volume > 0:
                print(f"【{self.contract_code}】–>可用持仓为0，但总持仓={total_volume}，数据异常，重新同步")
                self.account_manager.update(self.C)
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                available_volume = 0
                for pos in positions:
                    if pos.m_strInstrumentID == contract_key:
                        available_volume = getattr(pos, 'm_nVolume', 0) - getattr(pos, 'm_nFrozenVolume', 0)
                        total_volume = getattr(pos, 'm_nVolume', 0)
                        break
            
            # 修改：获取 tick 数据
            retries = 3
            bid_vol = 0
            bid_price = 0
            current_tick = None
            for attempt in range(retries):
                tick_data = self.C.get_full_tick([self.contract_code])
                if tick_data and self.contract_code in tick_data:
                    current_tick = tick_data[self.contract_code]
                    bid_vol = current_tick.get('bidVol', [0])[0]
                    bid_price = current_tick.get('bidPrice', [0])[0]
                    if bid_price > 0 and bid_vol > 0:
                        self.current_tick = current_tick
                        break
                print(f"【{self.contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                time.sleep(0.5)
            else:
                print(f"【{self.contract_code}】–>多次尝试后仍无法获取有效tick数据")
                return False

            if available_volume <= 0 or not bid_vol:
                print(f"【{self.contract_code}】–>无可用持倉或買一量 (可用: {available_volume}, 買一量: {bid_vol})")
                # 修改：不立即清理状态，仅记录问题
                return False
            
            # 灵活平仓，考虑持仓量和买一量
            if hasattr(self, 'batches_to_close') and self.batches_to_close:
                target_volume = min(sum(batch['volume'] for batch in self.batches_to_close), available_volume)
                order_volume = min(target_volume, bid_vol)
                print(f"【{self.contract_code}】–>部分平仓: 目标平仓量={target_volume}, 实际平仓量={order_volume}")
            else:
                order_volume = min(available_volume, bid_vol)
                print(f"【{self.contract_code}】–>全部平仓: 平仓量={order_volume}")
                
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用平仓数量")
                return False
            
            order_price = round(bid_price, 4)
            
            # 调用 execute_order 执行下单
            order_remark = execute_order(
                self.contract_code, 'SELL', order_volume, 1, order_price,
                self.account_manager.account_id, self.C, self.account_manager.account_info,
                is_open=False, current_tick=self.current_tick
            )

            if order_remark:
                print(f"【{self.contract_code}】–>平仓订单提交成功: 数量 {order_volume}, 价格 {order_price:.4f}, 订单备注: {order_remark}")
                self.active_orders[order_remark] = {
                    'time': time.time(),
                    'is_close': True,
                    'price': order_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 51,
                    'contract': self.contract_code
                }
                self.pending_close = True  # 修改：标记等待成交
                return True  # 修改：仅表示提交成功
            print(f"【{self.contract_code}】–>平仓订单提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行出场异常: {str(e)}")
            traceback.print_exc()
            return False

    def cancel_frozen_orders(self, C, is_open=False):
        """撤销冻结订单，支持入场和平仓"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            direction_flag = 50 if is_open else 51  # 50: 开仓, 51: 平仓
            active_states = [0, 1, 2, 3, 50, 51]  # 包括“已报待撤”(假设 2)
            canceled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus') or not hasattr(order, 'm_nOffsetFlag'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                direction = int(getattr(order, 'm_nOffsetFlag', 0))
                order_id = getattr(order, 'm_strOrderSysID', '')
                contract = getattr(order, 'm_strInstrumentID', '')
                
                if (contract == self.contract_code.replace('.SHO', '') and 
                    direction == direction_flag and status in active_states):
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    if volume_total > volume_traded:
                        result = cancel(order_id, self.account_manager.account_id, 'STOCK_OPTION', C)
                        if result:
                            print(f"【{self.contract_code}】–>撤销冻结订单成功–>ID: {order_id}, 状态: {status}")
                            canceled = True
                        else:
                            print(f"【{self.contract_code}】–>撤销冻结订单失败–>ID: {order_id}, 状态: {status}")
            
            return canceled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤销冻结订单异常: {str(e)}")
            traceback.print_exc()
            return False

    # 修改：仅在连续上涨趋势中触发入场
    def check_entry_conditions(self):
        """检查入场条件，仅依赖 calculate_trend 判断"""
        try:
            if not is_trade_time():
                print(f"【{self.contract_code}】–>非交易时间，暂不入场")
                return False
            
            # 实时同步持仓
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            self.position = 0
            if positions and isinstance(positions, list):
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        break

            # 计算未完成订单的委托量
            pending_volume = 0
            if self.active_orders:
                for order_id, info in list(self.active_orders.items()):
                    if info['direction'] == 50:  # 开仓订单（50 表示买入开仓）
                        pending_volume += info['volume'] - info['traded']
            
            # 检查持仓量 + 未成交委托是否超过最大持仓限制，只打印一次
            if self.position + pending_volume >= self.max_position:
                if not hasattr(self, '_max_position_logged') or not self._max_position_logged:
                    print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position} (当前: {self.position}, 未完成: {pending_volume})")
                    self._max_position_logged = True  # 标记为已打印
                return False

            if not self.account_manager or not self.account_manager.account_info:
                print(f"【{self.contract_code}】–>等待账户信息初始化")
                return False
                
            if len(self.price_history) < self.trend_sensitivity + self.breakout_confirm:
                return False
                
            if not self.current_price:
                return False

            # 添加频率控制，防止短时间内重复触发
            if not hasattr(self, '_last_entry_time'):
                self._last_entry_time = 0
            current_time = time.time()
            if current_time - self._last_entry_time < 1:  # 1秒内只允许触发一次
                return False

            # 日志控制（趋势非上涨）
            if not hasattr(self, '_last_entry_trend_logged'):
                self._last_entry_trend_logged = None
            if not hasattr(self, '_last_entry_log_time'):
                self._last_entry_log_time = 0
            log_interval = 60

            trend, trend_type = self.calculate_trend()
            if trend == "up":
                print(f"【{self.contract_code}】–>触发入场: 趋势 {trend}, 类型 {trend_type}")
                self._last_entry_time = current_time  # 更新最后入场时间
                return self.enter_position(self.C)
            # 删除非上涨趋势的日志输出，减少高频冗余
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>检查入场条件异常: {str(e)}")
            traceback.print_exc()
            return False

    def on_tick(self):
        try:
            if not hasattr(self, '_initialized'):
                self.price_history = []
                self.effective_ticks = []
                self._initialized = True
                print(f"【{self.contract_code}】–>初始化价格历史数据")

            current_trade_status = is_trade_time()
            if not hasattr(self, '_last_trade_status'):
                self._last_trade_status = current_trade_status
            
            if current_trade_status != self._last_trade_status:
                print(f"【{self.contract_code}】–>{'进入' if current_trade_status else '退出'}交易时间")
                self._last_trade_status = current_trade_status

            if not current_trade_status:
                return

            current_time = time.time()
            self._last_tick_time = current_time
            
            # 添加频率控制，避免高频调用
            if not hasattr(self, '_last_update_time') or current_time - self._last_update_time < 0.5:
                return
            self._last_update_time = current_time

            if not self.update_price():
                return
            
            if hasattr(self, 'account_manager'):
                self.account_manager.update(self.C)
            
            if not hasattr(self, '_last_timeout_log_time'):
                self._last_timeout_log_time = {}
            log_interval = 60

            # 修改：检查 active_orders
            if self.active_orders:
                for order_id, info in list(self.active_orders.items()):
                    if current_time - info['time'] > self.order_timeout:
                        if (order_id not in self._last_timeout_log_time or 
                            current_time - self._last_timeout_log_time.get(order_id, 0) >= log_interval):
                            print(f"【{self.contract_code}】–>检测到超时订单–>ID: {order_id}, 已等待 {current_time - info['time']:.1f}秒")
                            self._last_timeout_log_time[order_id] = current_time
                        
                        if self.cancel_order(self.C, order_id):
                            print(f"【{self.contract_code}】–>超时订单撤单成功–>ID: {order_id}")
                            del self.active_orders[order_id]
                            if info['is_close']:
                                print(f"【{self.contract_code}】–>平仓订单超时，重新提交平仓")
                                self.exit_position("超时订单触发平仓")
                        else:
                            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
                            order_found = False
                            if orders and isinstance(orders, list):
                                for order in orders:
                                    if getattr(order, 'm_strOrderSysID', '') == order_id:
                                        status = int(getattr(order, 'm_nOrderStatus', -1))
                                        if status not in [0, 1, 3, 50, 51]:
                                            print(f"【{self.contract_code}】–>订单已非活动状态–>ID: {order_id}, 状态: {status}, 移除")
                                            del self.active_orders[order_id]
                                        order_found = True
                                        break
                            if not order_found and order_id in self.active_orders:
                                print(f"【{self.contract_code}】–>订单未在最新数据中找到，移除–>ID: {order_id}")
                                del self.active_orders[order_id]
                            else:
                                print(f"【{self.contract_code}】–>超时订单撤单失败–>ID: {order_id}")
            
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            pending_orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if positions and isinstance(positions, list):
                self.position = 0
                self.in_position = False
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        self.in_position = self.position > 0
                        if self.in_position and not hasattr(self, 'total_cost') and self.is_legacy_position:
                            multiplier = 10000
                            if self.update_price():
                                self.total_cost = self.current_price * self.position * multiplier
                                self.total_volume = self.position
                                if not hasattr(self, '_entry_price_fixed') or not self._entry_price_fixed:
                                    self.entry_price = self.current_price
                                    self.original_entry_price = self.current_price
                                    self._entry_price_fixed = True
                                    print(f"【{self.contract_code}】–>首次初始化遗留持仓: 开仓价固定为 {self.entry_price:.4f}")
                                self.entry_batches = [{'price': self.entry_price, 'volume': self.position}]
                                print(f"【{self.contract_code}】–>初始化持仓: 数量={self.total_volume}, 开仓价={self.entry_price:.4f}, 来源: 实时tick")
                            else:
                                print(f"【{self.contract_code}】–>无法获取实时tick价格，持仓初始化失败")
                # 修改：若无持仓，仅在确认成交后清理状态
                if not self.in_position and not self.pending_close:
                    if hasattr(self, 'batches_to_close'):
                        print(f"【{self.contract_code}】–>无持仓，清理 batches_to_close")
                        delattr(self, 'batches_to_close')
                    self.total_cost = 0
                    self.total_volume = 0
                    self.entry_price = None
                    if self.is_legacy_position:
                        self.is_legacy_position = False
                        print(f"【{self.contract_code}】–>持仓清零，重置遗留持仓标记")

            pending_volume = 0
            if pending_orders and isinstance(pending_orders, list):
                for order in pending_orders:
                    if (hasattr(order, 'm_strInstrumentID') and 
                        order.m_strInstrumentID == self.contract_code.replace('.SHO', '') and 
                        order.m_nOrderStatus in [50, 51]):
                        pending_volume += getattr(order, 'm_nVolumeTotal', 0) - getattr(order, 'm_nVolumeTraded', 0)

            trend, trend_type = self.calculate_trend()

            if hasattr(self, 'batches_to_close') and self.batches_to_close:
                if not hasattr(self, '_batches_to_close_time'):
                    self._batches_to_close_time = current_time
                elif current_time - self._batches_to_close_time > 30:
                    print(f"【{self.contract_code}】–>batches_to_close超时未平仓，重新尝试")
                    self.exit_position(self.close_reason)
                    self._batches_to_close_time = current_time

            if self.in_position and self.check_exit_conditions():
                if self.exit_position(self.close_reason):
                    # 修改：不再立即标记 in_position 为 False，等待成交确认
                    print(f"【{self.contract_code}】–>平仓订单已提交，等待成交")

            if not hasattr(self, '_last_position_log_time'):
                self._last_position_log_time = 0
            if not hasattr(self, '_last_pending_log_time'):
                self._last_pending_log_time = 0
            log_interval = 60

            if self.position + pending_volume < self.max_position:
                self.check_entry_conditions()
            else:
                if pending_volume > 0 and current_time - self._last_pending_log_time >= log_interval:
                    print(f"【{self.contract_code}】–>存在未完成订单，持仓量: {self.position}, 未完成: {pending_volume}")
                    self._last_pending_log_time = current_time
                elif self.position >= self.max_position:
                    if current_time - self._last_position_log_time >= log_interval:
                        print(f"【{self.contract_code}】–>持仓已满: {self.position}/{self.max_position}，继续监测入场条件")
                        self._last_position_log_time = current_time
                self.check_entry_conditions()
        except Exception as e:
            print(f"【{self.contract_code}】–>on_tick异常: {str(e)}")
            traceback.print_exc()

    def update_entry_price(self, price, volume):
        if not hasattr(self, 'total_cost'):
            self.total_cost = 0
            self.total_volume = 0
            self.entry_batches = []
        multiplier = 10000
        if self.is_legacy_position and hasattr(self, '_entry_price_fixed') and self._entry_price_fixed:
            self.total_cost += price * volume * multiplier
            self.total_volume += volume
            self.entry_batches.append({'price': price, 'volume': volume})
            print(f"【{self.contract_code}】–>遗留持仓加仓: 新批次价格={price:.4f}, 数量={volume}, 原开仓价保持: {self.entry_price:.4f}")
        else:
            self.total_cost += price * volume * multiplier
            self.total_volume += volume
            self.entry_price = self.total_cost / (self.total_volume * multiplier) if self.total_volume > 0 else 0
            self.entry_batches.append({'price': price, 'volume': volume})
            # 仅在首次初始化时打印 entry_price
            if not hasattr(self, '_entry_price_logged') or not self._entry_price_logged:
                print(f"【{self.contract_code}】–>首次记录入场: 价格={price:.4f}, 数量={volume}, 初始平均开仓价={self.entry_price:.4f}")
                self._entry_price_logged = True
            else:
                print(f"【{self.contract_code}】–>记录入场批次: 价格={price:.4f}, 数量={volume}")

    def on_order_filled(self, price, volume, direction):
        if direction == 50:  # 开仓（买入）
            self.update_entry_price(price, volume)
            print(f"【{self.contract_code}】–>开仓成交: 价格={price:.4f}, 数量={volume}")
        elif direction == 51:  # 平仓（卖出）
            if hasattr(self, 'batches_to_close') and self.batches_to_close:
                remaining_volume = volume
                for batch in self.batches_to_close:
                    if remaining_volume <= 0:
                        break
                    if batch['volume'] > 0:
                        reduce_volume = min(remaining_volume, batch['volume'])
                        batch['volume'] -= reduce_volume
                        remaining_volume -= reduce_volume
                        for eb in self.entry_batches:
                            if eb['price'] == batch['price'] and eb['volume'] >= reduce_volume:
                                eb['volume'] -= reduce_volume
                                break
                        print(f"【{self.contract_code}】–>平仓批次更新: 价格={batch['price']:.4f}, 减少数量={reduce_volume}, 剩余={batch['volume']}")
            self.total_volume -= volume
            self.position -= volume  # 修改：同步 position
            self.in_position = self.position > 0  # 修改：更新 in_position
            multiplier = 10000
            self.total_cost = self.entry_price * self.total_volume * multiplier if self.total_volume > 0 else 0
            print(f"【{self.contract_code}】–>平仓成交: 价格={price:.4f}, 数量={volume}, 剩余持仓={self.total_volume}")

# 5. 主要策略函数
def call_back(data):
    """主策略回调函数"""
    try:
        # 处理订阅成功的初始事件（可选保留，仅打印日志）
        if isinstance(data, dict) and 'EventCode' in data and data['EventCode'] == 0:
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
            C = call_back.context
            print(f"订阅标的行情: {strategy_state.undl_code}")
            # 删除 select_options 和后续逻辑，因为已在 after_init 中实现
            return

        # 处理实时行情数据
        if isinstance(data, dict):
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
            C = call_back.context
            for code, tick_data in data.items():
                if code in C.monitors:
                    monitor = C.monitors[code]
                    monitor.on_tick()

    except Exception as e:
        print(f"策略异常: {str(e)}")
        traceback.print_exc()

def init(C):
    """初始化策略"""
    global strategy_state
    
    strategy_state = StrategyState(C)
    
    strategy_state.subID = 0
    strategy_state.undl_code = '510300.SH'
    strategy_state.monitors = {}
    C.monitors = strategy_state.monitors
    
    print(f"订阅标的行情: {strategy_state.undl_code}")
    C.subscribe_quote(strategy_state.undl_code, "1", "0")
    
    account_id = '*********'
    strategy_state.account_manager = AccountManager(account_id)
    
    C.set_account(account_id)
    
    call_back.context = C

def after_init(C):
    """初始化后执行的操作"""
    try:
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
            positions = get_trade_detail_data(strategy_state.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            position_contracts = set()
            
            if positions and isinstance(positions, list):
                for pos in positions:
                    if hasattr(pos, 'm_strInstrumentID'):
                        contract_code = pos.m_strInstrumentID + '.SHO'  # 统一格式
                        pos_volume = getattr(pos, 'm_nVolume', 0)
                        if pos_volume > 0:
                            position_contracts.add(contract_code)
                            if contract_code not in strategy_state.monitors:
                                monitor = ContractMonitor(C, contract_code, strategy_state.account_manager)
                                strategy_state.monitors[contract_code] = monitor
                                entry_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                                monitor.position = pos_volume
                                monitor.in_position = True
                                monitor.is_legacy_position = True
                                monitor.load_parameters()
                                # 延迟初始化，直到获取实时价格成功
                                retries = 5
                                for attempt in range(retries):
                                    if monitor.update_price():
                                        monitor.init_position(contract_code, pos_volume, entry_price)
                                        print(f"【{contract_code}】–>初始化已有持仓–>持仓量: {pos_volume}, 开仓价: {monitor.entry_price:.4f}, 来源: 实时tick")
                                        break
                                    else:
                                        print(f"【{contract_code}】–>等待实时价格，尝试 {attempt + 1}/{retries}")
                                        time.sleep(1)
                                else:
                                    print(f"【{contract_code}】–>无法获取实时价格，初始化失败")
                                    return
        
        tick_data = C.get_full_tick([strategy_state.undl_code])
        if not tick_data or strategy_state.undl_code not in tick_data:
            print("无法获取市场数据")
            return
            
        data = tick_data[strategy_state.undl_code]
        if not isinstance(data, dict):
            print("市场数据格式错误")
            return
            
        timetag = data.get('timetag', '')
        current_date = timetag.split()[0].replace('-', '') if timetag else ''
        target_price = data.get('lastPrice', 0)
        if target_price <= 0:
            target_price = data.get('lastClose', 0)
            if target_price > 0:
                print("使用昨收盘价")
            else:
                print("无法获取有效价格")
                return
                
        if not current_date:
            print("无法获取有效日期")
            return
            
        print(f"\n当前日期: {current_date}")
        print(f"标的{strategy_state.undl_code}当前价格: {target_price}")
        
        all_options = C.get_option_list(strategy_state.undl_code, current_date, "", True)
        if not all_options:
            print("没有可用期权")
            return
            
        expiry_groups = {}
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if not detail:
                continue
            expiry = detail['ExpireDate']
            if expiry not in expiry_groups:
                expiry_groups[expiry] = []
            expiry_groups[expiry].append((opt, detail))
        
        if not expiry_groups:
            print("无法获取期权详情")
            return
            
        nearest_expiry = min(expiry_groups.keys())
        print(f"\n选择到期日: {nearest_expiry}")
        
        selected_options = []
        min_call_diff = float('inf')
        min_put_diff = float('inf')
        closest_call = None
        closest_put = None
        
        for opt, detail in expiry_groups[nearest_expiry]:
            strike = float(detail['OptExercisePrice'])
            if detail['optType'] == 'CALL' and strike > target_price:
                diff = abs(strike - target_price)
                if diff < min_call_diff:
                    min_call_diff = diff
                    closest_call = (opt, detail)
            elif detail['optType'] == 'PUT' and strike < target_price:
                diff = abs(strike - target_price)
                if diff < min_put_diff:
                    min_put_diff = diff
                    closest_put = (opt, detail)
        
        if closest_call:
            opt, detail = closest_call
            # 修改：添加流动性检查
            tick_data = C.get_full_tick([opt])
            if tick_data and opt in tick_data and tick_data[opt].get('volume', 0) > 0:
                if opt not in selected_options:
                    selected_options.append(opt)
                    print(f"\n选中看涨期权: {opt}")
                    print(f"到期日: {detail['ExpireDate']}")
                    print(f"行权价: {detail['OptExercisePrice']}")
                    print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            else:
                print(f"【{opt}】–>流动性不足，跳过")
        if closest_put:
            opt, detail = closest_put
            # 修改：添加流动性检查
            tick_data = C.get_full_tick([opt])
            if tick_data and opt in tick_data and tick_data[opt].get('volume', 0) > 0:
                if opt not in selected_options:
                    selected_options.append(opt)
                    print(f"\n选中看跌期权: {opt}")
                    print(f"到期日: {detail['ExpireDate']}")
                    print(f"行权价: {detail['OptExercisePrice']}")
                    print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            else:
                print(f"【{opt}】–>流动性不足，跳过")
            
        if selected_options:
            print(f"\n订阅期权合约: {selected_options}")
            for opt in selected_options:
                if opt not in strategy_state.monitors:
                    monitor = ContractMonitor(C, opt, strategy_state.account_manager)
                    strategy_state.monitors[opt] = monitor
                    monitor.load_parameters()
                    print(f"【{opt}】–>初始化策略合约")
                    monitor.update_price()
            
            active_contracts = list(set(selected_options) | position_contracts)
            if active_contracts:
                # 修改：绑定 call_back 回调
                strategy_state.subID = C.subscribe_whole_quote(active_contracts, callback=call_back)
                if strategy_state.subID > 0:
                    print(f"行情订阅成功，订阅号: {strategy_state.subID}")
                    # 等待订阅生效并验证实时数据
                    time.sleep(2)
                    for contract in active_contracts:
                        if contract in strategy_state.monitors:
                            monitor = strategy_state.monitors[contract]
                            if not monitor.update_price():
                                print(f"【{contract}】–>订阅后仍无法获取实时tick数据")
                else:
                    print("行情订阅失败")
                    return
        else:
            print("未找到合适期权合约")
            
    except Exception as e:
        print(f"初始化异常: {str(e)}")
        traceback.print_exc()
        strategy_state.subID = 0

def handlebar(C):
    """K线数据更新时执行"""
    try:
        if not hasattr(C, 'monitors'):
            return
            
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
        for code, monitor in C.monitors.items():
            try:
                monitor.on_tick()
            except Exception as e:
                print(f"处理合约 {code} 异常: {str(e)}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"handlebar异常: {str(e)}")
        traceback.print_exc()

def is_trade_time():
    """检查是否在交易时段"""
    current_time = time.localtime()
    hour = current_time.tm_hour
    minute = current_time.tm_min
    
    if current_time.tm_wday >= 5:
        return False
        
    if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
        return True
        
    if (hour >= 13 and hour < 15) or (hour == 15 and minute == 0):
        return True
        
    return False

def execute_order(contract_code, direction, volume, price_type, price, account_id, C, account_info, is_open=True, current_tick=None):
    """执行订单"""
    try:
        # 修改：检查 C 是否有效
        if C is None:
            print("交易上下文无效")
            return None
            
        if not is_trade_time():
            print("当前不在交易时段,不能下单")
            return None
            
        if not account_info:
            print("账户信息无效")
            return None
            
        if is_open:
            try:
                total_position = 0
                positions = get_trade_detail_data(account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_nVolume'):
                            total_position += getattr(pos, 'm_nVolume', 0)
                
                pending_volume = 0
                has_pending_orders = False
                orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
                if orders and isinstance(orders, list):
                    for order in orders:
                        if (hasattr(order, 'm_nOffsetFlag') and 
                            getattr(order, 'm_nOffsetFlag') == 48):
                            status = getattr(order, 'm_nOrderStatus', -1)
                            if status in [0, 1, 3]:
                                if (hasattr(order, 'm_strInstrumentID') and 
                                    order.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                    pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                    getattr(order, 'm_nVolumeTraded', 0))
                                    has_pending_orders = True
                                else:
                                    pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                    getattr(order, 'm_nVolumeTraded', 0))
                                    has_pending_orders = True
                
                total_exposure = total_position + pending_volume + volume
                max_position = account_info.get('max_position', 30)  # 默认值 30
                
                if total_exposure > max_position:
                    print(f"\n>>> 持仓限制检查 <<<")
                    print(f"当前持仓: {total_position}")
                    print(f"未完成开仓: {pending_volume}")
                    print(f"本次委托: {volume}")
                    print(f"总暴露: {total_exposure}")
                    print(f"最大持仓限制: {max_position}")
                    print("超过最大持仓限制，取消委托")
                    return None
                    
                if has_pending_orders and pending_volume > 0:
                    print(f"存在未完成的开仓委托（未完成量: {pending_volume}），等待处理完成后再开新仓")
                    return None
                    
            except Exception as e:
                print(f"检查持仓限制异常: {str(e)}")
                return None
                
        if not is_open:
            # 平仓时，使用传入的 current_tick 数据
            if current_tick:
                bid_vol = current_tick.get('bidVol', [0])[0]
                print(f"【{contract_code}】–>使用传入tick数据，买一量: {bid_vol}")
                if bid_vol < volume:
                    print(f"市场可成交量不足: 需要{volume}, 可成交量{bid_vol}")
                    return None
            else:
                # 若无传入数据，重试获取
                retries = 3
                for attempt in range(retries):
                    tick_data = C.get_full_tick([f"{contract_code}.SHO"])
                    if tick_data and f"{contract_code}.SHO" in tick_data:
                        data = tick_data[f"{contract_code}.SHO"]
                        bid_vol = data.get('bidVol', [0])[0]
                        print(f"【{contract_code}】–>重试获取tick数据，买一量: {bid_vol}")
                        if bid_vol >= volume:
                            break
                    print(f"【{contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                else:
                    print(f"【{contract_code}】–>无法获取市场报价，假设市场无成交量")
                    return None
            
        # 获取价格，优先使用传入的 tick 数据
        if current_tick and direction == 'SELL':
            exec_price = current_tick.get('bidPrice', [0])[0] if current_tick else price
            print(f"【{contract_code}】–>使用传入tick价格: {exec_price}")
        else:
            subscribe_code = f"{contract_code}.SHO"
            tick_data = C.get_full_tick([subscribe_code])
            if tick_data and subscribe_code in tick_data:
                data = tick_data[subscribe_code]
                if isinstance(data, dict):
                    bid_price = data.get('bidPrice', [0])[0]
                    ask_price = data.get('askPrice', [0])[0]
                    if bid_price > 0 and ask_price > 0:
                        print(f"获取到有效行情 - 买价: {bid_price}, 卖价: {ask_price}")
                        exec_price = bid_price if direction == 'SELL' else ask_price
                    else:
                        print("行情价格无效，使用委托价格")
                        exec_price = price
                else:
                    print("行情数据格式错误，使用委托价格")
                    exec_price = price
            else:
                print(f"无法获取{contract_code}市场报价，使用委托价格")
                exec_price = price
            
        if direction == 'BUY':
            if not is_open:
                print("不支持买入平仓操作")
                return None
            op_type = 50
        else:
            if is_open:
                print("不支持卖出开仓操作")
                return None
            op_type = 51
            
        print("\n>>> 委托参数 <<<")
        print(f"合约代码: {contract_code}")
        print(f"交易方向: {direction}")
        print(f"开平标志: {'开仓' if is_open else '平仓'}")
        print(f"委托数量: {volume}")
        print(f"价格类型: {price_type}")
        print(f"委托价格: {exec_price}")
        
        print(f"\n>>> 开始执行委托 - {'开仓' if is_open else '平仓'} <<<")
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        random_num = random.randint(1000, 9999)
        order_remark = f"{contract_code}_{direction}_{timestamp}_{random_num}"
        
        exec_price = round(exec_price, 4)
        result = passorder(
            op_type,
            1101,
            account_id,
            contract_code,
            price_type,
            exec_price,
            volume,
            '期权交易',
            2,
            order_remark,
            C
        )
        
        if result is not None:
            print(f"\n>>> 委托已发送 - 投资备注: {order_remark} <<<")
            return order_remark
        else:
            print("委托发送失败，返回值为None")
            return None
            
    except Exception as e:
        print(f"订单执行异常: {str(e)}")
        traceback.print_exc()
        return None

# 3. 账户管理类
class AccountManager:
    def __init__(self, account_id):
        """账户管理器初始化"""
        self.account_id = account_id
        self.account_info = {
            'm_strAccountID': account_id,
            'm_dAvailable': 0.0,
            'm_dBalance': 0.0,
            'm_dInstrumentValue': 0.0
        }
        self.orders = {}
        self.positions = {}
        self.last_update_time = 0
        self.update_interval = 1
        self.enable_logging = False  # 新增：日志开关，默认关闭
        print(f"\n>>> 账户管理器初始化 - {account_id} <<<")

    def update(self, C):
        """
        更新账户信息和持仓状态
        """
        try:
            current_time = time.time()
            self.last_update_time = current_time
            
            # 添加日志频率控制
            if not hasattr(self, '_last_sync_log_time'):
                self._last_sync_log_time = {}
            log_interval = 60  # 每 60 秒打印一次同步日志
            
            orders = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ORDER')
            if orders:
                new_orders = {}
                for order in orders:
                    order_id = getattr(order, 'm_strOrderSysID', '')
                    if not order_id:
                        continue
                                    
                    status = int(getattr(order, 'm_nOrderStatus', -1))
                    contract = getattr(order, 'm_strInstrumentID', '')
                    direction = int(getattr(order, 'm_nOffsetFlag', 0))
                    
                    if direction not in [50, 51]:
                        continue
                                    
                    if status in [0, 1]:
                        new_orders[order_id] = {
                            'contract': contract,
                            'direction': direction,
                            'price': float(getattr(order, 'm_dLimitPrice', 0.0)),
                            'volume': int(getattr(order, 'm_nVolumeTotalOriginal', 0)),
                            'traded': int(getattr(order, 'm_nVolumeTraded', 0)),
                            'status': status,
                            'submit_time': getattr(order, 'm_strInsertTime', '')
                        }
                                    
                        if order_id not in self.orders:
                            if hasattr(C, 'monitors'):
                                monitor = C.monitors.get(f"{contract}.SHO")
                                if monitor:
                                    monitor.active_orders[order_id] = {
                                        'time': time.time(),
                                        'is_close': direction == 51,
                                        'price': new_orders[order_id]['price'],
                                        'volume': new_orders[order_id]['volume'],
                                        'traded': new_orders[order_id]['traded'],
                                        'direction': direction
                                    }
                        elif hasattr(C, 'monitors'):
                            monitor = C.monitors.get(f"{contract}.SHO")
                            if monitor and order_id in monitor.active_orders:
                                monitor.active_orders[order_id].update({
                                    'traded': new_orders[order_id]['traded']
                                })
                        
                    self.orders = new_orders
                            
                    if hasattr(C, 'monitors'):
                        for monitor in C.monitors.values():
                            to_delete = [order_id for order_id in monitor.active_orders 
                                        if order_id not in new_orders]
                            for order_id in to_delete:
                                del monitor.active_orders[order_id]
                
            account_info = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ACCOUNT')
            if account_info and len(account_info) > 0:
                account_obj = account_info[0]
                new_account_info = {
                    'm_strAccountID': self.account_id,
                    'm_dAvailable': float(getattr(account_obj, 'm_dAvailable', 0.0)),
                    'm_dBalance': float(getattr(account_obj, 'm_dBalance', 0.0)),
                    'm_dInstrumentValue': float(getattr(account_obj, 'm_dInstrumentValue', 0.0))
                }
                            
                if self._has_account_changes(new_account_info):
                    self.account_info = new_account_info
                    self._log_account_changes(new_account_info)
                
            positions = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'POSITION')
            if positions:
                new_positions = {}
                for pos in positions:
                    contract = getattr(pos, 'm_strInstrumentID', '')
                    volume = getattr(pos, 'm_nVolume', 0)
                    frozen_volume = getattr(pos, 'm_nFrozenVolume', 0)
                    if volume > 0:
                        open_price = float(getattr(pos, 'm_dOpenPrice', 0.0))
                        new_positions[contract] = {
                            'Volume': volume,
                            'FrozenVolume': frozen_volume,
                            'AvailableVolume': volume - frozen_volume,
                            'OpenPrice': open_price,
                            'PositionValue': volume * open_price * 10000,
                            'UnrealizedPL': float(getattr(pos, 'm_dPositionProfit', 0.0))
                        }
                        if contract in self.positions and 'CustomOpenPrice' in self.positions[contract]:
                            new_positions[contract]['CustomOpenPrice'] = self.positions[contract]['CustomOpenPrice']
                                            
                        if hasattr(C, 'monitors'):
                            monitor = C.monitors.get(f"{contract}.SHO")
                            if monitor and monitor.contract_code == f"{contract}.SHO":
                                monitor.position = volume
                                monitor.in_position = volume > 0
                                if volume > 0:
                                    # 添加首次同步标记和日志控制
                                    if not hasattr(monitor, '_entry_price_synced'):
                                        monitor._entry_price_synced = False
                                    old_entry_price = monitor.entry_price
                                    old_original_entry_price = monitor.original_entry_price
                                    
                                    if monitor.is_legacy_position and not monitor.entry_price:
                                        monitor.entry_price = new_positions[contract].get('CustomOpenPrice', monitor.current_price if monitor.update_price() else open_price)
                                        monitor._entry_price_synced = True
                                    elif not monitor.is_legacy_position and not monitor.entry_price:
                                        monitor.entry_price = new_positions[contract]['OpenPrice']
                                        monitor.entry_time = time.strftime("%Y%m%d%H%M%S")
                                    if not monitor.original_entry_price and not monitor.is_legacy_position:
                                        if not monitor.update_price():
                                            monitor.current_price = open_price
                                            print(f"【{contract}.SHO】–>无法获取最新价，使用平均开仓价: {open_price:.4f}")
                                        monitor.original_entry_price = monitor.current_price
                                        monitor._entry_price_synced = True
                                    
                                    # 日志频率控制，仅在首次同步或价格变化时打印
                                    if (not monitor._entry_price_synced or 
                                        old_entry_price != monitor.entry_price or 
                                        old_original_entry_price != monitor.original_entry_price):
                                        if (contract not in self._last_sync_log_time or 
                                            current_time - self._last_sync_log_time.get(contract, 0) >= log_interval):
                                            print(f"【{contract}.SHO】–>同步 entry_price: {monitor.entry_price:.4f}, 原始开仓价: {monitor.original_entry_price:.4f}")
                                            self._last_sync_log_time[contract] = current_time
                                    monitor._entry_price_synced = True
                            
                if self._has_position_changes(new_positions):
                    self._log_position_changes(new_positions)
                    self.positions = new_positions
                                        
        except Exception as e:
            print(f"更新账户信息异常: {str(e)}")
            traceback.print_exc()

    def _has_account_changes(self, new_account_info):
        """检查账户信息是否有变化"""
        if not self.account_info:
            return True
            
        thresholds = {
            'm_dAvailable': 0.01,
            'm_dBalance': 0.01,
            'm_dInstrumentValue': 0.01,
        }
        
        for key, threshold in thresholds.items():
            old_value = self.account_info.get(key, 0)
            new_value = new_account_info[key]
            if abs(new_value - old_value) >= threshold:
                return True
                
        if new_account_info.get('m_strStatus') != self.account_info.get('m_strStatus'):
            return True
            
        return False
        
    def _log_account_changes(self, new_account_info):
        """记录账户变化"""
        if self.enable_logging:  # 新增：检查日志开关
            print("\n>>> 账户状态 <<<")
            print(f"账户ID: {new_account_info['m_strAccountID']}")
            print(f"可用资金: {new_account_info['m_dAvailable']:.2f}")
            print(f"总资产: {new_account_info['m_dBalance']:.2f}")
            print(f"持仓市值: {new_account_info['m_dInstrumentValue']:.2f}")
            print("")
        
    def _has_position_changes(self, new_positions):
        """检查持仓是否有显著变化"""
        if not self.positions:
            return bool(new_positions)
            
        if set(self.positions.keys()) != set(new_positions.keys()):
            return True
            
        for contract in self.positions:
            if contract not in new_positions:
                continue
                
            old_pos = self.positions[contract]
            new_pos = new_positions[contract]
            
            if old_pos['Volume'] != new_pos['Volume']:
                return True
                
            old_pnl_ratio = (old_pos['UnrealizedPL'] / old_pos['PositionValue'] * 100 
                        if old_pos['PositionValue'] > 0 else 0)
            new_pnl_ratio = (new_pos['UnrealizedPL'] / new_pos['PositionValue'] * 100 
                        if new_pos['PositionValue'] > 0 else 0)
            if abs(new_pnl_ratio - old_pnl_ratio) > 0.1:
                return True
                
        return False
        
    def _log_position_changes(self, new_positions):
        """记录持仓变化"""
        if not new_positions:
            return
            
        if self.enable_logging:  # 新增：检查日志开关
            print("\n>>> 持仓状态 <<<")
            for contract_id, pos in new_positions.items():
                volume = pos.get('Volume', 0)
                open_price = pos.get('OpenPrice', 0.0)
                unrealized_pl = pos.get('UnrealizedPL', 0.0)
                
                market_value = volume * open_price * 10000
                if market_value > 0:
                    pl_ratio = unrealized_pl / market_value if market_value != 0 else 0
                    print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}, 盈亏比例: {pl_ratio*100:.2f}%")
                else:
                    print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}")
                    if 'OpenTime' in pos:
                        print(f"开仓时间: {pos['OpenTime']}")
                    if 'Direction' in pos:
                        print(f"方向: {'多头' if pos['Direction'] == 1 else '空头'}")
                    if 'MarketValue' in pos:
                        print(f"市值: {pos['MarketValue']:.2f}")
                    print("")