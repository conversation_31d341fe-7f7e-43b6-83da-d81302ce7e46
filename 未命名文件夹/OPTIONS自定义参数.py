# coding: gbk
import time
from datetime import datetime
import xml.etree.ElementTree as ET
import os
import pandas as pd
import random
import traceback

# 全局订单跟踪字典
order_tracker = {
    'orders': {},
    'positions': {}
}

# 1. 策略状态类
class StrategyState:
    def __init__(self, C):
        """策略状态初始化"""
        self.C = C  # 存储C
        self.subID = 0
        self.undl_code = '510300.SH'  # 标的代码
        self.monitors = {}
        self.account_manager = None
        self.last_check_time = 0

# 全局策略状态实例
strategy_state = None

# 工具函数
def show_data(data):
    """
    收集对象的关键数据
    @param data: 数据对象
    @return: dict 包含关键字段的字典
    """
    key_fields = {
        'm_strAccountID': '账户ID',
        'm_dAvailable': '可用资金',
        'm_dBalance': '账户余额',
        'm_dInstrumentValue': '持仓市值',
        'm_nOrderStatus': '订单状态',
        'm_strInstrumentID': '合约代码',
        'm_strInstrumentName': '合约名称',
        'm_dLimitPrice': '委托价格',
        'm_nVolumeTotal': '委托数量',
        'm_strOrderSysID': '系统委托号',
        'm_strErrorMsg': '错误信息'
    }
    
    result = {}
    for field, _ in key_fields.items():
        try:
            if hasattr(data, field):
                result[field] = getattr(data, field)
            else:
                result[field] = '<未知>'
        except Exception:
            result[field] = '<未知>'
    return result

# 2. 回调函数组
def account_callback(ContextInfo, accountInfo):
    """账户状态变化回调"""
    if not hasattr(ContextInfo, 'account_manager'):
        return
        
    try:
        # 让AccountManager处理更新
        ContextInfo.account_manager.update(ContextInfo)
    except Exception as e:
        print(f"账户回调处理异常: {str(e)}")

def order_callback(ContextInfo, orderInfo):
    """订单状态变化回调"""
    try:
        if hasattr(ContextInfo, 'monitors'):
            contract_code = getattr(orderInfo, 'm_strInstrumentID', '')
            if contract_code in ContextInfo.monitors:
                monitor = ContextInfo.monitors[contract_code]
                monitor.update_order_status(orderInfo)
    except Exception as e:
        print(f"订单回调处理异常: {str(e)}")

def deal_callback(ContextInfo, dealInfo):
    """成交状态变化回调"""
    try:
        # 获取成交信息
        trade_id = getattr(dealInfo, 'm_strTradeID', '')
        order_id = getattr(dealInfo, 'm_strOrderSysID', '')
        contract = getattr(dealInfo, 'm_strInstrumentID', '')
        direction = '买入' if getattr(dealInfo, 'm_nOffsetFlag', 0) == 50 else '卖出'
        volume = getattr(dealInfo, 'm_nVolume', 0)
        price = getattr(dealInfo, 'm_dPrice', 0.0)
        amount = getattr(dealInfo, 'm_dTradeAmount', 0.0)
        trade_time = getattr(dealInfo, 'm_strTradeTime', '')
        commission = getattr(dealInfo, 'm_dCommission', 0.0)
        
        print(f"\n>>> 新成交回报 <<<")
        print(f"成交编号: {trade_id}")
        print(f"委托号: {order_id}")
        print(f"合约: {contract}")
        print(f"方向: {direction}")
        print(f"成交数量: {volume}")
        print(f"成交价格: {price:.4f}")
        print(f"成交金额: {amount:.2f}")
        print(f"成交时间: {trade_time}")
        print(f"手续费: {commission:.2f}")
        
        # 更新持仓信息
        if hasattr(ContextInfo, 'account_manager'):
            ContextInfo.account_manager.update(ContextInfo)
            
    except Exception as e:
        print(f"成交回调处理异常: {str(e)}")
        print("调试信息 - 原始对象属性:")
        for attr in dir(dealInfo):
            if not attr.startswith('__'):
                try:
                    value = getattr(dealInfo, attr)
                    print(f"{attr}: {value}")
                except:
                    print(f"{attr}: <error>")

def orderError_callback(ContextInfo, orderArgs, errMsg):
    """委托异常回调"""
    try:
        print(f"委托错误: {errMsg}")
    except Exception as e:
        print(f"订单错误回调异常: {str(e)}")

def check_pending_orders(account_id, contract_code):
    """检查是否有未完成的委托"""
    try:
        orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
        if orders and isinstance(orders, list):
            for order in orders:
                if (hasattr(order, 'm_strInstrumentID') and 
                    order.m_strInstrumentID == contract_code):
                    status = getattr(order, 'm_nOrderStatus', -1)
                    if status in [0, 1]:  # 未成交或部分成交
                        return True
        return False
    except Exception as e:
        print(f"检查未完成委托异常: {str(e)}")
        return True  # 发生异常时保守处理，返回True

def get_order_status(self, order_id):
    """
    获取订单状态
    @param order_id: 订单编号
    @return: dict 订单状态信息
    """
    try:
        if not order_id:
            return None
            
        # 直接从active_orders获取信息
        if order_id in self.active_orders:
            order_info = self.active_orders[order_id]
            return {
                'order_id': order_id,
                'status': order_info['status'],
                'filled_volume': order_info['traded'],
                'remaining_volume': order_info['volume'] - order_info['traded'],
                'price': order_info['price'],
                'direction': 'BUY' if order_info['direction'] == 50 else 'SELL',
                'contract': order_info['contract']
            }
                    
        print(f"未找到订单: {order_id}")
        return None
                
    except Exception as e:
        print(f"获取订单状态异常: {str(e)}")
        return None
        
def get_position_volume(self, contract_code):
    """获取合约持仓数量"""
    if contract_code in self.positions:
        return getattr(self.positions[contract_code], 'm_nVolume', 0)
    return 0
        
def get_available_volume(self, contract_code):
    """获取合约可用数量"""
    if contract_code in self.positions:
        return getattr(self.positions[contract_code], 'm_nCanUseVolume', 0)
    return 0

# 4. 合约监控类（修改部分）
class ContractMonitor:
    def __init__(self, C, contract_code, account_manager, max_position=30):
        """初始化合约监控器"""
        # 基础参数
        self.C = C
        self.contract_code = contract_code if contract_code.endswith('.SHO') else f"{contract_code}.SHO"
        self.account_manager = account_manager
        self.max_position = max_position
        
        # 新参数
        self.stop_loss = 0.03  # 止损比例
        self.drawdown = 0.03   # 回撤比例
        self.trend_sensitivity = 3  # 连续趋势判断数
        self.breakout_confirm = 1   # 突破确认tick数
        self.reversal_threshold = 0.05  # 反转阈值
        self.monitor_periods = 3    # 震荡模式监测周期数
        self.commission_per_lot = 1.7  # 每张合约手续费
        self.trend_stop_enabled = 1    # 趋势止损开关
        
        # 价格相关
        self.current_price = 0.0
        self.current_tick = None
        self.price_history = []
        self.initial_price = None
        self.daily_high = None
        self.daily_low = None
        self._price_initialized = False
        self.price_trend_points = []
        self.last_price_direction = None
        self.trend_direction = None
        
        # 持仓相关
        self.position = 0
        self.entry_price = 0.0
        self.entry_time = None
        self.in_position = False
        self.is_legacy_position = False
        self.highest_price = None
        self.lowest_price = None
        self.highest_since_entry = None
        self.lowest_since_entry = None
        
        # 订单相关
        self.active_orders = {}
        self.filled_orders = {}
        self.last_order_id = None
        self.last_order_check_time = time.time()
        self.order_timeout = 10
        
        # 交易状态
        self.pending_close = False
        self.close_reason = None
        self.trade_history = []
        
        # 趋势和动量指标（保留但不直接使用）
        self.trend = 0.0
        self.momentum = []
        self.volatility = 0.0
        
        # 添加已订阅合约集合（类变量）
        if not hasattr(ContractMonitor, '_subscribed_contracts'):
            ContractMonitor._subscribed_contracts = set()

    def calculate_trend(self, prices):
        """计算趋势指标（保留原始方法但不直接使用）"""
        if len(prices) < 2:
            return 0.0
        price_change = (prices[-1] - prices[0]) / prices[0]
        return price_change

    def calculate_momentum(self, prices):
        """计算动量指标（保留原始方法但不直接使用）"""
        if len(prices) < 2:
            return []
        momentum = [(prices[i] - prices[i-1]) / prices[i-1] 
                   for i in range(1, len(prices))]
        return momentum

    def calculate_volatility(self, prices):
        """计算波动率（保留原始方法但不直接使用）"""
        if len(prices) < 2:
            return 0.0
        price_changes = [(prices[i] - prices[i-1]) / prices[i-1] 
                        for i in range(1, len(prices))]
        if not price_changes:
            return 0.0
        mean = sum(price_changes) / len(price_changes)
        variance = sum((x - mean) ** 2 for x in price_changes) / len(price_changes)
        return (variance ** 0.5)

    def update_indicators(self):
        """更新指标（保留原始方法但不直接使用）"""
        if len(self.price_history) < 2:
            return
        prices = [p['price'] for p in self.price_history]
        self.trend = self.calculate_trend(prices)
        self.momentum = self.calculate_momentum(prices)
        self.volatility = self.calculate_volatility(prices)

    # 修改：优化趋势计算逻辑与日志输出
    def calculate_trend(self):
        """计算趋势，分为连续趋势和震荡模式，仅记录关键变动"""
        if len(self.price_history) < self.trend_sensitivity:
            return None, "价格历史不足"

        prices = [p['price'] for p in self.price_history]
        times = [p['time'] for p in self.price_history]

        def get_valid_changes(price_list):
            changes = []
            for i in range(1, len(price_list)):
                if price_list[i] != price_list[i-1]:
                    change = '上涨' if price_list[i] > price_list[i-1] else '下跌'
                    changes.append(change)
            return changes

        # 1. 连续趋势监测
        required_ticks = self.trend_sensitivity + self.breakout_confirm
        recent_prices = prices[-required_ticks-1:]
        changes = get_valid_changes(recent_prices)
        trend_log = f"【{self.contract_code}】–>tick连续监测中"
        for i, change in enumerate(changes, 1):
            trend_log += f"–>{change}{i}"
        print(trend_log)

        if len(changes) >= required_ticks:
            all_positive = all(c == '上涨' for c in changes[-required_ticks:])
            all_negative = all(c == '下跌' for c in changes[-required_ticks:])
            if all_positive:
                return "up", "continuous"
            if all_negative:
                return "down", "continuous"

        # 2. 震荡模式监测
        ticks_per_period = self.trend_sensitivity
        total_ticks_needed = ticks_per_period * self.monitor_periods
        if len(prices) < total_ticks_needed:
            return None, "震荡模式tick不足"

        period_trends = []
        current_idx = -1
        oscillation_log = f"【{self.contract_code}】–>tick震荡监测中"
        for period in range(self.monitor_periods):
            period_prices = []
            valid_ticks = 0
            while valid_ticks < ticks_per_period and current_idx > -len(prices):
                current_idx -= 1
                period_prices.insert(0, prices[current_idx])
                if len(period_prices) > 1 and period_prices[0] != period_prices[1]:
                    valid_ticks += 1

            changes = get_valid_changes(period_prices)
            if len(changes) < ticks_per_period:
                oscillation_log += f"–>周期{period+1}数据不足"
                print(oscillation_log)
                break

            if len(changes) >= required_ticks:
                all_positive = all(c == '上涨' for c in changes[-required_ticks:])
                all_negative = all(c == '下跌' for c in changes[-required_ticks:])
                if all_positive:
                    print(f"【{self.contract_code}】–>tick震荡监测中–>发现tick连续监测达到入场/平仓条件–>停止tick震荡监测–>触发{'入场' if not self.in_position else '平仓'}")
                    return "up", "continuous"
                if all_negative:
                    print(f"【{self.contract_code}】–>tick震荡监测中–>发现tick连续监测达到入场/平仓条件–>停止tick震荡监测–>触发{'入场' if not self.in_position else '平仓'}")
                    return "down", "continuous"

            all_positive = all(c == '上涨' for c in changes)
            all_negative = all(c == '下跌' for c in changes)
            period_direction = "上涨" if all_positive else "下跌" if all_negative else "震荡"
            oscillation_log += f"–>周期{period+1}{period_direction}"
            if all_positive:
                period_trends.append("up")
            elif all_negative:
                period_trends.append("down")
            else:
                print(f"{oscillation_log}–>终止tick震荡监测")
                break
            print(oscillation_log)

        if len(period_trends) == self.monitor_periods and all(t == period_trends[0] for t in period_trends):
            return period_trends[0], "oscillation"

        if "震荡" in oscillation_log and changes:
            print(f"【{self.contract_code}】–>tick连续监测中–>{changes[-1]}1–>重置tick连续监测并激活tick震荡监测")
            return None, "重置tick连续监测并激活tick震荡监测"
        return None, "无明确趋势"

    def update_order_status(self, order_info):
        """
        处理单个订单的状态变化
        """
        try:
            current_time = time.time()
            
            if isinstance(order_info, str):
                order_id = order_info
                order_info = get_value_by_order_id(
                    order_id,
                    self.account_manager.account_id,
                    'STOCK_OPTION',
                    'ORDER'
                )
                if not order_info:
                    print(f"未找到订单 {order_id} 的信息")
                    if order_id in self.active_orders:
                        del self.active_orders[order_id]
                    return
            
            order_id = getattr(order_info, 'm_strOrderSysID', '')
            contract = getattr(order_info, 'm_strInstrumentID', '')
            order_status = getattr(order_info, 'm_nOrderStatus', -1)
            direction = getattr(order_info, 'm_nOffsetFlag', 0)
            volume = getattr(order_info, 'm_nVolumeTotalOriginal', 0)
            traded = getattr(order_info, 'm_nVolumeTraded', 0)
            price = float(getattr(order_info, 'm_dLimitPrice', 0.0))
            
            if order_id not in self.active_orders:
                return
                
            order_info_dict = self.active_orders[order_id]
            is_close_order = order_info_dict.get('is_close', False)
            timeout = 5 if is_close_order else self.order_timeout
            
            print(f"【{self.contract_code}】–>订单状态更新–>ID: {order_id}, 方向: {'买入开仓' if direction == 50 else '卖出平仓'}")
            print(f"数量: {traded}/{volume}, 价格: {price}, 状态: {order_status}")
            
            if order_status == 3:
                print(f"【{self.contract_code}】–>订单全部成交–>ID: {order_id}")
                del self.active_orders[order_id]
                if hasattr(self, 'account_manager'):
                    self.account_manager.update(self.C)
            elif order_status == 4:
                print(f"【{self.contract_code}】–>订单被拒绝–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print("【{self.contract_code}】–>平仓订单被拒绝–>重新提交")
                    time.sleep(1)
                    self.close_position(self.position)
            elif order_status == 5:
                print(f"【{self.contract_code}】–>订单被撤销–>ID: {order_id}")
                del self.active_orders[order_id]
                if is_close_order:
                    print(f"【{self.contract_code}】–>平仓订单被撤销–>重新提交")
                    time.sleep(1)
                    self.close_position(self.position)
            else:
                order_time = order_info_dict['time']
                if current_time - order_time > timeout:
                    print(f"【{self.contract_code}】–>订单超时–>ID: {order_id}, 平仓单: {is_close_order}")
                    if self.cancel_order(self.C, order_id):
                        print(f"【{self.contract_code}】–>订单超时撤单成功–>ID: {order_id}")
                        if is_close_order:
                            print(f"【{self.contract_code}】–>平仓订单超时–>重新提交")
                            time.sleep(1)
                            self.close_position(self.position)
                    else:
                        print(f"【{self.contract_code}】–>订单超时撤单失败–>ID: {order_id}")
                else:
                    remaining_time = timeout - (current_time - order_time)
                    print(f"【{self.contract_code}】–>订单等待中–>剩余时间: {remaining_time:.1f}秒")
                if traded > 0:
                    print(f"【{self.contract_code}】–>订单部分成交–>{traded}/{volume}")
        except Exception as e:
            print(f"【{self.contract_code}】–>更新订单状态异常: {str(e)}")
            traceback.print_exc()

    def cancel_order(self, C, order_sys_id=None, contract_code=None):
        """撤销委托订单"""
        try:
            orders = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'ORDER')
            if not orders or not isinstance(orders, list):
                return False
            
            active_states = [0, 1, 3, 50, 51]
            orders_cancelled = False
            for order in orders:
                if not hasattr(order, 'm_nOrderStatus'):
                    continue
                    
                status = int(getattr(order, 'm_nOrderStatus', -1))
                current_order_id = getattr(order, 'm_strOrderSysID', '')
                current_contract = getattr(order, 'm_strInstrumentID', '')
                
                if order_sys_id and current_order_id != order_sys_id:
                    continue
                    
                if contract_code and current_contract != contract_code:
                    continue
                    
                if status in active_states:
                    volume_total = getattr(order, 'm_nVolumeTotalOriginal', 0)
                    volume_traded = getattr(order, 'm_nVolumeTraded', 0)
                    
                    if volume_total > volume_traded:
                        result = cancel(
                            current_order_id,
                            self.account_manager.account_id,
                            'STOCK_OPTION',
                            C
                        )
                        
                        if result:
                            print(f"【{self.contract_code}】–>撤单成功–>ID: {current_order_id}")
                            orders_cancelled = True
                        else:
                            print(f"【{self.contract_code}】–>撤单失败–>ID: {current_order_id}")
                            
            return orders_cancelled
        except Exception as e:
            print(f"【{self.contract_code}】–>撤单异常: {str(e)}")
            traceback.print_exc()
            return False

    def load_parameters(self):
        """从XML文件加载策略参数"""
        try:
            xml_path = r"C:\国金QMT交易端模拟\python\formulaLayout\期权策略.xml"
            print(f"【{self.contract_code}】–>加载策略参数–>路径: {xml_path}")
            
            if not os.path.exists(xml_path):
                raise FileNotFoundError(f"未找到配置文件: {xml_path}")
            
            tree = ET.parse(xml_path)
            root = tree.getroot()
            
            updated_params = []
            for item in root.findall('.//item'):
                bind = item.get('bind')
                value = item.get('value')
                
                if not bind or not value:
                    continue
                    
                try:
                    if bind == 'max_position':
                        self.max_position = int(value)
                        updated_params.append(('最大持仓', self.max_position))
                    elif bind == 'stop_loss':
                        self.stop_loss = float(value)
                        updated_params.append(('止损比例', self.stop_loss))
                    elif bind == 'drawdown':
                        self.drawdown = float(value)
                        updated_params.append(('回撤比例', self.drawdown))
                    elif bind == 'trend_sensitivity':
                        self.trend_sensitivity = int(value)
                        updated_params.append(('趋势敏感度', self.trend_sensitivity))
                    elif bind == 'breakout_confirm':
                        self.breakout_confirm = int(value)
                        updated_params.append(('突破确认', self.breakout_confirm))
                    elif bind == 'reversal_threshold':
                        self.reversal_threshold = float(value)
                        updated_params.append(('反转阈值', self.reversal_threshold))
                    elif bind == 'monitor_periods':
                        self.monitor_periods = int(value)
                        updated_params.append(('监测周期数', self.monitor_periods))
                    elif bind == 'commission_per_lot':
                        self.commission_per_lot = float(value)
                        updated_params.append(('每张手续费', self.commission_per_lot))
                    elif bind == 'trend_stop_enabled':
                        self.trend_stop_enabled = int(value)
                        updated_params.append(('趋势止损开关', self.trend_stop_enabled))
                except ValueError as e:
                    print(f"【{self.contract_code}】–>参数[{bind}]格式错误: {str(e)}")
                    continue
            
            if updated_params:
                print(f"【{self.contract_code}】–>成功更新参数:")
                for param_name, param_value in updated_params:
                    print(f"{param_name}: {param_value}")
            else:
                print(f"【{self.contract_code}】–>警告: 未能更新任何参数")
        except Exception as e:
            print(f"【{self.contract_code}】–>加载参数异常: {str(e)}")
            print(f"【{self.contract_code}】–>使用默认参数继续运行")

    def init_position(self, contract_code, volume, open_price, open_time=None):
        """初始化历史持仓信息"""
        try:
            if self.is_legacy_position and self.contract_code == contract_code:
                print(f"【{self.contract_code}】–>持仓已初始化，跳过")
                return
                
            print(f"【{self.contract_code}】–>初始化历史持仓")
            print(f"合约代码: {contract_code}")
            print(f"持仓量: {volume}")
            print(f"开仓价: {open_price:.4f}")
            print(f"开仓时间: {open_time if open_time else time.strftime('%Y%m%d%H%M%S')}")
            
            self.contract_code = contract_code
            self.is_legacy_position = True
            
            order_info = type('OrderInfo', (), {
                'm_strOrderSysID': f'LEGACY_{contract_code}_{time.time()}',
                'm_strInstrumentID': contract_code,
                'm_nOrderStatus': 3,
                'm_nOffsetFlag': 50,
                'm_nVolumeTotalOriginal': volume,
                'm_nVolumeTraded': volume,
                'm_dLimitPrice': open_price,
                'm_strInsertTime': open_time if open_time else time.strftime('%H%M%S')
            })
            
            self.update_order_status(order_info)
        except Exception as e:
            print(f"【{self.contract_code}】–>初始化持仓信息异常: {str(e)}")
            traceback.print_exc()

    # 修改：1. 价格精度限制为小数点后4位; 2. 仅在价格变动时打印日志
    def update_price(self):
        """更新价格数据，仅在价格变化时记录，价格精度限制为小数点后4位"""
        try:
            current_time = time.time()
            if hasattr(self, '_last_update_time') and current_time - self._last_update_time < 1:
                return True
            self._last_update_time = current_time
            
            retries = 3
            for attempt in range(retries):
                tick_data = self.C.get_full_tick([self.contract_code])
                if not isinstance(tick_data, dict) or self.contract_code not in tick_data:
                    print(f"【{self.contract_code}】–>获取行情数据失败–>重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                    continue
                
                data = tick_data[self.contract_code]
                if not isinstance(data, dict):
                    print(f"【{self.contract_code}】–>数据格式错误: {type(data)}")
                    return False
                
                self.current_tick = {
                    'time': data.get('timetag', ''),
                    'lastPrice': data.get('lastPrice', 0.0),
                    'open': data.get('open', 0.0),
                    'high': data.get('high', 0.0),
                    'low': data.get('low', 0.0),
                    'lastClose': data.get('lastClose', 0.0),
                    'amount': data.get('amount', 0.0),
                    'volume': data.get('volume', 0),
                    'openInterest': data.get('openInt', 0),
                    'settlementPrice': data.get('settlementPrice', 0.0),
                    'lastSettlementPrice': data.get('lastSettlementPrice', 0.0),
                    'askPrice': data.get('askPrice', []),
                    'askVol': data.get('askVol', []),
                    'bidPrice': data.get('bidPrice', []),
                    'bidVol': data.get('bidVol', [])
                }
                
                price = self.current_tick['lastPrice']
                if price <= 0:
                    bid_price = self.current_tick['bidPrice'][0] if self.current_tick['bidPrice'] else 0
                    ask_price = self.current_tick['askPrice'][0] if self.current_tick['askPrice'] else 0
                    if bid_price > 0 and ask_price > 0:
                        price = (bid_price + ask_price) / 2
                    else:
                        price = self.current_tick['lastClose']
                
                if price <= 0:
                    print(f"【{self.contract_code}】–>尝试 {attempt+1}/{retries} 获取有效价格失败")
                    continue
                
                # 限制价格精度为小数点后4位
                price = round(price, 4)
                self.current_price = price
                current_tick_time = self.current_tick.get('time', '')
                
                # 仅在价格变动时记录日志和更新历史
                if not self.price_history or price != self.price_history[-1]['price']:
                    self.price_history.append({'price': price, 'time': current_tick_time})
                    if len(self.price_history) > 20:
                        self.price_history.pop(0)
                    print(f"【{self.contract_code}】–>价格变动–>{price:.4f} at {current_tick_time}")
                
                if not self._price_initialized:
                    self.initial_price = price
                    self.daily_high = price
                    self.daily_low = price
                    if self.in_position:
                        self.highest_price = price
                    self._price_initialized = True
                else:
                    if self.daily_high is None:
                        self.daily_high = price
                    else:
                        self.daily_high = max(self.daily_high, price)
                    
                    if self.daily_low is None:
                        self.daily_low = price
                    else:
                        self.daily_low = min(self.daily_low, price)
                
                return True
            
            print(f"【{self.contract_code}】–>无法获取有效价格–>已重试{retries}次")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>更新价格异常: {str(e)}")
            return False

    # 修改：优化平仓条件检测与日志
    def check_exit_conditions(self, check_time=True):
        """检查是否满足出场条件"""
        if not self.in_position:
            return False
                
        if check_time and not is_trade_time():
            return False
                
        if not self.update_price():
            print(f"【{self.contract_code}】–>无法更新价格–>跳过平仓检查")
            return False
        
        if self.trend_stop_enabled:
            trend, trend_type = self.calculate_trend()
            if trend == "down":
                self.close_reason = "趋势止损"
                print(f"【{self.contract_code}】–>触发平仓条件–>{self.close_reason}")
                return True
        
        if self.highest_price is None or self.current_price > self.highest_price:
            self.highest_price = self.current_price
        
        if self.highest_price > 0:
            current_drawdown = (self.highest_price - self.current_price) / self.highest_price
            if current_drawdown >= self.drawdown:
                self.close_reason = f"回撤 {current_drawdown*100:.2f}%"
                print(f"【{self.contract_code}】–>触发平仓条件–>{self.close_reason}")
                return True
        
        if self.entry_price:
            profit_ratio = (self.current_price - self.entry_price) / self.entry_price
            if profit_ratio <= -self.stop_loss:
                self.close_reason = f"止损 {profit_ratio*100:.2f}%"
                print(f"【{self.contract_code}】–>触发平仓条件–>{self.close_reason}")
                return True
        
        return False

    # 修改：优化入场逻辑，动态计算数量
    def enter_position(self, C):
        """执行入场操作，动态计算数量"""
        try:
            if not self.current_tick and not self.update_price():
                print(f"【{self.contract_code}】–>无法获取当前行情数据")
                return False

            if self.contract_code not in ContractMonitor._subscribed_contracts:
                print(f"【{self.contract_code}】–>订阅合约")
                self.C.subscribe_quote(self.contract_code, "1", "0")
                ContractMonitor._subscribed_contracts.add(self.contract_code)

            bid_price = self.current_tick.get('bidPrice', [0])[0]
            ask_price = self.current_tick.get('askPrice', [0])[0]
            ask_vol = self.current_tick.get('askVol', [0])[0]
            if not bid_price or not ask_price or not ask_vol:
                print(f"【{self.contract_code}】–>无法获取有效买卖价格或卖一量")
                return False

            available_fund = self.account_manager.account_info.get('m_dAvailable', 0)
            max_by_fund = int(available_fund / (ask_price * 10000))
            order_volume = min(ask_vol, self.max_position - self.position, max_by_fund, 6)  # 限制最大6张
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用入场数量")
                return False

            order_price = round(ask_price, 4) # 限制精度
            print(f"【{self.contract_code}】–>触发入场–>价格: {order_price:.4f}, 数量: {order_volume}")

            timestamp = time.strftime('%Y%m%d_%H%M%S')
            random_num = random.randint(1000, 9999)
            order_remark = f"{self.contract_code}_BUY_{timestamp}_{random_num}"

            order_id = passorder(
                50, 1101, self.account_manager.account_id, self.contract_code,
                1, order_price, order_volume, "期权策略", 2, order_remark, C
            )

            if order_id:
                print(f"【{self.contract_code}】–>入场订单提交成功–>ID: {order_id}")
                self.last_order_id = order_id
                self.last_order_check_time = time.time()
                self.active_orders[order_id] = {
                    'time': time.time(),
                    'is_close': False,
                    'price': order_price,
                    'volume': order_volume,
                    'traded': 0,
                    'direction': 50,
                    'contract': self.contract_code
                }
                return True
            print(f"【{self.contract_code}】–>入场订单提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行入场操作异常: {str(e)}")
            traceback.print_exc()
            return False

    # 修改：优化平仓逻辑，动态计算数量
    def exit_position(self, reason):
        """执行出场交易"""
        if not self.check_exit_conditions():
            return False
                    
        print(f"【{self.contract_code}】–>触发平仓–>{reason}")
        
        try:
            self.cancel_order(self.C)
            
            self.account_manager.update(self.C)
            print(f"【{self.contract_code}】–>持仓字典: {self.account_manager.positions}")
            
            contract_key = self.contract_code.replace('.SHO', '')
            position_info = self.account_manager.positions.get(contract_key)
            print(f"【{self.contract_code}】–>持仓信息: {position_info}")
            
            if not position_info:
                positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if (hasattr(pos, 'm_strInstrumentID') and 
                            pos.m_strInstrumentID == contract_key):
                            position_info = {
                                'AvailableVolume': getattr(pos, 'm_nVolume', 0) - getattr(pos, 'm_nFrozenVolume', 0)
                            }
                            print(f"【{self.contract_code}】–>从数据源获取持仓信息: {position_info}")
                            break
                if not position_info:
                    print(f"【{self.contract_code}】–>无持仓信息")
                    return False
                    
            available_volume = position_info.get('AvailableVolume', 0)
            bid_vol = self.current_tick.get('bidVol', [0])[0] if self.current_tick else 0
            print(f"【{self.contract_code}】–>可用持仓: {available_volume}, 买一量: {bid_vol}")
            if available_volume <= 0 or not bid_vol:
                print(f"【{self.contract_code}】–>无可用持仓或买一量 (可用: {available_volume}, 买一量: {bid_vol})")
                return False
                    
            order_volume = min(available_volume, bid_vol)
            if order_volume <= 0:
                print(f"【{self.contract_code}】–>无可用平仓数量")
                return False
            
            bid_price = self.current_tick.get('bidPrice', [0])[0] if self.current_tick else 0
            order_price = round(bid_price, 4)
            ret = execute_order(
                self.contract_code, 'SELL', order_volume, 1,
                order_price, self.account_manager.account_id,
                self.C, self.account_manager.account_info, is_open=False,
                current_tick=self.current_tick  # 传递当前tick数据
            )
            
            if ret:
                print(f"【{self.contract_code}】–>平仓委托提交成功–>ID: {ret}")
                self.pending_close = False
                self.close_reason = None
                return True
            print(f"【{self.contract_code}】–>平仓委托提交失败")
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>执行出场异常: {str(e)}")
            traceback.print_exc()
            return False

    # 修改：优化入场条件检测
    def check_entry_conditions(self):
        """检查入场条件"""
        try:
            if self.active_orders:
                print(f"【{self.contract_code}】–>存在未完成订单，暂不入场")
                return False

            if not self.account_manager or not self.account_manager.account_info:
                print(f"【{self.contract_code}】–>等待账户信息初始化")
                return False
                
            if len(self.price_history) < self.trend_sensitivity + self.breakout_confirm:
                print(f"【{self.contract_code}】–>价格历史不足 ({len(self.price_history)}/{self.trend_sensitivity + self.breakout_confirm})")
                return False
                
            if not self.current_price:
                return False

            if self.position >= self.max_position:
                print(f"【{self.contract_code}】–>达到最大持仓限制: {self.max_position}")
                return False

            trend, trend_type = self.calculate_trend()
            if trend in ["up", "down"]:
                return self.enter_position(self.C)
            return False
        except Exception as e:
            print(f"【{self.contract_code}】–>检查入场条件异常: {str(e)}")
            return False

    def check_trend_strength(self):
        """检查趋势强度（保留原始方法）"""
        try:
            if len(self.price_history) < 2:
                return 0.0
                
            prices = [p['price'] for p in self.price_history]
            price_changes = []
            for i in range(1, len(prices)):
                if prices[i-1] > 0:
                    change = (prices[i] - prices[i-1]) / prices[i-1]
                    price_changes.append(change)
            
            if not price_changes:
                return 0.0
                
            avg_change = sum(price_changes) / len(price_changes)
            return abs(avg_change)
        except Exception as e:
            print(f"【{self.contract_code}】–>计算趋势强度异常: {str(e)}")
            return 0.0

    # 修改：优化on_tick逻辑与日志
    def on_tick(self):
        """处理实时行情数据"""
        try:
            current_time = time.time()
            if hasattr(self, '_last_tick_time') and current_time - self._last_tick_time < 1:
                return
            self._last_tick_time = current_time
            
            if not self.update_price():
                return
                    
            if hasattr(self, 'account_manager'):
                self.account_manager.update(self.C)
            
            positions = get_trade_detail_data(self.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            if positions and isinstance(positions, list):
                self.position = 0
                self.in_position = False
                for pos in positions:
                    if (hasattr(pos, 'm_strInstrumentID') and 
                        pos.m_strInstrumentID == self.contract_code.replace('.SHO', '')):
                        self.position = getattr(pos, 'm_nVolume', 0)
                        self.in_position = self.position > 0
                        if self.in_position and not self.entry_price:
                            self.entry_price = float(getattr(pos, 'm_dbOpenPrice', 0.0))
                        break
            
            if self.in_position and self.check_exit_conditions():
                self.exit_position(self.close_reason)
            elif not self.in_position:
                self.check_entry_conditions()
        except Exception as e:
            print(f"【{self.contract_code}】–>行情处理异常: {str(e)}")
            traceback.print_exc()

# 5. 主要策略函数
def call_back(data):
    """主策略回调函数"""
    try:
        if isinstance(data, dict) and 'EventCode' in data and data['EventCode'] == 0:
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
                
            C = call_back.context
            
            print(f"订阅标的行情: {underlying}")
            C.subscribe_quote(underlying)
            
            call_code, put_code = select_options(C, underlying)
            
            if call_code and put_code:
                print(f"订阅期权合约: [{call_code}, {put_code}]")
                C.subscribe_quote(call_code)
                C.subscribe_quote(put_code)
                
                contract_monitor = ContractMonitor(C, call_code, account_manager)
                contract_monitor.init_contract(call_code)
                
                contract_monitor.call_option = call_code
                contract_monitor.put_option = put_code
                
            return
            
        if isinstance(data, dict):
            if not hasattr(call_back, 'context'):
                print("上下文未初始化")
                return
                
            C = call_back.context
            
            for code, tick_data in data.items():
                if code in C.monitors:
                    monitor = C.monitors[code]
                    monitor.on_tick()

    except Exception as e:
        print(f"策略异常: {str(e)}")
        traceback.print_exc()

def init(C):
    """初始化策略"""
    global strategy_state
    
    strategy_state = StrategyState(C)
    
    strategy_state.subID = 0
    strategy_state.undl_code = '510300.SH'
    strategy_state.monitors = {}
    C.monitors = strategy_state.monitors
    
    print(f"订阅标的行情: {strategy_state.undl_code}")
    C.subscribe_quote(strategy_state.undl_code, "1", "0")
    
    account_id = '*********'
    strategy_state.account_manager = AccountManager(account_id)
    
    C.set_account(account_id)
    
    account_type = 'STOCK_OPTION'
    
    call_back.context = C

def after_init(C):
    """初始化后执行的操作"""
    try:
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
            positions = get_trade_detail_data(strategy_state.account_manager.account_id, 'STOCK_OPTION', 'POSITION')
            position_contracts = set()
            
            if positions and isinstance(positions, list):
                for pos in positions:
                    if hasattr(pos, 'm_strInstrumentID'):
                        contract_code = pos.m_strInstrumentID
                        pos_volume = getattr(pos, 'm_nVolume', 0)
                        if pos_volume > 0:
                            position_contracts.add(contract_code)
                            
                            if contract_code not in strategy_state.monitors:
                                monitor = ContractMonitor(C, contract_code, strategy_state.account_manager)
                                strategy_state.monitors[contract_code] = monitor
                                
                                entry_price = float(getattr(pos, 'm_dbOpenPrice', 0.0))
                                monitor.position = pos_volume
                                monitor.entry_price = entry_price
                                monitor.in_position = True
                                monitor.is_legacy_position = True
                                print(f"【{contract_code}】–>初始化已有持仓–>持仓量: {pos_volume}, 开仓价: {entry_price}")
        
        tick_data = C.get_full_tick([strategy_state.undl_code])
        if not tick_data or strategy_state.undl_code not in tick_data:
            print("无法获取市场数据")
            return
            
        data = tick_data[strategy_state.undl_code]
        if not isinstance(data, dict):
            print("市场数据格式错误")
            return
            
        timetag = data.get('timetag', '')
        current_date = timetag.split()[0].replace('-', '') if timetag else ''
        
        target_price = data.get('lastPrice', 0)
        if target_price <= 0:
            target_price = data.get('lastClose', 0)
            if target_price > 0:
                print("使用昨收盘价")
            else:
                print("无法获取有效价格")
                return
                
        if not current_date:
            print("无法获取有效日期")
            return
            
        print(f"\n当前日期: {current_date}")
        print(f"标的{strategy_state.undl_code}当前价格: {target_price}")
        
        all_options = C.get_option_list(strategy_state.undl_code, current_date, "", True)
        if not all_options:
            print("没有可用期权")
            return
            
        expiry_groups = {}
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if not detail:
                continue
                
            expiry = detail['ExpireDate']
            if expiry not in expiry_groups:
                expiry_groups[expiry] = []
            expiry_groups[expiry].append((opt, detail))
        
        if not expiry_groups:
            print("无法获取期权详情")
            return
            
        nearest_expiry = min(expiry_groups.keys())
        print(f"\n选择到期日: {nearest_expiry}")
        
        selected_options = []
        min_call_diff = float('inf')
        min_put_diff = float('inf')
        closest_call = None
        closest_put = None
        
        for opt, detail in expiry_groups[nearest_expiry]:
            strike = float(detail['OptExercisePrice'])
            
            if detail['optType'] == 'CALL':
                if strike > target_price:
                    diff = abs(strike - target_price)
                    if diff < min_call_diff:
                        min_call_diff = diff
                        closest_call = (opt, detail)
            elif detail['optType'] == 'PUT':
                if strike < target_price:
                    diff = abs(strike - target_price)
                    if diff < min_put_diff:
                        min_put_diff = diff
                        closest_put = (opt, detail)
        
        if closest_call:
            opt, detail = closest_call
            if opt not in selected_options:
                selected_options.append(opt)
                print(f"\n选中看涨期权: {opt}")
                print(f"到期日: {detail['ExpireDate']}")
                print(f"行权价: {detail['OptExercisePrice']}")
                print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            
        if closest_put:
            opt, detail = closest_put
            if opt not in selected_options:
                selected_options.append(opt)
                print(f"\n选中看跌期权: {opt}")
                print(f"到期日: {detail['ExpireDate']}")
                print(f"行权价: {detail['OptExercisePrice']}")
                print(f"与标的价差: {abs(float(detail['OptExercisePrice']) - target_price)}")
            
        if selected_options:
            print(f"\n订阅期权合约: {selected_options}")
            
            for opt in selected_options:
                if opt not in strategy_state.monitors:
                    monitor = ContractMonitor(C, opt, strategy_state.account_manager)
                    strategy_state.monitors[opt] = monitor
                    
                    if opt not in position_contracts:
                        print(f"【{opt}】–>初始化策略合约")
                        monitor.update_price()
            
            active_contracts = list(set(selected_options) | position_contracts)
            if active_contracts:
                strategy_state.subID = C.subscribe_whole_quote(active_contracts, callback=call_back)
                if strategy_state.subID > 0:
                    print(f"行情订阅成功，订阅号: {strategy_state.subID}")
                else:
                    print("行情订阅失败")
        else:
            print("未找到合适期权合约")
            
    except Exception as e:
        print(f"初始化异常: {str(e)}")
        traceback.print_exc()
        strategy_state.subID = 0

def handlebar(C):
    """K线数据更新时执行"""
    try:
        if not hasattr(C, 'monitors'):
            return
            
        if hasattr(strategy_state, 'account_manager'):
            strategy_state.account_manager.update(C)
            
        for code, monitor in C.monitors.items():
            try:
                monitor.on_tick()
            except Exception as e:
                print(f"处理合约 {code} 异常: {str(e)}")
                traceback.print_exc()
                
    except Exception as e:
        print(f"handlebar异常: {str(e)}")
        traceback.print_exc()

def calculate_trade_volume(account_info, price, current_position, available_volume=None, max_position=None):
    """
    计算合适的交易数量
    """
    try:
        if not account_info:
            print("账户信息未初始化")
            return 0
            
        available_fund = account_info.get('m_dAvailable', 0)
        
        if available_fund <= 0:
            print(f"可用资金不足: {available_fund}")
            return 0
            
        remaining_position = max_position - current_position if max_position is not None else float('inf')
        if remaining_position <= 0:
            print(f"已达到最大持仓限制: {max_position}")
            return 0
            
        max_volume_by_fund = int(available_fund / (price * 10000))
        
        volume = min(
            available_volume if available_volume is not None else float('inf'),
            remaining_position,
            max_volume_by_fund
        )
        
        if volume <= 0:
            print(f"计算后的交易数量为0")
            return 0
            
        print(f"\n>>> 交易数量计算 <<<")
        print(f"当前持仓: {current_position}张")
        print(f"最大持仓限制: {max_position if max_position else '不限'}张")
        print(f"剩余可买: {remaining_position}张")
        print(f"可用资金: {available_fund:.2f}")
        print(f"合约价格: {price:.4f}")
        print(f"资金允许买入: {max_volume_by_fund}张")
        print(f"可成交量限制: {available_volume if available_volume else '不限'}张")
        print(f"最终计划买入: {volume}张")
        
        return volume
        
    except Exception as e:
        print(f"计算交易数量异常: {str(e)}")
        return 0

def is_trade_time():
    """检查是否在交易时段"""
    current_time = time.localtime()
    hour = current_time.tm_hour
    minute = current_time.tm_min
    
    if current_time.tm_wday >= 5:
        return False
        
    if (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30):
        return True
        
    if (hour >= 13 and hour < 15) or (hour == 15 and minute == 0):
        return True
        
    return False

def execute_order(contract_code, direction, volume, price_type, price, account_id, C, account_info, is_open=True, current_tick=None):
    """执行订单"""
    try:
        if not is_trade_time():
            print("当前不在交易时段,不能下单")
            return None
            
        if not account_info:
            print("账户信息无效")
            return None
            
        if is_open:
            try:
                total_position = 0
                has_position = False
                positions = get_trade_detail_data(account_id, 'STOCK_OPTION', 'POSITION')
                if positions and isinstance(positions, list):
                    for pos in positions:
                        if hasattr(pos, 'm_nVolume'):
                            total_position += getattr(pos, 'm_nVolume', 0)
                            if (hasattr(pos, 'm_strInstrumentID') and 
                                pos.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                has_position = True
                                print(f"发现本合约已有持仓，不能开新仓")
                                return None
                
                pending_volume = 0
                has_pending_orders = False
                orders = get_trade_detail_data(account_id, 'STOCK_OPTION', 'ORDER')
                if orders and isinstance(orders, list):
                    for order in orders:
                        if (hasattr(order, 'm_nOffsetFlag') and 
                            getattr(order, 'm_nOffsetFlag') == 50):
                            status = getattr(order, 'm_nOrderStatus', -1)
                            if status in [0, 1, 3]:
                                if (hasattr(order, 'm_strInstrumentID') and 
                                    order.m_strInstrumentID == contract_code.replace('.SHO', '')):
                                    print(f"发现本合约有未完成委托，不能开新仓")
                                    return None
                                pending_volume += (getattr(order, 'm_nVolumeTotalOriginal', 0) - 
                                                getattr(order, 'm_nVolumeTraded', 0))
                                has_pending_orders = True
                
                total_exposure = total_position + pending_volume + volume
                max_position = account_info.get('max_position', 30)
                
                if total_exposure > max_position:
                    print(f"\n>>> 持仓限制检查 <<<")
                    print(f"当前持仓: {total_position}")
                    print(f"未完成开仓: {pending_volume}")
                    print(f"本次委托: {volume}")
                    print(f"总暴露: {total_exposure}")
                    print(f"最大持仓限制: {max_position}")
                    print("超过最大持仓限制，取消委托")
                    return None
                    
                if has_pending_orders:
                    print("存在未完成的开仓委托，等待处理完成后再开新仓")
                    return None
                    
            except Exception as e:
                print(f"检查持仓限制异常: {str(e)}")
                return None
                
        if not is_open:
            # 平仓时，使用传入的 current_tick 数据
            if current_tick:
                bid_vol = current_tick.get('bidVol', [0])[0]
                print(f"【{contract_code}】–>使用传入tick数据，买一量: {bid_vol}")
                if bid_vol < volume:
                    print(f"市场可成交量不足: 需要{volume}, 可成交量{bid_vol}")
                    return None
            else:
                # 若无传入数据，重试获取
                retries = 3
                for attempt in range(retries):
                    tick_data = C.get_full_tick([f"{contract_code}.SHO"])
                    if tick_data and f"{contract_code}.SHO" in tick_data:
                        data = tick_data[f"{contract_code}.SHO"]
                        bid_vol = data.get('bidVol', [0])[0]
                        print(f"【{contract_code}】–>重试获取tick数据，买一量: {bid_vol}")
                        if bid_vol >= volume:
                            break
                    print(f"【{contract_code}】–>获取tick数据失败，重试 {attempt+1}/{retries}")
                    time.sleep(0.5)
                else:
                    print(f"【{contract_code}】–>无法获取市场报价，假设市场无成交量")
                    return None
            
        # 获取价格，优先使用传入的 tick 数据
        if current_tick and direction == 'SELL':
            exec_price = current_tick.get('bidPrice', [0])[0] if current_tick else price
            print(f"【{contract_code}】–>使用传入tick价格: {exec_price}")
        else:
            subscribe_code = f"{contract_code}.SHO"
            tick_data = C.get_full_tick([subscribe_code])
            if tick_data and subscribe_code in tick_data:
                data = tick_data[subscribe_code]
                if isinstance(data, dict):
                    bid_price = data.get('bidPrice', [0])[0]
                    ask_price = data.get('askPrice', [0])[0]
                    if bid_price > 0 and ask_price > 0:
                        print(f"获取到有效行情 - 买价: {bid_price}, 卖价: {ask_price}")
                        exec_price = bid_price if direction == 'SELL' else ask_price
                    else:
                        print("行情价格无效，使用委托价格")
                        exec_price = price
                else:
                    print("行情数据格式错误，使用委托价格")
                    exec_price = price
            else:
                print(f"无法获取{contract_code}市场报价，使用委托价格")
                exec_price = price
            
        if direction == 'BUY':
            if not is_open:
                print("不支持买入平仓操作")
                return None
            op_type = 50
        else:
            if is_open:
                print("不支持卖出开仓操作")
                return None
            op_type = 51
            
        print("\n>>> 委托参数 <<<")
        print(f"合约代码: {contract_code}")
        print(f"交易方向: {direction}")
        print(f"开平标志: {'开仓' if is_open else '平仓'}")
        print(f"委托数量: {volume}")
        print(f"价格类型: {price_type}")
        print(f"委托价格: {exec_price}")
        
        print(f"\n>>> 开始执行委托 - {'开仓' if is_open else '平仓'} <<<")
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        random_num = random.randint(1000, 9999)
        order_remark = f"{contract_code}_{direction}_{timestamp}_{random_num}"
        
        exec_price = round(exec_price, 4)
        result = passorder(
            op_type,
            1101,
            account_id,
            contract_code,
            price_type,
            exec_price,
            volume,
            '期权交易',
            2,
            order_remark,
            C
        )
        
        if result is not None:
            print(f"\n>>> 委托已发送 - 投资备注: {order_remark} <<<")
            return order_remark
        else:
            print("委托发送失败，返回值为None")
            return None
            
    except Exception as e:
        print(f"订单执行异常: {str(e)}")
        traceback.print_exc()
        return None

def get_option_expiry_dates(C):
    """获取期权到期日列表"""
    try:
        current_date = datetime.now().strftime('%Y%m%d')
        expiry_dates = C.get_option_expire_date()
        if not expiry_dates:
            return []
            
        valid_dates = [d for d in expiry_dates if d > current_date]
        valid_dates.sort()
        return valid_dates
        
    except Exception as e:
        print(f"获取到期日失败: {str(e)}")
        return []

def get_options_by_strike(C, underlying, expiry, option_type):
    """
    获取指定到期日和类型的期权列表
    """
    try:
        options = []
        all_options = C.get_option_list(underlying, expiry, option_type.upper(), True)
        
        for opt in all_options:
            detail = C.get_option_detail_data(opt)
            if detail:
                options.append({
                    'code': opt,
                    'strike': float(detail['OptExercisePrice']),
                    'type': option_type,
                    'expiry': detail['ExpireDate']
                })
                
        return options
        
    except Exception as e:
        print(f"获取期权列表失败: {str(e)}")
        return []

# 3. 账户管理类
class AccountManager:
    def __init__(self, account_id):
        """账户管理器初始化"""
        self.account_id = account_id
        self.account_info = {
            'm_strAccountID': account_id,
            'm_dAvailable': 0.0,
            'm_dBalance': 0.0,
            'm_dInstrumentValue': 0.0
        }
        self.orders = {}
        self.positions = {}
        self.last_update_time = 0
        self.update_interval = 1
        print(f"\n>>> 账户管理器初始化 - {account_id} <<<")

    def update(self, C):
        """
        更新账户信息和持仓状态
        """
        try:
            current_time = time.time()
            if current_time - self.last_update_time < self.update_interval:
                return
                    
            self.last_update_time = current_time
            
            orders = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ORDER')
            if orders:
                new_orders = {}
                for order in orders:
                    order_id = getattr(order, 'm_strOrderSysID', '')
                    if not order_id:
                        continue
                        
                    status = int(getattr(order, 'm_nOrderStatus', -1))
                    contract = getattr(order, 'm_strInstrumentID', '')
                    direction = int(getattr(order, 'm_nOffsetFlag', 0))
                    
                    if direction not in [50, 51]:
                        continue
                        
                    if status in [0, 1]:
                        new_orders[order_id] = {
                            'contract': contract,
                            'direction': direction,
                            'price': float(getattr(order, 'm_dLimitPrice', 0.0)),
                            'volume': int(getattr(order, 'm_nVolumeTotalOriginal', 0)),
                            'traded': int(getattr(order, 'm_nVolumeTraded', 0)),
                            'status': status,
                            'submit_time': getattr(order, 'm_strInsertTime', '')
                        }
                        
                        if order_id not in self.orders:
                            if hasattr(C, 'monitors'):
                                monitor = C.monitors.get(contract)
                                if monitor:
                                    monitor.active_orders[order_id] = {
                                        'time': time.time(),
                                        'is_close': direction == 51,
                                        'price': new_orders[order_id]['price'],
                                        'volume': new_orders[order_id]['volume'],
                                        'traded': new_orders[order_id]['traded'],
                                        'direction': direction
                                    }
                        elif hasattr(C, 'monitors'):
                            monitor = C.monitors.get(contract)
                            if monitor and order_id in monitor.active_orders:
                                monitor.active_orders[order_id].update({
                                    'traded': new_orders[order_id]['traded']
                                })
                
                self.orders = new_orders
                
                if hasattr(C, 'monitors'):
                    for monitor in C.monitors.values():
                        to_delete = [order_id for order_id in monitor.active_orders 
                                   if order_id not in new_orders]
                        for order_id in to_delete:
                            del monitor.active_orders[order_id]
            
            account_info = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'ACCOUNT')
            if account_info and len(account_info) > 0:
                account_obj = account_info[0]
                new_account_info = {
                    'm_strAccountID': self.account_id,
                    'm_dAvailable': float(getattr(account_obj, 'm_dAvailable', 0.0)),
                    'm_dBalance': float(getattr(account_obj, 'm_dBalance', 0.0)),
                    'm_dInstrumentValue': float(getattr(account_obj, 'm_dInstrumentValue', 0.0))
                }
                
                if self._has_account_changes(new_account_info):
                    self.account_info = new_account_info
                    self._log_account_changes(new_account_info)
            
            positions = get_trade_detail_data(self.account_id, 'STOCK_OPTION', 'POSITION')
            if positions:
                new_positions = {}
                for pos in positions:
                    contract = getattr(pos, 'm_strInstrumentID', '')
                    volume = getattr(pos, 'm_nVolume', 0)
                    frozen_volume = getattr(pos, 'm_nFrozenVolume', 0)
                    if volume > 0:
                        new_positions[contract] = {
                            'Volume': volume,
                            'FrozenVolume': frozen_volume,
                            'AvailableVolume': volume - frozen_volume,
                            'OpenPrice': float(getattr(pos, 'm_dOpenPrice', 0.0)),
                            'PositionValue': volume * float(getattr(pos, 'm_dOpenPrice', 0.0)) * 10000,
                            'UnrealizedPL': float(getattr(pos, 'm_dPositionProfit', 0.0))
                        }
                        
                        if hasattr(C, 'monitors'):
                            monitor = C.monitors.get(contract)
                            if monitor and monitor.contract_code == contract:
                                monitor.position = volume
                                monitor.in_position = volume > 0
                                if volume > 0 and not monitor.entry_price:
                                    monitor.entry_price = new_positions[contract]['OpenPrice']
                                    monitor.entry_time = time.strftime("%Y%m%d%H%M%S")
                
                if self._has_position_changes(new_positions):
                    self._log_position_changes(new_positions)
                    self.positions = new_positions
                        
        except Exception as e:
            print(f"更新账户信息异常: {str(e)}")
            traceback.print_exc()

    def _has_account_changes(self, new_account_info):
        """检查账户信息是否有变化"""
        if not self.account_info:
            return True
            
        thresholds = {
            'm_dAvailable': 0.01,
            'm_dBalance': 0.01,
            'm_dInstrumentValue': 0.01,
        }
        
        for key, threshold in thresholds.items():
            old_value = self.account_info.get(key, 0)
            new_value = new_account_info[key]
            if abs(new_value - old_value) >= threshold:
                return True
                
        if new_account_info.get('m_strStatus') != self.account_info.get('m_strStatus'):
            return True
            
        return False
        
    def _log_account_changes(self, new_account_info):
        """记录账户变化"""
        print("\n>>> 账户状态 <<<")
        print(f"账户ID: {new_account_info['m_strAccountID']}")
        print(f"可用资金: {new_account_info['m_dAvailable']:.2f}")
        print(f"总资产: {new_account_info['m_dBalance']:.2f}")
        print(f"持仓市值: {new_account_info['m_dInstrumentValue']:.2f}")
        print("")
        
    def _has_position_changes(self, new_positions):
        """检查持仓是否有显著变化"""
        if not self.positions:
            return bool(new_positions)
            
        if set(self.positions.keys()) != set(new_positions.keys()):
            return True
            
        for contract in self.positions:
            if contract not in new_positions:
                continue
                
            old_pos = self.positions[contract]
            new_pos = new_positions[contract]
            
            if old_pos['Volume'] != new_pos['Volume']:
                return True
                
            old_pnl_ratio = (old_pos['UnrealizedPL'] / old_pos['PositionValue'] * 100 
                        if old_pos['PositionValue'] > 0 else 0)
            new_pnl_ratio = (new_pos['UnrealizedPL'] / new_pos['PositionValue'] * 100 
                        if new_pos['PositionValue'] > 0 else 0)
            if abs(new_pnl_ratio - old_pnl_ratio) > 0.1:
                return True
                
        return False
        
    def _log_position_changes(self, new_positions):
        """记录持仓变化"""
        if not new_positions:
            return
            
        print("\n>>> 持仓状态 <<<")
        for contract_id, pos in new_positions.items():
            volume = pos.get('Volume', 0)
            open_price = pos.get('OpenPrice', 0.0)
            unrealized_pl = pos.get('UnrealizedPL', 0.0)
            
            market_value = volume * open_price * 10000
            if market_value > 0:
                pl_ratio = unrealized_pl / market_value if market_value != 0 else 0
                print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}, 盈亏比例: {pl_ratio*100:.2f}%")
            else:
                print(f"【{contract_id}】–>持仓更新–>持仓量: {volume}张, 开仓价: {open_price:.6f}, 未实现盈亏: {unrealized_pl:.2f}")
                if 'OpenTime' in pos:
                    print(f"开仓时间: {pos['OpenTime']}")
                if 'Direction' in pos:
                    print(f"方向: {'多头' if pos['Direction'] == 1 else '空头'}")
                if 'MarketValue' in pos:
                    print(f"市值: {pos['MarketValue']:.2f}")
                print("")