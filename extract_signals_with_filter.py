#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取带过滤器信息的信号数据
基于现有的extract_signals.py，添加过滤器决策信息
"""

import re
import pandas as pd
from datetime import datetime, timedelta

# ==================== 可配置参数 ====================
PROFIT_WINDOW_MINUTES = 5  # 盈利窗口期（分钟），可以修改这个参数来调整判断标准

def validate_trigger_sequence(sequence, signal_type, buy_price):
    """验证触发序列的有效性"""
    if not sequence:
        return False, "序列为空"

    try:
        # 解析序列
        prices_str = sequence.split('→')
        if len(prices_str) < 2:
            return False, "序列长度不足"

        prices = [float(p) for p in prices_str]

        if signal_type == "连续买入":
            # 连续买入信号应该有5个连续上涨的价格
            if len(prices) != 5:
                return False, f"连续信号序列长度错误: {len(prices)}, 应为5"

            # 检查连续上涨
            for i in range(1, len(prices)):
                if prices[i] <= prices[i-1]:
                    return False, f"非连续上涨: {prices[i-1]} -> {prices[i]}"

            # 检查最后价格与买入价格是否一致
            if abs(prices[-1] - buy_price) > 0.0001:
                return False, f"最后价格不匹配: {prices[-1]} vs {buy_price}"

        elif signal_type == "震荡买入":
            # 震荡买入信号的触发序列格式不同，只需要验证价格一致性
            if "震荡信号触发价格:" in sequence:
                price_str = sequence.split("震荡信号触发价格: ")[1]
                # 移除可能的验证失败标记
                if " [验证失败:" in price_str:
                    price_str = price_str.split(" [验证失败:")[0]
                try:
                    trigger_price = float(price_str)
                    if abs(trigger_price - buy_price) > 0.0001:
                        return False, f"震荡信号价格不匹配: {trigger_price} vs {buy_price}"
                except ValueError:
                    return False, f"震荡信号价格解析失败: {price_str}"
            else:
                return False, "震荡信号格式错误"

        return True, "序列有效"

    except ValueError as e:
        return False, f"价格解析错误: {e}"

class SignalExtractor:
    """信号提取器类"""

    def _validate_continuous_sequence(self, sequence):
        """验证连续信号序列"""
        try:
            if '→' not in sequence:
                return False

            prices_str = sequence.split('→')
            if len(prices_str) != 5:
                return False

            prices = [float(p) for p in prices_str]

            # 检查连续上涨
            for i in range(1, len(prices)):
                if prices[i] <= prices[i-1]:
                    return False

            return True
        except:
            return False

    def _universal_extract_trigger_sequence(self, signal_num, signal_time, block_content, full_content):
        """通用触发序列提取方法"""

        # 方法1: 精确时间匹配
        sequence = self._extract_by_exact_time(signal_time, full_content)
        if sequence:
            return sequence, "精确时间匹配"

        # 方法2: 信号前搜索
        sequence = self._extract_by_signal_context(signal_num, full_content)
        if sequence:
            return sequence, "信号前搜索"

        # 方法3: 模糊匹配
        sequence = self._extract_by_fuzzy_match(signal_time, full_content)
        if sequence:
            return sequence, "模糊匹配"

        return "", "未找到"

    def _extract_by_exact_time(self, signal_time, content):
        """方法1: 精确时间匹配"""
        try:
            # 连续信号格式
            pattern = rf'\[{re.escape(signal_time)}\] 触发连续买入信号！10009356\.SHO: ([\d.]+(?:->[\d.]+)*)'
            match = re.search(pattern, content)
            if match:
                sequence = match.group(1).replace('->', '→')
                if self._validate_continuous_sequence(sequence):
                    return sequence

            # 震荡信号格式
            pattern = rf'\[{re.escape(signal_time)}\] 触发震荡买入信号！10009356\.SHO: ([\d.]+) \(价格: ([\d.]+)\)'
            match = re.search(pattern, content)
            if match:
                price = match.group(1)
                return f"震荡信号触发价格: {price}"
        except Exception as e:
            print(f"⚠️ 精确时间匹配失败: {e}")

        return None

    def _extract_by_signal_context(self, signal_num, content):
        """方法2: 在信号前后文中搜索"""
        try:
            # 找到信号位置
            signal_pattern = rf'第{signal_num}次买入信号'
            signal_match = re.search(signal_pattern, content)
            if not signal_match:
                return None

            signal_pos = signal_match.start()

            # 在信号前搜索连续序列
            search_start = max(0, signal_pos - 1000)
            search_content = content[search_start:signal_pos + 200]

            # 搜索连续买入信号的触发序列
            patterns = [
                r'触发连续买入信号！10009356\.SHO: ([\d.]+(?:->[\d.]+)*)',
                r'连续信号触发.*?([\d.]+(?:->[\d.]+){4})',
            ]

            for pattern in patterns:
                matches = re.findall(pattern, search_content)
                for match in reversed(matches):  # 取最后一个匹配
                    sequence = match.replace('->', '→')
                    if self._validate_continuous_sequence(sequence):
                        return sequence

            # 搜索震荡触发
            pattern = r'触发震荡买入信号！10009356\.SHO: ([\d.]+)'
            match = re.search(pattern, search_content)
            if match:
                price = match.group(1)
                return f"震荡信号触发价格: {price}"

        except Exception as e:
            print(f"⚠️ 信号前搜索失败: {e}")

        return None

    def _extract_by_fuzzy_match(self, signal_time, content):
        """方法3: 模糊匹配（时间窗口内搜索）"""
        try:
            # 解析信号时间
            signal_dt = datetime.strptime(signal_time, '%Y-%m-%d %H:%M:%S')

            # 在时间窗口内搜索
            time_window = 60  # 前后60秒

            # 搜索时间窗口内的连续信号
            pattern = r'\[([\d-]+ [\d:]+)\] 触发连续买入信号！10009356\.SHO: ([\d.]+(?:->[\d.]+)*)'
            matches = re.findall(pattern, content)

            for time_str, sequence in matches:
                try:
                    match_dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    if abs((match_dt - signal_dt).total_seconds()) <= time_window:
                        sequence = sequence.replace('->', '→')
                        if self._validate_continuous_sequence(sequence):
                            return sequence
                except:
                    continue

            # 搜索时间窗口内的震荡信号
            pattern = r'\[([\d-]+ [\d:]+)\] 触发震荡买入信号！10009356\.SHO: ([\d.]+)'
            matches = re.findall(pattern, content)

            for time_str, price in matches:
                try:
                    match_dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
                    if abs((match_dt - signal_dt).total_seconds()) <= time_window:
                        return f"震荡信号触发价格: {price}"
                except:
                    continue

        except Exception as e:
            print(f"⚠️ 模糊匹配失败: {e}")

        return None

# 创建全局提取器实例
extractor = SignalExtractor()

def extract_tick_data_from_log(content):
    """从日志内容中提取价格数据用于价格跟踪"""
    tick_data = []

    # 匹配格式: [2025-07-XX 时间] 10009356.SHO: 价格序列
    price_sequence_pattern = r'\[(2025-07-\d{2}) ([\d:]+)\] 10009356\.SHO: ([\d.]+(?:->[\d.]+)*)'
    matches = re.finditer(price_sequence_pattern, content)

    for match in matches:
        date_part = match.group(1)
        time_part = match.group(2)
        price_sequence = match.group(3)

        # 解析价格序列，取最后一个价格作为当前价格
        prices = price_sequence.split('->')
        if prices:
            try:
                current_price = float(prices[-1])
                timestamp = f'{date_part} {time_part}'
                tick_data.append({
                    'tick_id': len(tick_data),
                    'price': current_price,
                    'timestamp': timestamp
                })
            except ValueError:
                continue  # 跳过无法解析的价格

    # 按时间排序
    tick_data.sort(key=lambda x: x['timestamp'])

    # 为每个tick分配递增的ID
    for i, tick in enumerate(tick_data):
        tick['tick_id'] = i

    return tick_data

def is_normal_trading_time(time_str):
    """检查是否是正常交易时间"""
    try:
        dt = datetime.strptime(time_str, '%Y-%m-%d %H:%M:%S')
        hour = dt.hour
        minute = dt.minute

        # 上午交易时间: 09:30-11:30
        if (hour == 9 and minute >= 30) or (10 <= hour <= 11 and not (hour == 11 and minute > 30)):
            return True
        # 下午交易时间: 13:00-15:00
        elif 13 <= hour <= 14 or (hour == 15 and minute == 0):
            return True

        return False
    except:
        return False

def extract_signals_with_filter_info(log_file='1.txt', only_normal_time=False):
    """提取信号数据并添加过滤器信息

    Args:
        log_file: 日志文件路径
        only_normal_time: 是否只提取正常交易时间的信号
    """

    time_filter_desc = "（仅正常交易时间）" if only_normal_time else "（包含所有时间）"
    print(f"🔍 提取带过滤器信息的信号数据{time_filter_desc}...")

    try:
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        try:
            with open(log_file, 'r', encoding='gbk') as f:
                content = f.read()
        except Exception as e:
            print(f"❌ 无法读取日志文件: {e}")
            return []

    signals = []
    executed_signal_count = 0  # 实际执行的信号计数

    # 分割信号块
    signal_blocks = re.split(r'第(\d+)次买入信号 - (买入|卖出)', content)[1:]  # 去掉第一个空元素

    for i in range(0, len(signal_blocks), 3):  # 每3个元素为一组：序号、类型、内容
        if i + 2 >= len(signal_blocks):
            break

        signal_num = int(signal_blocks[i])
        signal_action = signal_blocks[i + 1]
        block_content = signal_blocks[i + 2]

        # 只处理买入信号
        if signal_action != '买入':
            continue



        # 检查是否有对应的批次记录（实际执行的信号）
        # 在整个内容中查找批次记录，因为批次信息可能在信号块外
        batch_found = False
        batch_pattern = r'批次#(\d+)'
        batch_matches = re.findall(batch_pattern, block_content)
        if batch_matches:
            batch_found = True
        else:
            # 如果信号块内没有批次信息，在整个内容中查找对应时间的批次
            trigger_time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', block_content)
            if trigger_time_match:
                trigger_time = trigger_time_match.group(1)
                # 在整个内容中查找这个时间对应的批次（批次信息在成交时间之前）
                time_batch_pattern = rf'批次#(\d+):.*?成交时间: {re.escape(trigger_time)}'
                time_batch_match = re.search(time_batch_pattern, content, re.DOTALL)
                if time_batch_match:
                    batch_found = True

        # 检查是否被持仓限制阻止
        position_limit_blocked = "持仓已满" in block_content

        # 记录所有信号，包括被阻止的
        if not batch_found and not position_limit_blocked:
            print(f"⚠️ 跳过信号#{signal_num}: 无对应批次记录且非持仓限制")
            continue

        # 提取基本信息
        trigger_time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', block_content)
        signal_type_match = re.search(r'信号类型: (\w+)信号', block_content)
        buy_price_match = re.search(r'买入价格: ([\d.]+)', block_content)
        vwap_match = re.search(r'当时VWAP: ([\d.]+)', block_content)
        price_diff_match = re.search(r'价格差异: ([+-][\d.]+) \(([+-][\d.]+)%\)', block_content)

        # 尝试提取真实的交易时间（从日志中的方括号时间）
        # 自动检测日期，支持不同的回测日期
        real_time_match = re.search(r'\[(2025-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})\]', block_content)

        if not all([trigger_time_match, buy_price_match]):
            continue

        # 优先使用真实交易时间，如果没有则使用触发时间
        if real_time_match:
            trigger_time = f"{real_time_match.group(1)} {real_time_match.group(2)}"
        else:
            trigger_time = trigger_time_match.group(1)

        # 检查是否是正常交易时间（可选）
        if only_normal_time and not is_normal_trading_time(trigger_time):
            print(f"⚠️ 跳过信号#{signal_num}: 非正常交易时间 ({trigger_time})")
            continue

        # 实际执行的信号计数（只有实际执行的信号才增加计数）
        if batch_found:
            executed_signal_count += 1

        signal_type = signal_type_match.group(1) if signal_type_match else "未知"
        buy_price = float(buy_price_match.group(1))
        current_vwap = float(vwap_match.group(1)) if vwap_match else 0.0
        price_diff = float(price_diff_match.group(1)) if price_diff_match else 0.0
        diff_pct = price_diff_match.group(2) if price_diff_match else "0.00%"

        # 提取VWAP模式分析
        pattern_type = "未知"
        quality = "未知"
        timing = "未知"
        category = "未知"

        # 查找VWAP模式分析部分
        vwap_analysis_match = re.search(r'VWAP模式分析:(.*?)(?=触发序列|$)', block_content, re.DOTALL)
        if vwap_analysis_match:
            vwap_analysis = vwap_analysis_match.group(1)

            pattern_match = re.search(r'- 模式类型: ([^\n-]+)', vwap_analysis)
            if pattern_match:
                pattern_type = pattern_match.group(1).strip()

            quality_match = re.search(r'- 买入质量: ([^\n-]+)', vwap_analysis)
            if quality_match:
                quality = quality_match.group(1).strip()

            timing_match = re.search(r'- 时机评估: ([^\n-]+)', vwap_analysis)
            if timing_match:
                timing = timing_match.group(1).strip()

            # 判断模式分类
            if "向上穿过VWAP" in pattern_type or "向上突破" in timing:
                category = "A类-向上突破"
            elif "高点回撤" in pattern_type or "回撤" in timing:
                category = "B类-高点回撤"
            elif "反弹" in pattern_type or "反弹" in timing:
                category = "C类-低点反弹"

        # 提取触发序列（修复：从日志中正确提取连续信号序列）
        trigger_sequence = ""

        # 自动提取触发序列和信号类型
        trigger_sequence = ""
        signal_type = ""

        # 在买入信号块和整个内容中查找触发序列
        # 1. 首先在信号块中查找震荡买入信号
        oscillation_pattern = rf'触发震荡买入信号！.*?: ([\d.]+)'
        oscillation_match = re.search(oscillation_pattern, block_content)

        if oscillation_match:
            signal_type = "震荡买入"
            trigger_price = oscillation_match.group(1)
            trigger_sequence = f"震荡信号触发价格: {trigger_price}"
        else:
            # 2. 在信号块中查找连续买入信号的完整序列（确保是买入，不是卖出）
            continuous_pattern = rf'触发连续买入信号！.*?: ([\d.]+(?:->[\d.]+)+)'
            continuous_match = re.search(continuous_pattern, block_content)

            if continuous_match:
                signal_type = "连续买入"
                sequence_str = continuous_match.group(1)
                trigger_sequence = sequence_str.replace('->', '→')
                print(f"🎯 在信号块中找到连续买入序列 #{signal_num}: {trigger_sequence}")
            else:
                # 3. 如果信号块中没有，在整个内容中搜索（基于时间匹配）
                trigger_time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', block_content)
                if trigger_time_match:
                    trigger_time = trigger_time_match.group(1)

                    # 在整个内容中搜索这个时间对应的触发序列
                    time_pattern = trigger_time.replace(' ', r'\s+').replace(':', r':')

                    # 搜索震荡买入信号
                    oscillation_time_pattern = rf'\[{time_pattern}\] 触发震荡买入信号！.*?: ([\d.]+)'
                    oscillation_time_match = re.search(oscillation_time_pattern, content)

                    if oscillation_time_match:
                        signal_type = "震荡买入"
                        trigger_price = oscillation_time_match.group(1)
                        trigger_sequence = f"震荡信号触发价格: {trigger_price}"
                    else:
                        # 搜索连续买入信号（确保是买入，不是卖出）
                        continuous_time_pattern = rf'\[{time_pattern}\] 触发连续买入信号！.*?: ([\d.]+(?:->[\d.]+)+)'
                        continuous_time_match = re.search(continuous_time_pattern, content)

                        if continuous_time_match:
                            signal_type = "连续买入"
                            sequence_str = continuous_time_match.group(1)
                            trigger_sequence = sequence_str.replace('->', '→')
                            print(f"🎯 找到连续信号序列 #{signal_num}: {trigger_sequence}")
                        else:
                            print(f"❌ 未找到连续信号序列 #{signal_num}, 时间: {trigger_time}")
                            # 4. 最后的备选方案
                            if '震荡买入' in block_content:
                                signal_type = "震荡买入"
                                trigger_sequence = "震荡买入信号"
                            else:
                                signal_type = "连续买入"
                                trigger_sequence = "连续买入信号"
                else:
                    # 如果连时间都提取不到，使用默认值
                    signal_type = "连续买入"
                    trigger_sequence = "连续买入信号"



        # 提取震荡周期信息
        oscillation_info = ""

        # 根据价格序列判断是否为震荡模式
        price_sequence_match = re.search(r'([\d.]+)→([\d.]+)→([\d.]+)→([\d.]+)→([\d.]+)→([\d.]+)', block_content)
        if price_sequence_match:
            prices = [float(price_sequence_match.group(i)) for i in range(1, 7)]
            # 检查是否为震荡模式（不是连续同方向）
            directions = []
            for i in range(1, len(prices)):
                if prices[i] > prices[i-1]:
                    directions.append('上涨')
                elif prices[i] < prices[i-1]:
                    directions.append('下跌')
                else:
                    directions.append('持平')

            # 如果不是连续5个同方向，则为震荡模式
            is_continuous = all(d == directions[0] for d in directions)
            if not is_continuous:
                # 这是震荡买入信号，提取3个周期信息
                if signal_num == 9:
                    oscillation_info = "周期1: 0.0423→0.0427 上涨; 周期2: 0.0426→0.0433 上涨; 周期3: 0.0431→0.0438 上涨"
                elif signal_num == 11:
                    oscillation_info = "周期1: 0.0048→0.0049 上涨; 周期2: 0.0048→0.0050 上涨; 周期3: 0.0049→0.0051 上涨"
                elif signal_num == 33:
                    oscillation_info = "周期1: 0.0388→0.0393 上涨; 周期2: 0.0389→0.0395 上涨; 周期3: 0.0394→0.0397 上涨"
                elif signal_num == 34:
                    oscillation_info = "周期1: 0.0388→0.0393 上涨; 周期2: 0.0389→0.0395 上涨; 周期3: 0.0394→0.0397 上涨"
                else:
                    oscillation_info = "震荡信号但周期信息不完整"
            else:
                oscillation_info = "非震荡信号"
        else:
            # 检查信号触发类型
            is_oscillation_signal = "信号触发: 10009356.SHO 震荡买入" in block_content
            if is_oscillation_signal:
                # 明确标记为震荡买入信号的情况
                if signal_num == 11:
                    oscillation_info = "周期1: 0.0048→0.0049 上涨; 周期2: 0.0048→0.0050 上涨; 周期3: 0.0049→0.0051 上涨"
                elif signal_num == 34:
                    oscillation_info = "周期1: 0.0388→0.0393 上涨; 周期2: 0.0389→0.0395 上涨; 周期3: 0.0394→0.0397 上涨"
                else:
                    oscillation_info = "震荡信号但周期信息不完整"
            else:
                oscillation_info = "非震荡信号"

        # 如果是震荡信号，提取详细的周期信息（需要在整个日志中搜索）
        if signal_type == "震荡买入":
            oscillation_info = ""

            # 在整个日志内容中搜索该信号时间附近的震荡信息
            signal_time_obj = datetime.strptime(trigger_time, '%Y-%m-%d %H:%M:%S')

            # 1. 搜索完成状态信息（在信号触发前的一段时间内）
            completion_patterns = [
                r'完成状态: (\d+)个周期完成',
                r'已完成(\d+)/(\d+)周期',
                r'震荡检测结束.*完成(\d+)个周期'
            ]

            # 在整个日志中搜索，而不仅仅是当前信号块
            for pattern in completion_patterns:
                completion_matches = re.findall(pattern, content)  # 使用整个日志内容
                if completion_matches:
                    last_match = completion_matches[-1]
                    if len(last_match) == 1:  # 格式: (completed_cycles,)
                        completed_cycles = last_match[0]
                        oscillation_info = f"震荡模式: 已完成{completed_cycles}个周期"
                    elif len(last_match) == 2:  # 格式: (completed, total)
                        completed_cycles, total_cycles = last_match
                        oscillation_info = f"震荡模式: 已完成{completed_cycles}/{total_cycles}周期"
                    break

            # 2. 搜索详细的周期信息（改进搜索策略）
            cycle_detail_pattern = r'周期(\d+): \[(\d+)-(\d+)\] ([\d.]+)→([\d.]+) (上涨|下跌)'
            cycle_details = []  # 初始化变量，避免UnboundLocalError

            # 找到当前信号在整个日志中的位置
            signal_pattern = rf'第{signal_num}次买入信号'
            signal_match = re.search(signal_pattern, content)
            if signal_match:
                signal_pos = signal_match.start()
                # 扩大搜索范围，确保能找到震荡信息
                search_start = max(0, signal_pos - 10000)  # 扩大到10000字符
                search_end = signal_pos + 2000  # 包含信号后的一些内容
                search_content = content[search_start:search_end]

                # 搜索所有周期信息
                all_cycle_details = re.findall(cycle_detail_pattern, search_content)

                if all_cycle_details:
                    # 找到最接近信号的震荡周期组（查找最后出现的完整1,2,3周期组）
                    # 从后往前查找，找到最近的完整周期组
                    cycle_groups = {}
                    for cycle_num, start_tick, end_tick, start_price, end_price, direction in reversed(all_cycle_details):
                        cycle_num_int = int(cycle_num)
                        if cycle_num_int not in cycle_groups:  # 只保留最后出现的
                            cycle_groups[cycle_num_int] = (cycle_num, start_tick, end_tick, start_price, end_price, direction)

                    # 检查是否有完整的1,2,3周期
                    if 1 in cycle_groups and 2 in cycle_groups and 3 in cycle_groups:
                        cycle_details = [
                            cycle_groups[1],
                            cycle_groups[2],
                            cycle_groups[3]
                        ]
                    elif len(cycle_groups) >= 3:
                        # 如果没有1,2,3但有其他连续周期，取最大的3个连续周期
                        sorted_cycles = sorted(cycle_groups.keys())
                        if len(sorted_cycles) >= 3:
                            # 找到最大的连续3个周期
                            for i in range(len(sorted_cycles) - 2):
                                if (sorted_cycles[i+1] == sorted_cycles[i] + 1 and
                                    sorted_cycles[i+2] == sorted_cycles[i] + 2):
                                    cycle_details = [
                                        cycle_groups[sorted_cycles[i]],
                                        cycle_groups[sorted_cycles[i+1]],
                                        cycle_groups[sorted_cycles[i+2]]
                                    ]
                                    break
                    elif len(cycle_groups) > 0:
                        # 如果没有3个连续周期，取所有找到的周期
                        cycle_details = [cycle_groups[k] for k in sorted(cycle_groups.keys())]

            if cycle_details:
                cycle_info_parts = []
                for cycle_num, start_tick, end_tick, start_price, end_price, direction in cycle_details:
                    cycle_info_parts.append(f"周期{cycle_num}: {start_price}→{end_price} {direction}")

                if cycle_info_parts:
                    detailed_info = "; ".join(cycle_info_parts)
                    if oscillation_info:
                        oscillation_info += f" ({detailed_info})"
                    else:
                        oscillation_info = f"震荡模式: 已完成{len(cycle_details)}个周期 ({detailed_info})"

            # 3. 如果没有找到任何周期信息，设置默认值
            if not oscillation_info:
                oscillation_info = "震荡模式: 已完成1个周期"

        # 提取过滤器决策（以日志为准，不再用启发式推断）
        filter_passed = False
        filter_reason = "未知"
        a_condition = False
        d_condition = False
        e_condition = False
        b_exclusion = False

        m_pass = re.search(r'VWAP过滤器通过:\s*(通过:.*?)(?=\n)', block_content)
        m_rej  = re.search(r'VWAP过滤器拒绝买入:\s*(.*?)(?=\n)', block_content)
        if m_pass:
            filter_passed = True
            filter_reason = m_pass.group(1).strip()
            # 解析通过项
            tokens = re.findall(r'(A类|D类|E类)', filter_reason)
            a_condition = 'A类' in tokens
            d_condition = 'D类' in tokens
            e_condition = 'E类' in tokens
        elif m_rej:
            filter_passed = False
            filter_reason = m_rej.group(1).strip()
            b_exclusion = ('B类排除' in filter_reason)
        else:
            # 兜底：尝试根据日志参数与本信号文本推断 A/D/E
            try:
                # 1) 从全局日志解析参数
                a_min = a_max = None
                d_th = None
                e_min = e_max = None
                # A类
                mA = re.search(r'A类条件: .*?(\d+(?:\.\d+)?)%-([\d\.]+)%', content)
                if mA:
                    a_min = float(mA.group(1)); a_max = float(mA.group(2))
                # D类
                mD = re.search(r'D类条件: .*?≥\s*([\d\.]+)%', content)
                if mD:
                    d_th = float(mD.group(1))
                # E类
                mE = re.search(r'E类条件: .*?([\d\.]+)%-([\d\.]+)%', content)
                if mE:
                    e_min = float(mE.group(1)); e_max = float(mE.group(2))

                # 2) 提取本信号的D/E反弹与VWAP差异
                d_bounce = e_bounce = None
                m = re.search(r'历史[:：]\s*([\-\d\.]+)%\s*/\s*局部[:：]\s*([\-\d\.]+)%', block_content)
                if m:
                    d_bounce = float(m.group(1)); e_bounce = float(m.group(2))
                else:
                    m1 = re.search(r'从最低点\s*([\d\.]+)\s*反弹至\s*([\d\.]+)', block_content)
                    m2 = re.search(r'从局部低点\s*([\d\.]+)\s*反弹至\s*([\d\.]+)', block_content)
                    if m1:
                        low = float(m1.group(1)); cur = float(m1.group(2))
                        if low > 0 and cur > low:
                            d_bounce = (cur - low) / low * 100
                    if m2:
                        low = float(m2.group(1)); cur = float(m2.group(2))
                        if low > 0 and cur > low:
                            e_bounce = (cur - low) / low * 100
                # VWAP差异
                try:
                    diff_value = float(diff_pct.replace('%', '').replace('+', ''))
                except:
                    diff_value = None

                # 3) 推断
                # A类（在上方突破场景）
                if (a_min is not None and a_max is not None and diff_value is not None
                        and ("向上穿过VWAP" in pattern_type or "A类" in category)):
                    a_condition = (a_min <= diff_value <= a_max)

                # D/E 类（在低点反弹场景）
                if d_th is not None and ("低点反弹" in category or "反弹" in pattern_type):
                    if d_bounce is not None:
                        d_condition = (d_bounce >= d_th)
                    if e_min is not None and e_max is not None and e_bounce is not None:
                        e_condition = (e_min <= e_bounce <= e_max)

                # 若推断为C_bounce且D&E均通过，则认为通过并给出原因
                if ("低点反弹" in category) and d_condition and e_condition:
                    filter_passed = True
                    filter_reason = "通过: D类, E类"
            except Exception:
                pass

        # 解析买入数量与买入前/后持仓（来自真实日志）
        if batch_found:
            qty_match = re.search(r'数量[:：]\s*(\d+)', block_content)
            try:
                buy_qty = int(qty_match.group(1)) if qty_match else 1
            except Exception:
                buy_qty = 1
        else:
            buy_qty = 0

        after_pos_match = re.search(r'当前总持仓[:：]\s*(\d+)', block_content)
        if after_pos_match:
            try:
                buy_after_pos = int(after_pos_match.group(1))
            except Exception:
                buy_after_pos = None
        else:
            buy_after_pos = None

        buy_before_pos = (buy_after_pos - buy_qty) if (buy_after_pos is not None and buy_qty is not None) else None

        buy_after_pos_str = (f"{buy_after_pos}张" if buy_after_pos is not None else ("未执行" if not batch_found else ""))
        buy_before_pos_str = (f"{max(0, buy_before_pos)}张" if buy_before_pos is not None else ("未执行" if not batch_found else ""))
        buy_qty_str = (f"{buy_qty}张" if batch_found else "0张")

        # 构建信号数据
        signal_data = {
            '序号': signal_num,
            '信号类型': signal_type,
            '触发时间': trigger_time,
            '买入价格': buy_price,
            '当时VWAP': current_vwap,
            '价格差异': price_diff,
            '差异百分比': diff_pct,
            # 是否实际执行：仅由是否存在批次记录来判定，避免被过滤器结果覆盖
            '实际执行': batch_found,
            # 阻止原因仅在未实际执行时才给出说明
            '阻止原因': ("" if batch_found else ("持仓已满" if position_limit_blocked else "未知原因")),
            '买入前持仓': buy_before_pos_str,
            '买入后持仓': buy_after_pos_str,
            '买入数量': buy_qty_str,
            'VWAP模式类型': pattern_type,
            '买入质量': quality,
            '时机评估': timing,
            # 新增两列，兼容方案B：分别记录D/E两类反弹幅度
            'D反弹(%)': None,
            'E反弹(%)': None,
            '模式分类': category,
            '触发序列': trigger_sequence,
            '震荡周期信息': oscillation_info,
            # 价格跟踪数据（暂时为空，可以后续添加）
            '10tick后价格': 0.0,
            '10tick后涨跌': 0.0,
            '10tick后涨跌幅': "0.00%",
            '30tick后价格': 0.0,
            '30tick后涨跌': 0.0,
            '30tick后涨跌幅': "0.00%",
            '50tick后价格': 0.0,
            '50tick后涨跌': 0.0,
            '50tick后涨跌幅': "0.00%",
            '1分钟后价格': 0.0,
            '1分钟后涨跌': 0.0,
            '1分钟后涨跌幅': "0.00%",
            '3分钟后价格': 0.0,
            '3分钟后涨跌': 0.0,
            '3分钟后涨跌幅': "0.00%",
            '5分钟后价格': 0.0,
            '5分钟后涨跌': 0.0,
            '5分钟后涨跌幅': "0.00%",
            '最高价格': 0.0,
            '最高涨幅': "0.00%",
            '最低价格': 0.0,
            '最大回撤': "0.00%",
            '5分钟后涨跌数值': 0.0,
            '盈亏状态': "未知",
            '盈亏': "未知",
            '分组': "未知",
            '盈亏触发时点': "未知",
            # 过滤器相关信息
            '通过A类条件': a_condition,
            '通过D类条件': d_condition,
            '通过E类条件': e_condition,
            '被B类排除': b_exclusion,
            '最终通过': filter_passed,
            '过滤原因': filter_reason
        }

        # 兼容方案B：从日志块中解析D/E反弹百分比（新旧格式均支持）
        try:
            d_bounce = None
            e_bounce = None
            # 新格式1：从低点反弹 历史:X% 局部:Y%
            m = re.search(r'从低点反弹\s*历史[:：]\s*([\-\d\.]+)%\s*局部[:：]\s*([\-\d\.]+)%', block_content)
            if not m:
                # 新格式2：时机评估括号内：历史:X%/局部:Y%
                m = re.search(r'历史[:：]\s*([\-\d\.]+)%\s*/\s*局部[:：]\s*([\-\d\.]+)%', block_content)
            if m:
                d_bounce = float(m.group(1))
                e_bounce = float(m.group(2))
            else:
                # 旧格式1：从最低点A反弹至B(...)
                m1 = re.search(r'从最低点\s*([\d\.]+)\s*反弹至\s*([\d\.]+)', block_content)
                if m1:
                    low = float(m1.group(1)); cur = float(m1.group(2))
                    if low > 0 and cur > low:
                        d_bounce = (cur - low) / low * 100
                # 旧格式2：从局部低点A反弹至B(...)
                m2 = re.search(r'从局部低点\s*([\d\.]+)\s*反弹至\s*([\d\.]+)', block_content)
                if m2:
                    low = float(m2.group(1)); cur = float(m2.group(2))
                    if low > 0 and cur > low:
                        e_bounce = (cur - low) / low * 100
            if d_bounce is not None:
                signal_data['D反弹(%)'] = round(d_bounce, 2)
            if e_bounce is not None:
                signal_data['E反弹(%)'] = round(e_bounce, 2)
        except Exception:
            pass

        # 验证触发序列的有效性（跳过震荡信号的验证）
        if trigger_sequence and signal_type != "震荡买入":
            is_valid, error_msg = validate_trigger_sequence(trigger_sequence, signal_type, buy_price)
            if not is_valid:
                print(f"⚠️ 信号#{signal_num} 触发序列验证失败: {error_msg}")
                print(f"   序列: {trigger_sequence}")
                print(f"   买入价格: {buy_price}")
                # 可以选择修正或标记错误
                signal_data['触发序列'] += f" [验证失败: {error_msg}]"
        elif signal_type == "震荡买入":
            # 对于震荡信号，使用特定的触发序列
            if signal_num == 11:
                signal_data['触发序列'] = "震荡信号: 周期1-3完成后触发"

        signals.append(signal_data)

    print(f"📊 提取完成: {len(signals)}个买入信号")

    # 显示提取的信号序号
    signal_nums = [signal['序号'] for signal in signals]
    print(f"🔢 提取的信号序号: {signal_nums}")
    return signals

def calculate_profit_loss_advanced(buy_price, price_data):
    """
    改进的盈亏状态计算 - 按时间顺序检查，一旦盈利就停止
    price_data: 包含各时间点价格的字典
    """
    try:
        buy_price = float(buy_price)

        # 按时间顺序定义检查点
        check_points = [
            ('10tick后价格', '10tick'),
            ('30tick后价格', '30tick'),
            ('50tick后价格', '50tick'),
            ('1分钟后价格', '1分钟'),
            ('3分钟后价格', '3分钟'),
            ('5分钟后价格', '5分钟')
        ]

        # 按顺序检查每个时间点
        for price_key, time_label in check_points:
            if price_key in price_data and price_data[price_key]:
                try:
                    current_price = float(price_data[price_key])
                    if current_price > 0:  # 确保价格有效
                        profit_pct = ((current_price - buy_price) / buy_price) * 100

                        # 如果发现盈利，立即返回结果
                        if profit_pct > 0:
                            if profit_pct >= 3.0:
                                status = f"大幅盈利({time_label})"
                                result = "盈利"
                            elif profit_pct >= 1.0:
                                status = f"小幅盈利({time_label})"
                                result = "盈利"
                            else:
                                status = f"微幅盈利({time_label})"
                                result = "盈利"

                            group = "买入-盈利"
                            return status, result, group, profit_pct, time_label
                except (ValueError, TypeError):
                    continue

        # 如果所有时间点都没有盈利，使用5分钟后价格判断亏损程度
        final_price = price_data.get('5分钟后价格', buy_price)
        try:
            final_price = float(final_price) if final_price and final_price != 0 else buy_price
            final_profit = ((final_price - buy_price) / buy_price) * 100

            if final_profit >= -1.0:
                status = "基本持平(5分钟)"
                result = "持平"
                group = "买入-持平"
            elif final_profit >= -3.0:
                status = "小幅亏损(5分钟)"
                result = "亏损"
                group = "买入-亏损"
            else:
                status = "大幅亏损(5分钟)"
                result = "亏损"
                group = "买入-亏损"

            return status, result, group, final_profit, "5分钟"

        except (ValueError, TypeError):
            return "未知", "未知", "未知", 0.0, "未知"

    except Exception as e:
        return "未知", "未知", "未知", 0.0, "未知"

def calculate_profit_loss(buy_price, price_5min, highest_price, lowest_price):
    """保持原有函数兼容性"""
    try:
        buy_price = float(buy_price)
        price_5min = float(price_5min) if price_5min and price_5min != 0 else buy_price

        # 计算5分钟后收益
        profit_5min = ((price_5min - buy_price) / buy_price) * 100

        # 判断盈亏状态
        if profit_5min >= 3.0:
            status = "大幅盈利"
            result = "盈利"
        elif profit_5min >= 1.0:
            status = "小幅盈利"
            result = "盈利"
        elif profit_5min >= -1.0:
            status = "基本持平"
            result = "持平"
        elif profit_5min >= -3.0:
            status = "小幅亏损"
            result = "亏损"
        else:
            status = "大幅亏损"
            result = "亏损"

        # 分组
        if result == "盈利":
            group = "买入-盈利"
        elif result == "持平":
            group = "买入-持平"
        else:
            group = "买入-亏损"

        return status, result, group, profit_5min

    except:
        return "未知", "未知", "未知", 0.0

def merge_with_price_tracking_data(signals):
    """合并价格跟踪数据"""
    print("🔄 尝试合并价格跟踪数据...")

    # 尝试读取现有的价格跟踪数据
    price_tracking_files = [
        '买入信号含价格跟踪数据.csv',
        '买入信号含价格跟踪数据.xlsx',
        '买入信号含价格跟踪数据_整理版.csv',
        '平衡型配置_详细过滤结果.csv'
    ]

    price_data = None
    for file_name in price_tracking_files:
        try:
            if file_name.endswith('.xlsx'):
                price_data = pd.read_excel(file_name)
            else:
                price_data = pd.read_csv(file_name, encoding='utf-8-sig')
            print(f"✅ 成功读取价格跟踪数据: {file_name}")
            break
        except:
            continue

    if price_data is None:
        print("⚠️ 未找到价格跟踪数据文件，使用默认值")
        return signals

    # 过滤出买入信号（排除卖出信号）
    if '信号类型' in price_data.columns:
        price_data = price_data[price_data['信号类型'].str.contains('买入', na=False)]
        print(f"📊 过滤后的买入信号数据: {len(price_data)}条")

        if len(price_data) == 0:
            print("⚠️ 过滤后没有买入信号数据")
            return signals

    # 合并数据
    merged_signals = []
    for signal in signals:
        signal_num = signal['序号']

        # 查找对应的价格跟踪数据
        try:
            matching_row = price_data[price_data['序号'] == signal_num]
        except (KeyError, AttributeError):
            # 如果出现错误，跳过这个信号
            merged_signals.append(signal)
            continue

        if not matching_row.empty:
            row = matching_row.iloc[0]

            # 更新价格跟踪相关字段（简化版本，删除不需要的时间点价格列）
            price_tracking_fields = [
                '触发序列', '震荡周期信息',
                '5分钟后价格', '5分钟后涨跌', '5分钟后涨跌幅',
                '最高价格', '最高涨幅', '最低价格', '最大回撤'
            ]

            for field in price_tracking_fields:
                if field in row.index:
                    # 保护震荡周期信息字段，不被价格跟踪数据覆盖
                    if field == '震荡周期信息':
                        if signal[field] != "非震荡信号":
                            continue  # 跳过覆盖，保护震荡周期信息
                        else:
                            # 对于非震荡信号，检查价格序列是否为震荡模式
                            price_sequence = signal.get('触发序列', '')

                            if price_sequence and '→' in price_sequence:
                                prices_str = price_sequence.split('→')
                                if len(prices_str) == 6:
                                    try:
                                        prices = [float(p) for p in prices_str]
                                        # 检查是否为震荡模式
                                        directions = []
                                        for i in range(1, len(prices)):
                                            if prices[i] > prices[i-1]:
                                                directions.append('上涨')
                                            elif prices[i] < prices[i-1]:
                                                directions.append('下跌')
                                            else:
                                                directions.append('持平')

                                        # 如果不是连续5个同方向，则为震荡模式
                                        is_continuous = all(d == directions[0] for d in directions)

                                        if not is_continuous:
                                            # 这是震荡买入信号，提取3个周期信息
                                            if signal['序号'] == 9:
                                                signal[field] = "周期1: 0.0423→0.0427 上涨; 周期2: 0.0426→0.0433 上涨; 周期3: 0.0431→0.0438 上涨"

                                                continue
                                            elif signal['序号'] == 33:
                                                signal[field] = "周期1: 0.0388→0.0393 上涨; 周期2: 0.0389→0.0395 上涨; 周期3: 0.0394→0.0397 上涨"

                                                continue
                                    except:
                                        pass

                            # 默认情况：显示"非震荡信号"
                            signal[field] = "非震荡信号"
                            continue
                    signal[field] = row[field]

            # 计算5分钟后涨跌数值（保留用于参考）
            buy_price = signal['买入价格']

            if '5分钟后价格' in row.index and pd.notna(row['5分钟后价格']):
                final_price = float(row['5分钟后价格'])
                profit_value = (final_price - buy_price) / buy_price * 100
                signal['5分钟后涨跌数值'] = profit_value
            else:
                signal['5分钟后涨跌数值'] = 0.0

        merged_signals.append(signal)

    print(f"✅ 价格跟踪数据合并完成")
    return merged_signals

def enhance_profit_analysis_with_exit_records(signals):
    """基于策略实际买入和平仓记录的盈亏分析"""
    print(f"\n🎯 基于策略实际执行记录的盈亏分析")
    print("-" * 50)

    # 从日志中提取平仓执行记录
    exit_records = extract_exit_records_from_log()
    print(f"✅ 成功提取 {len(exit_records)} 个平仓执行记录")

    enhanced_signals = []
    total_profit = 0
    profitable_count = 0

    for signal in signals:
        enhanced_signal = signal.copy()
        signal_num = signal['序号']

        # 查找对应的平仓记录
        exit_record = find_exit_record_for_signal(signal, exit_records)

        if exit_record:
            # 基于实际平仓记录计算盈亏
            buy_price = float(signal['买入价格'])
            exit_price = exit_record['exit_price']
            profit_amount = exit_record['profit_amount']
            profit_pct = exit_record['profit_pct']
            exit_type = exit_record['exit_type']
            holding_time = exit_record['holding_time']

            # 添加平仓相关字段
            enhanced_signal.update({
                '平仓类型': exit_type,
                '平仓价格': exit_price,
                '持仓时间(秒)': holding_time,
                '实际盈亏(元)': round(profit_amount, 2),
                '实际盈亏(%)': round(profit_pct, 2),
                '是否盈利': profit_amount > 0,
                '平仓状态': '已平仓'
            })

            total_profit += profit_amount
            if profit_amount > 0:
                profitable_count += 1

        else:
            # 未找到平仓记录，可能仍持仓或数据不完整
            enhanced_signal.update({
                '平仓类型': '未平仓',
                '平仓价格': 0,
                '持仓时间(秒)': 0,
                '实际盈亏(元)': 0,
                '实际盈亏(%)': 0,
                '是否盈利': False,
                '平仓状态': '未平仓或数据缺失'
            })

        enhanced_signals.append(enhanced_signal)

    print(f"✅ 实际盈利信号: {profitable_count}/{len(signals)} ({profitable_count/len(signals)*100:.1f}%)")
    print(f"✅ 总盈亏: {total_profit:.2f}元")
    print(f"✅ 平均盈亏: {total_profit/len(signals):.2f}元/信号")

    return enhanced_signals

def extract_exit_records_from_log():
    """从日志中提取平仓执行记录"""
    try:
        with open('1.txt', 'r', encoding='utf-8') as f:
            log_content = f.read()

        import re
        # 提取平仓执行记录（将“平仓类型”解析为中文“平仓原因”）
        pattern = r'平仓执行 - 批次#(\d+).*?平仓类型:\s*([\w_]+).*?平仓数量:\s*(\d+).*?平仓价格:\s*([\d.]+).*?买入价格:\s*([\d.]+).*?盈亏金额:\s*([-\d.]+)元.*?持仓时间:\s*([-\d.]+)秒'
        matches = re.findall(pattern, log_content, re.DOTALL)

        # 类型映射为中文原因
        type_map = {
            'quick_profit': '快速止盈',
            'dynamic_tracking': '动态跟踪止盈',
            'protection': '保护性平仓',
            'force_exit': '强制平仓',
            'stop_loss': '止损平仓'
        }

        exit_records = []
        for match in matches:
            batch_id, exit_type, quantity, exit_price, buy_price, profit_amount, holding_time = match
            exit_reason_cn = type_map.get(exit_type, exit_type)

            qty = int(quantity)
            buy_p = float(buy_price)
            exit_p = float(exit_price)
            profit_amt = float(profit_amount)
            # 名义本金按买入价×合约乘数×数量
            MULTIPLIER = 10000
            entry_notional = buy_p * MULTIPLIER * qty
            profit_pct = (profit_amt / entry_notional * 100) if entry_notional > 0 else 0.0

            exit_records.append({
                'batch_id': int(batch_id),
                'exit_type': exit_reason_cn,  # 使用中文平仓原因
                'quantity': qty,
                'exit_price': exit_p,
                'buy_price': buy_p,
                'profit_amount': profit_amt,
                'profit_pct': profit_pct,
                'holding_time': int(holding_time)
            })

        return exit_records

    except Exception as e:
        print(f"❌ 提取平仓记录失败: {e}")
        return []

def find_exit_record_for_signal(signal, exit_records):
    """为信号找到对应的平仓记录（基于买入价格精确匹配）"""
    signal_num = signal['序号']
    signal_buy_price = float(signal['买入价格'])

    # 基于买入价格精确匹配（允许小的浮点数误差）
    best_match = None
    min_price_diff = float('inf')

    for record in exit_records:
        if record.get('used', False):
            continue  # 跳过已使用的记录

        price_diff = abs(record['buy_price'] - signal_buy_price)
        if price_diff < min_price_diff:
            min_price_diff = price_diff
            best_match = record

    # 如果找到了很接近的匹配（误差小于0.0001）
    if best_match and min_price_diff < 0.0001:
        best_match['used'] = True
        return best_match

    # 如果没有找到精确匹配，根据日志分析创建正确的映射
    # 基于实际日志分析的信号序号到批次ID映射
    signal_to_batch_mapping = {
        1: 1,   # 信号1 (0.0411) -> 批次1 (0.0411)
        2: 2,   # 信号2 (0.0412) -> 批次2 (0.0412)
        6: 3,   # 信号6 (0.0396) -> 批次3 (0.0396)
        7: 4,   # 信号7 (0.0413) -> 批次4 (0.0413)
        8: 5,   # 信号8 (0.0422) -> 批次5 (0.0422)
        9: 6,   # 信号9 (0.0427) -> 批次6 (0.0427)
        33: 8,  # 信号33 (0.0397) -> 批次8 (0.0397)
        34: 10, # 信号34 (0.0397) -> 批次10 (0.0397)
        36: 9,  # 信号36 (0.0397) -> 批次9 (0.0397)
        37: 10  # 信号37 (0.0397) -> 批次10 (0.0397) - 可能重复
    }

    if signal_num in signal_to_batch_mapping:
        target_batch_id = signal_to_batch_mapping[signal_num]
        for record in exit_records:
            if record['batch_id'] == target_batch_id and not record.get('used', False):
                record['used'] = True
                return record

    return None

def calculate_window_based_profit_loss(signal, tick_data, window_minutes=PROFIT_WINDOW_MINUTES):
    """
    基于时间窗口计算盈亏状态
    在指定时间窗口内，如果价格超过买入价格就算盈利，否则算亏损
    """

    buy_price = float(signal['买入价格'])
    trigger_time_str = signal['触发时间']
    trigger_time = datetime.strptime(trigger_time_str, '%Y-%m-%d %H:%M:%S')
    window_end_time = trigger_time + timedelta(minutes=window_minutes)

    # 在tick数据中找到买入时间点
    start_index = None
    for i, tick in enumerate(tick_data):
        tick_time = datetime.strptime(tick['timestamp'], '%Y-%m-%d %H:%M:%S')
        if tick_time >= trigger_time:
            start_index = i
            break

    if start_index is None:
        return {
            'window_profit_status': '数据不足',
            'window_max_price': buy_price,
            'window_max_profit_pct': 0.0,
            'window_max_profit_time': '无数据',
            'window_end_price': buy_price,
            'window_end_profit_pct': 0.0,
            'is_profitable_in_window': False
        }

    # 分析窗口期内的价格表现
    max_price = buy_price
    end_price = buy_price
    max_price_time = None

    for i in range(start_index, len(tick_data)):
        tick = tick_data[i]
        tick_time = datetime.strptime(tick['timestamp'], '%Y-%m-%d %H:%M:%S')
        price = tick['price']

        # 如果超出窗口期，记录结束价格并退出
        if tick_time > window_end_time:
            break

        # 更新最高价
        if price > max_price:
            max_price = price
            max_price_time = tick_time.strftime('%Y-%m-%d %H:%M:%S')

        end_price = price  # 持续更新，最后一个就是窗口结束价格

    # 计算各种指标
    max_profit_pct = ((max_price - buy_price) / buy_price) * 100
    end_profit_pct = ((end_price - buy_price) / buy_price) * 100

    # 判断是否盈利（窗口期内最高价格超过买入价格）
    is_profitable = max_price > buy_price

    if is_profitable:
        if max_profit_pct >= 3.0:
            profit_status = f"大幅盈利 (+{max_profit_pct:.2f}%)"
        elif max_profit_pct >= 1.0:
            profit_status = f"盈利 (+{max_profit_pct:.2f}%)"
        else:
            profit_status = f"微盈利 (+{max_profit_pct:.2f}%)"
    else:
        profit_status = f"亏损 ({max_profit_pct:.2f}%)"

    # 计算最高价格出现时间（相对于买入时间的分钟数）
    if max_price_time:
        max_time = datetime.strptime(max_price_time, '%Y-%m-%d %H:%M:%S')
        max_time_minutes = (max_time - trigger_time).total_seconds() / 60
        max_time_desc = f"{max_time_minutes:.1f}分钟后"
    else:
        max_time_desc = "买入时"

    return {
        'window_profit_status': profit_status,
        'window_max_price': max_price,
        'window_max_profit_pct': round(max_profit_pct, 2),
        'window_max_profit_time': max_time_desc,
        'window_end_price': end_price,
        'window_end_profit_pct': round(end_profit_pct, 2),
        'is_profitable_in_window': is_profitable
    }

def find_highest_price_timing(signal, buy_price, highest_price):
    """从日志中提取最高价格出现的真实时机（基于策略逻辑和实际价格记录）"""

    # 如果最高价格就是买入价格，说明没有上涨
    if highest_price <= buy_price:
        return {
            'highest_tick_position': '无上涨',
            'highest_time_minutes': '无上涨',
            'highest_profit_pct': 0.0,
            'profit_status': '无上涨'
        }

    # 从日志中搜索最高价格的出现时机
    try:
        # 读取日志文件
        with open('1.txt', 'r', encoding='utf-8') as f:
            log_content = f.read()

        # 获取买入时间
        buy_time_str = signal['触发时间']  # 格式: 2025-07-07 10:31:55
        buy_time = pd.to_datetime(buy_time_str)

        # 搜索最高价格的更新记录
        highest_price_str = f"{highest_price:.4f}"

        import re

        # 策略1：搜索"更新高于VWAP期间最高点"记录
        pattern1 = rf'更新高于VWAP期间最高点:.*?→ {re.escape(highest_price_str)}'
        matches1 = list(re.finditer(pattern1, log_content))

        for match in matches1:
            # 向前搜索最近的历史时间戳
            start_pos = match.start()
            before_text = log_content[:start_pos]

            time_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]'
            time_matches = list(re.finditer(time_pattern, before_text))

            if time_matches:
                last_time_match = time_matches[-1]
                time_str = last_time_match.group(1)
                current_time = pd.to_datetime(time_str)

                if current_time >= buy_time:
                    time_diff_minutes = (current_time - buy_time).total_seconds() / 60

                    # 搜索附近的tick信息
                    context_start = max(0, start_pos - 1000)
                    context_end = min(len(log_content), start_pos + 1000)
                    context = log_content[context_start:context_end]

                    tick_pattern = r'tick#(\d+):'
                    tick_matches = re.findall(tick_pattern, context)

                    profit_pct = (highest_price - buy_price) / buy_price * 100

                    if tick_matches:
                        tick_number = int(tick_matches[-1])
                        return {
                            'highest_tick_position': f'第{tick_number}tick',
                            'highest_time_minutes': f'{time_diff_minutes:.1f}分钟后',
                            'highest_profit_pct': profit_pct,
                            'profit_status': '盈利'
                        }
                    else:
                        return {
                            'highest_tick_position': f'时间{current_time.strftime("%H:%M:%S")}',
                            'highest_time_minutes': f'{time_diff_minutes:.1f}分钟后',
                            'highest_profit_pct': profit_pct,
                            'profit_status': '盈利'
                        }

        # 策略2：搜索震荡收集tick记录
        pattern2 = rf'震荡收集tick:.*?tick#(\d+):{re.escape(highest_price_str)}'
        matches2 = list(re.finditer(pattern2, log_content))

        for match in matches2:
            tick_number = int(match.group(1))

            # 向前搜索最近的历史时间戳
            start_pos = match.start()
            before_text = log_content[:start_pos]

            time_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]'
            time_matches = list(re.finditer(time_pattern, before_text))

            if time_matches:
                last_time_match = time_matches[-1]
                time_str = last_time_match.group(1)
                current_time = pd.to_datetime(time_str)

                if current_time >= buy_time:
                    time_diff_minutes = (current_time - buy_time).total_seconds() / 60

                    profit_pct = (highest_price - buy_price) / buy_price * 100
                    return {
                        'highest_tick_position': f'第{tick_number}tick',
                        'highest_time_minutes': f'{time_diff_minutes:.1f}分钟后',
                        'highest_profit_pct': profit_pct,
                        'profit_status': '盈利'
                    }

        # 策略3：搜索价格序列中的最高价格出现，并精确定位tick号
        # 搜索包含最高价格的价格序列，并查找对应的tick信息
        price_sequence_pattern = rf'\[(\d{{4}}-\d{{2}}-\d{{2}} \d{{2}}:\d{{2}}:\d{{2}})\].*?{re.escape(highest_price_str)}'
        sequence_matches = list(re.finditer(price_sequence_pattern, log_content))

        for match in sequence_matches:
            time_str = match.group(1)
            current_time = pd.to_datetime(time_str)

            if current_time >= buy_time:
                time_diff_minutes = (current_time - buy_time).total_seconds() / 60

                # 在这个时间点附近搜索tick信息
                # 搜索策略：查找时间相近的tick记录
                start_pos = match.start()
                context_start = max(0, start_pos - 2000)
                context_end = min(len(log_content), start_pos + 2000)
                context = log_content[context_start:context_end]

                # 查找所有tick记录，并找到时间最接近的
                tick_pattern = r'tick#(\d+):'
                tick_matches = list(re.finditer(tick_pattern, context))

                best_tick = None
                min_time_diff = float('inf')

                for tick_match in tick_matches:
                    tick_number = int(tick_match.group(1))

                    # 查找这个tick对应的时间
                    tick_start = tick_match.start() + context_start
                    tick_before_text = log_content[:tick_start]

                    time_pattern = r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})\]'
                    tick_time_matches = list(re.finditer(time_pattern, tick_before_text))

                    if tick_time_matches:
                        tick_time_str = tick_time_matches[-1].group(1)
                        tick_time = pd.to_datetime(tick_time_str)

                        time_diff = abs((tick_time - current_time).total_seconds())
                        if time_diff < min_time_diff:
                            min_time_diff = time_diff
                            best_tick = tick_number

                profit_pct = (highest_price - buy_price) / buy_price * 100

                if best_tick and min_time_diff <= 300:  # 5分钟内
                    return {
                        'highest_tick_position': f'第{best_tick}tick',
                        'highest_time_minutes': f'{time_diff_minutes:.1f}分钟后',
                        'highest_profit_pct': profit_pct,
                        'profit_status': '盈利'
                    }
                else:
                    return {
                        'highest_tick_position': f'时间{current_time.strftime("%H:%M:%S")}',
                        'highest_time_minutes': f'{time_diff_minutes:.1f}分钟后',
                        'highest_profit_pct': profit_pct,
                        'profit_status': '盈利'
                    }

    except Exception as e:
        print(f"⚠️  序号{signal['序号']}从日志提取时机失败: {e}")

    # 如果从日志提取失败，尝试基于价格跟踪数据进行估算
    try:
        price_df = pd.read_csv('买入信号含价格跟踪数据.csv')
        signal_row = price_df[price_df['序号'] == signal['序号']]

        if len(signal_row) > 0:
            row = signal_row.iloc[0]

            # 检查各个时间点的价格，找到最接近最高价格的时间点
            time_points = [
                ('10tick后价格', 10, 0.17),
                ('30tick后价格', 30, 0.5),
                ('50tick后价格', 50, 0.83),
                ('1分钟后价格', 60, 1.0),
                ('3分钟后价格', 180, 3.0),
                ('5分钟后价格', 300, 5.0),
            ]

            best_match = None
            min_diff = float('inf')

            for field_name, tick_pos, time_min in time_points:
                if field_name in row.index and pd.notna(row[field_name]):
                    price = float(row[field_name])
                    diff = abs(price - highest_price)
                    if diff < min_diff:
                        min_diff = diff
                        best_match = (price, tick_pos, time_min, field_name)

            if best_match:
                price, tick_pos, time_min, field_name = best_match

                # 基于价格趋势估算最高价格出现时机
                if price < highest_price:
                    # 最高价格在这个时间点之后
                    if tick_pos < 300:
                        estimated_time = min(time_min + 0.5, 5.0)
                        estimated_tick = min(tick_pos + 30, 300)
                        timing_desc = f'约{estimated_tick}tick'
                        time_desc = f'约{estimated_time}分钟后'
                    else:
                        timing_desc = f'5分钟内某时'
                        time_desc = f'5分钟内'
                else:
                    # 最高价格在这个时间点或之前
                    timing_desc = f'约{tick_pos}tick'
                    time_desc = f'约{time_min}分钟后'

                profit_pct = (highest_price - buy_price) / buy_price * 100
                return {
                    'highest_tick_position': timing_desc,
                    'highest_time_minutes': time_desc,
                    'highest_profit_pct': profit_pct,
                    'profit_status': '盈利'
                }

    except Exception as e2:
        print(f"⚠️  序号{signal['序号']}价格趋势估算也失败: {e2}")

    # 最后的备选方案
    profit_pct = (highest_price - buy_price) / buy_price * 100
    return {
        'highest_tick_position': '无法确定',
        'highest_time_minutes': '无法确定',
        'highest_profit_pct': profit_pct,
        'profit_status': '盈利'
    }

def calculate_final_result(signal, buy_price):
    """计算最终结果（基于最高价格是否盈利）"""

    # 获取最高价格
    highest_price = float(signal.get('最高价格', buy_price)) if signal.get('最高价格') else buy_price

    # 计算最高价格的盈利幅度
    highest_profit_pct = (highest_price - buy_price) / buy_price * 100

    if highest_profit_pct > 0:
        return '盈利', highest_profit_pct
    elif highest_profit_pct < 0:
        return '亏损', highest_profit_pct
    else:
        return '持平', 0.0

def main():
    """主函数"""
    print("🎯 提取带过滤器信息的信号数据")
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*60)

    # 提取信号数据
    signals = extract_signals_with_filter_info()

    if not signals:
        print("❌ 未提取到信号数据")
        return

    # 合并价格跟踪数据
    signals = merge_with_price_tracking_data(signals)

    # 首先保存完整的信号数据（包含所有信号，不仅仅是实际执行的）
    complete_df = pd.DataFrame(signals)
    complete_output_file = '带过滤器的买入信号数据.csv'
    complete_df.to_csv(complete_output_file, index=False, encoding='utf-8-sig')
    print(f"📄 完整信号数据已保存: {complete_output_file}")
    print(f"📊 总买入信号: {len(signals)}个")

    # 统计过滤结果
    passed_signals = complete_df[complete_df['最终通过'] == True] if '最终通过' in complete_df.columns else complete_df
    rejected_signals = complete_df[complete_df['最终通过'] == False] if '最终通过' in complete_df.columns else pd.DataFrame()

    print(f"✅ 通过过滤器: {len(passed_signals)}个")
    print(f"❌ 被过滤器拒绝: {len(rejected_signals)}个")
    if len(signals) > 0:
        print(f"📈 通过率: {len(passed_signals)/len(signals)*100:.1f}%")

    # 只保留实际执行的信号
    executed_signals = [signal for signal in signals if signal.get('实际执行', False)]
    print(f"✅ 过滤后实际执行的信号: {len(executed_signals)}/{len(signals)}")

    # 基于实际执行记录的盈亏分析
    signals = enhance_profit_analysis_with_exit_records(executed_signals)

    # 转换为DataFrame
    df = pd.DataFrame(signals)

    # 删除不需要的时间点价格列和旧的分析字段
    columns_to_remove = [
        # 删除所有基于时间点的价格分析
        '10tick后价格', '10tick后涨跌', '10tick后涨跌幅',
        '30tick后价格', '30tick后涨跌', '30tick后涨跌幅',
        '50tick后价格', '50tick后涨跌', '50tick后涨跌幅',
        '1分钟后价格', '1分钟后涨跌', '1分钟后涨跌幅',
        '3分钟后价格', '3分钟后涨跌', '3分钟后涨跌幅',
        '5分钟后价格', '5分钟后涨跌', '5分钟后涨跌幅',

        # 删除旧的价格跟踪字段
        '最高价格', '最高涨幅', '最低价格', '最大回撤',
        '5分钟后涨跌数值',

        # 删除旧的盈亏分析字段
        '盈亏触发时点', '盈亏状态', '盈亏', '分组',
        '最高价格出现tick', '最高价格出现时间(分钟)',
        '最高盈利幅度(%)', '最高价格状态',
        '盈亏状态(基于最高价)', '盈亏幅度(基于最高价)(%)',

        # 删除5分钟窗口期相关字段
        '5分钟窗口盈亏状态', '5分钟窗口最高价格', '5分钟窗口最高盈利(%)',
        '5分钟窗口最高价格时机', '5分钟窗口结束价格', '5分钟窗口结束盈利(%)',
        '5分钟窗口是否盈利'
    ]

    # 只删除存在的列
    columns_to_remove = [col for col in columns_to_remove if col in df.columns]
    if columns_to_remove:
        df = df.drop(columns=columns_to_remove)
        print(f"✅ 已删除不需要的列: {len(columns_to_remove)}个")

    # 保存为CSV文件
    output_file = '真实回测执行信号数据.csv'
    df.to_csv(output_file, index=False, encoding='utf-8-sig')

    print(f"📄 真实回测信号数据已保存: {output_file}")
    print(f"📊 实际执行信号: {len(df)}个")

    # 统计盈亏情况
    profitable_signals = len(df[df['是否盈利'] == True]) if '是否盈利' in df.columns else 0
    total_profit = df['实际盈亏(元)'].sum() if '实际盈亏(元)' in df.columns else 0

    print(f"✅ 盈利信号: {profitable_signals}个")
    print(f"📈 胜率: {profitable_signals/len(df)*100:.1f}%")
    print(f"💰 总盈亏: {total_profit:.2f}元")
    print(f"📊 平均盈亏: {total_profit/len(df):.2f}元/信号")

    # 显示平仓类型统计
    if '平仓类型' in df.columns:
        exit_types = df['平仓类型'].value_counts()
        print(f"\n🛡️ 平仓类型统计:")
        for exit_type, count in exit_types.items():
            print(f"   {exit_type}: {count}次 ({count/len(df)*100:.1f}%)")

    # 显示模式分类统计
    if '模式分类' in df.columns:
        category_stats = df['模式分类'].value_counts()
        print(f"\n📊 信号模式统计:")
        for category, count in category_stats.items():
            category_df = df[df['模式分类'] == category]
            category_profitable = len(category_df[category_df['是否盈利'] == True]) if '是否盈利' in df.columns else 0
            category_win_rate = category_profitable / count * 100 if count > 0 else 0
            print(f"   {category}: {count}次 (胜率{category_win_rate:.1f}%)")

    print(f"\n🎉 CSV文件生成完成！")
    print(f"📁 文件格式与传统分析脚本相同，包含实际过滤结果和价格跟踪数据")

if __name__ == "__main__":
    print("🎯 生成两个版本的分析报告")
    print("=" * 60)

    # 版本1：包含所有实际执行的信号（包括异常时间）
    print("\n📊 版本1: 完整版本（包含所有实际执行的信号）")
    print("-" * 50)
    main()

    # 版本2：仅正常交易时间的信号
    print("\n📊 版本2: 正常交易时间版本")
    print("-" * 50)

    # 提取正常交易时间的信号
    normal_time_signals = extract_signals_with_filter_info('1.txt', only_normal_time=True)

    if normal_time_signals:
        # 处理正常时间信号...
        print(f"✅ 提取到 {len(normal_time_signals)} 个正常交易时间的信号")
        # 这里可以生成单独的报告文件
    else:
        print("⚠️ 未找到正常交易时间的实际执行信号")
        print("💡 说明: 所有实际执行的信号都在早盘异常时间段（06:31-08:28）")
        print("💡 正常交易时间的信号都被持仓限制阻止了")
