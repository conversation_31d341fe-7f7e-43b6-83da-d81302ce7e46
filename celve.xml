<?xml version="1.0" encoding="utf-8"?>
<TCStageLayout>
    <control note="控件">
        <variable note="控件">
            <!-- 原有策略参数 -->
            <item position="" bind="underlying_code" value="510300.SH" note="标的代码(如510300.SH)" name="标的代码" type="intput"/>
            <item position="" bind="signal_threshold" value="5" note="连续同方向tick数量(如5表示连续5个上涨或下跌触发信号)" name="连续信号阈值" type="intput"/>
            <item position="" bind="oscillation_period_size" value="5" note="每个震荡周期的tick数量" name="震荡周期大小" type="intput"/>
            <item position="" bind="oscillation_periods" value="3" note="需要的连续同方向周期数量" name="震荡周期数量" type="intput"/>
            <item position="" bind="min_days_to_expire" value="7" note="合约最少剩余天数(少于此天数选择下月合约)" name="最少剩余天数" type="intput"/>
            <item position="" bind="max_position_per_contract" value="1" note="单个合约最大持仓量(张)" name="单合约最大持仓" type="intput"/>
            <item position="" bind="order_timeout_seconds" value="5" note="委托超时时间(秒)" name="委托超时时间" type="intput"/>
            <item position="" bind="enable_real_trading" value="1" note="启用真实交易(1开0关,谨慎使用)" name="真实交易开关" type="intput"/>

            <!-- VWAP过滤器参数 -->
            <item position="" bind="vwap_filter_enabled" value="1" note="VWAP过滤器总开关(1开0关)" name="VWAP过滤器" type="intput"/>
            <item position="" bind="vwap_a_enabled" value="1" note="A类条件开关(向上穿过VWAP,1开0关)" name="A类条件" type="intput"/>
            <item position="" bind="vwap_a_min" value="0.3" note="A类最小百分比(高于VWAP的最小百分比)" name="A类最小%" type="intput"/>
            <item position="" bind="vwap_a_max" value="8.0" note="A类最大百分比(高于VWAP的最大百分比)" name="A类最大%" type="intput"/>
            <item position="" bind="vwap_b_enabled" value="1" note="B类排除开关(高点回撤排除,1开0关)" name="B类排除" type="intput"/>
            <item position="" bind="vwap_b_threshold" value="2.0" note="B类回撤阈值(回撤超过此百分比则排除)" name="B类阈值%" type="intput"/>
            <item position="" bind="vwap_d_enabled" value="1" note="D类条件开关(低点反弹,1开0关)" name="D类条件" type="intput"/>
            <item position="" bind="vwap_d_threshold" value="0.5" note="D类反弹阈值(反弹超过此百分比才通过)" name="D类阈值%" type="intput"/>
            <item position="" bind="vwap_e_enabled" value="0" note="E类条件开关(上一低点反弹,1开0关)" name="E类条件" type="intput"/>
            <item position="" bind="vwap_e_threshold" value="2.0" note="E类反弹阈值(反弹超过此百分比才通过)" name="E类阈值%" type="intput"/>
            <item position="" bind="vwap_debug_log" value="1" note="VWAP过滤器调试日志(1开0关)" name="VWAP调试" type="intput"/>
        </variable>
    </control>
</TCStageLayout>
