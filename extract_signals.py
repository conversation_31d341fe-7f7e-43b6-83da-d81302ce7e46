#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
QMT买入信号数据提取脚本
从日志文件中提取所有151次买入信号的详细数据并生成Excel文件
"""

import re
import pandas as pd
from datetime import datetime, timed<PERSON><PERSON>

def extract_signal_data(log_file_path):
    """从日志文件中提取买入信号数据，包括买入后的价格变化"""

    with open(log_file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # 首先提取所有tick数据用于价格跟踪
    tick_data = extract_tick_data(content)

    # 查找所有买入信号的起始位置 - 改进的正则表达式
    signal_pattern = r'第(\d+)次买入信号 - (.*?)(?:\n|$)'
    signals = re.finditer(signal_pattern, content)

    signal_data = []

    for match in signals:
        signal_num = int(match.group(1))
        signal_type = match.group(2)
        start_pos = match.start()

        # 找到下一个信号的位置或文件结束
        next_match = None
        for next_signal in re.finditer(signal_pattern, content[start_pos + 1:]):
            next_match = next_signal
            break

        if next_match:
            end_pos = start_pos + next_match.start() + 1
        else:
            end_pos = len(content)

        # 扩展搜索范围：向前搜索震荡周期信息
        # 查找前面最近的震荡信号触发信息
        extended_start = max(0, start_pos - 2000)  # 向前搜索2000字符
        extended_text = content[extended_start:end_pos]

        # 查找震荡信号触发的时间，确保是同一个信号
        signal_time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', content[start_pos:end_pos])
        best_oscillation_text = ""
        if signal_time_match:
            signal_time = signal_time_match.group(1)
            # 在扩展文本中查找对应时间的震荡信号
            oscillation_pattern = rf'震荡信号触发.*?买入.*?(?=第\d+次买入信号|$)'
            oscillation_matches = re.finditer(oscillation_pattern, extended_text, re.DOTALL)

            # 找到最接近当前信号时间的震荡信息
            for osc_match in oscillation_matches:
                osc_text = osc_match.group(0)
                # 检查是否包含周期信息
                if '周期1:' in osc_text and '周期2:' in osc_text and '周期3:' in osc_text:
                    best_oscillation_text = osc_text
                    break

        # 合并震荡信息和信号文本
        if best_oscillation_text:
            signal_text = best_oscillation_text + "\n" + content[start_pos:end_pos]
        else:
            signal_text = content[start_pos:end_pos]

        # 提取详细信息
        data = extract_signal_details(signal_text, signal_num, signal_type)
        if data:
            # 添加买入后价格变化分析
            add_price_tracking(data, tick_data)
            signal_data.append(data)

    return signal_data

def extract_tick_data(content):
    """从日志内容中提取价格数据用于价格跟踪"""
    tick_data = []

    # 更新匹配格式: 支持动态日期匹配 [2025-07-XX 时间] 10009356.SHO: 价格序列
    price_sequence_pattern = r'\[(2025-07-\d{2}) ([\d:]+)\] 10009356\.SHO: ([\d.]+(?:->[\d.]+)*)'
    matches = re.finditer(price_sequence_pattern, content)

    for match in matches:
        date_part = match.group(1)
        time_part = match.group(2)
        price_sequence = match.group(3)
        timestamp = f'{date_part} {time_part}'

        # 提取序列中的最后一个价格（当前价格）
        # 处理带有方向指示符的格式，如: 0.0348->0.0360 [↑1/5]
        price_part = price_sequence.split(' [')[0]  # 移除方向指示符
        prices = price_part.split('->')
        if prices:
            try:
                current_price = float(prices[-1])
                tick_data.append({
                    'tick_id': len(tick_data),
                    'price': current_price,
                    'timestamp': timestamp
                })
            except ValueError:
                continue  # 跳过无法解析的价格

    # 如果还是数据不足，尝试其他模式
    if len(tick_data) < 100:
        # 尝试匹配买入信号中的价格 - 更新日期格式
        signal_price_pattern = r'【2025-07-\d{2} ([\d:]+)\.[\d]+】.*?价格[:\s]*([\d.]+)'
        signal_matches = re.finditer(signal_price_pattern, content)

        for match in signal_matches:
            time_part = match.group(1)
            try:
                price = float(match.group(2))
                # 使用实际日期而不是硬编码
                timestamp = f'2025-07-07 {time_part}'  # 更新为实际日期

                # 避免重复添加
                if not any(t['price'] == price for t in tick_data):
                    tick_data.append({
                        'tick_id': len(tick_data),
                        'price': price,
                        'timestamp': timestamp
                    })
            except (ValueError, IndexError):
                continue  # 跳过无法解析的价格

    # 按时间排序
    tick_data.sort(key=lambda x: x['timestamp'])

    # 为每个tick分配递增的ID
    for i, tick in enumerate(tick_data):
        tick['tick_id'] = i

    return tick_data

def add_price_tracking(signal_data, tick_data):
    """为信号数据添加买入后的价格跟踪信息"""
    signal_time = signal_data['触发时间']
    signal_price = signal_data['买入价格']

    if not signal_time or not signal_price:
        return

    # 找到信号触发时间对应的tick
    signal_tick_index = None
    for i, tick in enumerate(tick_data):
        if tick['timestamp'] == signal_time:
            signal_tick_index = i
            break

    if signal_tick_index is None:
        # 如果找不到精确匹配，找最接近的时间
        signal_datetime = datetime.strptime(signal_time, '%Y-%m-%d %H:%M:%S')
        min_diff = float('inf')
        for i, tick in enumerate(tick_data):
            try:
                tick_datetime = datetime.strptime(tick['timestamp'], '%Y-%m-%d %H:%M:%S')
                diff = abs((tick_datetime - signal_datetime).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    signal_tick_index = i
            except:
                continue

    if signal_tick_index is not None:
        # 分析买入后不同时间段的价格变化
        analyze_price_changes(signal_data, tick_data, signal_tick_index, signal_price)

def analyze_price_changes(signal_data, tick_data, start_index, entry_price):
    """分析买入后的价格变化"""

    # 初始化跟踪字段
    signal_data.update({
        '10tick后价格': '',
        '10tick后涨跌': '',
        '10tick后涨跌幅': '',
        '30tick后价格': '',
        '30tick后涨跌': '',
        '30tick后涨跌幅': '',
        '50tick后价格': '',
        '50tick后涨跌': '',
        '50tick后涨跌幅': '',
        '1分钟后价格': '',
        '1分钟后涨跌': '',
        '1分钟后涨跌幅': '',
        '3分钟后价格': '',
        '3分钟后涨跌': '',
        '3分钟后涨跌幅': '',
        '5分钟后价格': '',
        '5分钟后涨跌': '',
        '5分钟后涨跌幅': '',
        '最高价格': '',
        '最高涨幅': '',
        '最低价格': '',
        '最大回撤': ''
    })

    if start_index >= len(tick_data):
        return

    entry_time = datetime.strptime(tick_data[start_index]['timestamp'], '%Y-%m-%d %H:%M:%S')

    # 跟踪价格变化
    max_price = entry_price
    min_price = entry_price

    # 按tick数量跟踪
    tick_targets = [10, 30, 50]
    for target_ticks in tick_targets:
        target_index = start_index + target_ticks
        if target_index < len(tick_data):
            target_price = tick_data[target_index]['price']
            change = target_price - entry_price
            change_pct = (change / entry_price) * 100

            signal_data[f'{target_ticks}tick后价格'] = f'{target_price:.4f}'
            signal_data[f'{target_ticks}tick后涨跌'] = f'{change:+.4f}'
            signal_data[f'{target_ticks}tick后涨跌幅'] = f'{change_pct:+.2f}%'

    # 按时间跟踪
    time_targets = [1, 3, 5]  # 分钟
    for target_minutes in time_targets:
        target_time = entry_time + timedelta(minutes=target_minutes)

        # 找到最接近目标时间的tick
        closest_tick = None
        min_diff = float('inf')

        for i in range(start_index, len(tick_data)):
            try:
                tick_time = datetime.strptime(tick_data[i]['timestamp'], '%Y-%m-%d %H:%M:%S')
                diff = abs((tick_time - target_time).total_seconds())
                if diff < min_diff:
                    min_diff = diff
                    closest_tick = tick_data[i]
                elif diff > min_diff:
                    break  # 时间开始变远，停止搜索
            except:
                continue

        if closest_tick:
            target_price = closest_tick['price']
            change = target_price - entry_price
            change_pct = (change / entry_price) * 100

            signal_data[f'{target_minutes}分钟后价格'] = f'{target_price:.4f}'
            signal_data[f'{target_minutes}分钟后涨跌'] = f'{change:+.4f}'
            signal_data[f'{target_minutes}分钟后涨跌幅'] = f'{change_pct:+.2f}%'

    # 计算最高价和最低价（在接下来的100个tick内）
    end_index = min(start_index + 100, len(tick_data))
    for i in range(start_index, end_index):
        price = tick_data[i]['price']
        max_price = max(max_price, price)
        min_price = min(min_price, price)

    # 计算最高涨幅和最大回撤
    max_gain = ((max_price - entry_price) / entry_price) * 100
    max_drawdown = ((min_price - entry_price) / entry_price) * 100

    signal_data['最高价格'] = f'{max_price:.4f}'
    signal_data['最高涨幅'] = f'{max_gain:+.2f}%'
    signal_data['最低价格'] = f'{min_price:.4f}'
    signal_data['最大回撤'] = f'{max_drawdown:+.2f}%'

def extract_signal_details(signal_text, signal_num, signal_type):
    """从信号文本中提取详细信息"""
    
    data = {
        '序号': signal_num,
        '信号类型': signal_type,
        '触发时间': '',
        '买入价格': '',
        '当时VWAP': '',
        '价格差异': '',
        '差异百分比': '',
        '买入前持仓': '',
        '买入后持仓': '',
        '买入数量': '',
        'VWAP模式类型': '',
        '买入质量': '',
        '时机评估': '',
        '模式分类': '',
        '触发序列': '',
        '震荡周期信息': ''
    }
    
    # 提取触发时间
    time_match = re.search(r'触发时间: ([\d-]+ [\d:]+)', signal_text)
    if time_match:
        data['触发时间'] = time_match.group(1)
    
    # 提取买入价格
    price_match = re.search(r'买入价格: ([\d.]+)', signal_text)
    if price_match:
        data['买入价格'] = float(price_match.group(1))
    
    # 提取VWAP
    vwap_match = re.search(r'当时VWAP: ([\d.]+)', signal_text)
    if vwap_match:
        data['当时VWAP'] = float(vwap_match.group(1))
    
    # 提取价格差异
    diff_match = re.search(r'价格差异: ([+-][\d.]+) \(([+-][\d.]+%)\)', signal_text)
    if diff_match:
        data['价格差异'] = diff_match.group(1)
        data['差异百分比'] = diff_match.group(2)
    
    # 提取持仓信息
    before_match = re.search(r'买入前持仓: (\d+)张', signal_text)
    if before_match:
        data['买入前持仓'] = f"{before_match.group(1)}张"
    
    after_match = re.search(r'买入后持仓: (\d+)张', signal_text)
    if after_match:
        data['买入后持仓'] = f"{after_match.group(1)}张"
    
    quantity_match = re.search(r'买入数量: (\d+)张', signal_text)
    if quantity_match:
        data['买入数量'] = f"{quantity_match.group(1)}张"
    
    # 提取VWAP模式分析 - 更新为新格式
    # 新格式: VWAP模式分析: 从局部低点反弹1.83%
    vwap_analysis_match = re.search(r'VWAP模式分析: (.*?)(?:\n|$)', signal_text)
    if vwap_analysis_match:
        data['VWAP模式类型'] = vwap_analysis_match.group(1).strip()

    # 兼容旧格式
    if not data['VWAP模式类型']:
        pattern_match = re.search(r'模式类型: (.*?)(?:\n|$)', signal_text)
        if pattern_match:
            data['VWAP模式类型'] = pattern_match.group(1).strip()

    # 提取买入质量和时机评估（如果存在）
    quality_match = re.search(r'买入质量: (.*?)(?:\n|$)', signal_text)
    if quality_match:
        data['买入质量'] = quality_match.group(1).strip()

    timing_match = re.search(r'时机评估: (.*?)(?:\n|$)', signal_text)
    if timing_match:
        data['时机评估'] = timing_match.group(1).strip()
    
    # 判断模式分类 - 更新为新的VWAP模式分析格式
    vwap_type = data['VWAP模式类型'].lower()
    if '向上突破' in vwap_type or '向上穿过vwap' in vwap_type or '突破vwap' in vwap_type:
        data['模式分类'] = 'A类-向上突破'
    elif '高点回撤' in vwap_type or '回撤' in vwap_type:
        data['模式分类'] = 'B类-高点回撤'
    elif '低点反弹' in vwap_type or '反弹' in vwap_type:
        data['模式分类'] = 'C类-低点反弹'
    elif '跌破vwap' in vwap_type or '向下穿过vwap' in vwap_type:
        data['模式分类'] = 'C类-跌破VWAP'
    else:
        data['模式分类'] = '其他'
    
    # 检查是否为震荡信号（通过查找周期信息）
    # 支持多种格式：
    # 格式1: 周期1: [30-34] 0.0239→0.0243 上涨
    # 格式2: 周期1: [1120-1124] 0.0235→0.0240 上涨
    cycle_matches = re.findall(r'周期\d+: \[[^\]]+\] ([\d.]+)→([\d.]+) (上涨|下跌)', signal_text)

    # 如果第一种格式没匹配到，尝试其他格式
    if not cycle_matches:
        # 尝试匹配带空格的格式
        cycle_matches = re.findall(r'周期(\d+): \[[^\]]+\] ([\d.]+)→([\d.]+) (上涨|下跌)', signal_text)
        if cycle_matches:
            # 重新组织匹配结果，去掉周期号
            cycle_matches = [(start, end, direction) for _, start, end, direction in cycle_matches]

    # 检查是否包含震荡信号触发的关键词（更精确的判断）
    # 优先检查连续信号的明确标识
    has_continuous_trigger = '触发连续买入信号' in signal_text or '连续买入' in signal_text
    has_oscillation_trigger = '触发震荡买入信号' in signal_text

    # 添加调试信息
    signal_num = data.get('序号', 0)
    if signal_num <= 5:  # 只对前5个信号显示调试信息
        print(f"🔍 调试信号#{signal_num}:")
        print(f"   has_continuous_trigger: {has_continuous_trigger}")
        print(f"   has_oscillation_trigger: {has_oscillation_trigger}")
        if has_continuous_trigger:
            print(f"   连续信号位置: {signal_text.find('触发连续买入信号')}")
        if has_oscillation_trigger:
            print(f"   震荡信号位置: {signal_text.find('触发震荡买入信号')}")

    # 如果同时包含两种信号，以实际触发的为准
    if has_continuous_trigger and has_oscillation_trigger:
        # 检查哪个信号在后面（更接近实际触发）
        continuous_pos = signal_text.rfind('触发连续买入信号')
        oscillation_pos = signal_text.rfind('触发震荡买入信号')
        is_oscillation = oscillation_pos > continuous_pos
    elif has_oscillation_trigger:
        is_oscillation = True
    else:
        is_oscillation = False

    if is_oscillation and cycle_matches:
        # 震荡信号：更新信号类型
        data['信号类型'] = f'震荡{signal_type}'

        # 构建震荡序列：周期1起始→周期1结束→周期2起始→周期2结束→周期3起始→周期3结束
        sequence_parts = []
        for start_price, end_price, direction in cycle_matches:
            sequence_parts.extend([start_price, end_price])

        if len(sequence_parts) >= 6:  # 至少3个周期
            data['触发序列'] = '→'.join(sequence_parts[:6])

        # 保存震荡周期详细信息
        cycle_info = []
        for i, (start_price, end_price, direction) in enumerate(cycle_matches, 1):
            cycle_info.append(f'周期{i}: {start_price}→{end_price} {direction}')
        data['震荡周期信息'] = '; '.join(cycle_info)
    elif is_oscillation:
        # 即使没有找到完整的周期信息，但明确标识为震荡信号
        data['信号类型'] = f'震荡{signal_type}'

        # 从日志中精确提取震荡信号的触发序列
        # 格式: 触发震荡买入信号！10009534.SHO: 0.0270 (价格: 0.0270)
        oscillation_pattern = r'触发震荡买入信号！10009534\.SHO: ([\d.]+) \(价格: ([\d.]+)\)'
        oscillation_match = re.search(oscillation_pattern, signal_text)
        if oscillation_match:
            trigger_price = oscillation_match.group(1)
            data['触发序列'] = f"震荡信号触发价格: {trigger_price}"
            print(f"✅ 震荡信号序号{data.get('序号', 0)}提取成功: 震荡信号触发价格: {trigger_price}")
        else:
            # 备选方案：尝试从价格序列中提取触发序列
            sequence_match = re.search(r'([\d.]+)->([\d.]+)->([\d.]+)->([\d.]+)->([\d.]+)', signal_text)
            if sequence_match:
                sequence = '→'.join([sequence_match.group(i) for i in range(1, 6)])
                data['触发序列'] = sequence
                print(f"⚠️ 震荡信号序号{data.get('序号', 0)}使用备选方案: {sequence}")
            else:
                print(f"❌ 震荡信号序号{data.get('序号', 0)}提取失败")

        data['震荡周期信息'] = '震荡信号但周期信息不完整'
    else:
        # 连续信号：更新信号类型
        data['信号类型'] = f'连续{signal_type}'

        # 从日志中精确提取连续信号的触发序列
        # 格式: 触发连续买入信号！10009534.SHO: 0.0324->0.0330->0.0332->0.0334->0.0335 (价格: 0.0335)
        continuous_pattern = r'触发连续买入信号！10009534\.SHO: ([\d.]+(?:->[\d.]+){4}) \(价格: ([\d.]+)\)'
        continuous_match = re.search(continuous_pattern, signal_text)
        if continuous_match:
            sequence_str = continuous_match.group(1)
            sequence = sequence_str.replace('->', '→')
            data['触发序列'] = sequence
            print(f"✅ 连续信号序号{data.get('序号', 0)}提取成功: {sequence}")
        else:
            # 备选方案：提取5个tick价格序列
            sequence_match = re.search(r'([\d.]+)->([\d.]+)->([\d.]+)->([\d.]+)->([\d.]+)', signal_text)
            if sequence_match:
                sequence = '→'.join([sequence_match.group(i) for i in range(1, 6)])
                data['触发序列'] = sequence
                print(f"⚠️ 连续信号序号{data.get('序号', 0)}使用备选方案: {sequence}")
            else:
                print(f"❌ 连续信号序号{data.get('序号', 0)}提取失败")
    
    return data

def main():
    """主函数"""
    log_file = '1.txt'
    output_file = '买入信号含价格跟踪数据.xlsx'

    print("开始提取买入信号数据...")

    # 先测试tick数据提取
    with open(log_file, 'r', encoding='utf-8') as f:
        content = f.read()
    tick_data = extract_tick_data(content)
    print(f"提取到 {len(tick_data)} 条tick数据")
    if len(tick_data) > 0:
        print("前5条tick数据:")
        for i, tick in enumerate(tick_data[:5]):
            print(f"  {i+1}. {tick['timestamp']} - {tick['price']}")

    signal_data = extract_signal_data(log_file)
    
    print(f"成功提取 {len(signal_data)} 条买入信号数据")
    
    # 创建DataFrame
    df = pd.DataFrame(signal_data)
    
    # 保存为Excel文件
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        df.to_excel(writer, sheet_name='买入信号详细数据', index=False)

        # 调整列宽
        worksheet = writer.sheets['买入信号详细数据']
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width

    # 同时保存为CSV文件
    csv_file = '买入信号含价格跟踪数据.csv'
    df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    
    print(f"数据已保存到: {output_file}")
    
    # 显示统计信息
    print("\n=== 数据统计 ===")
    print(f"总信号数量: {len(signal_data)}")
    print(f"信号类型分布:")
    type_counts = df['信号类型'].value_counts()
    for signal_type, count in type_counts.items():
        print(f"  {signal_type}: {count}次")
    
    print(f"\n模式分类分布:")
    pattern_counts = df['模式分类'].value_counts()
    for pattern, count in pattern_counts.items():
        print(f"  {pattern}: {count}次")

if __name__ == "__main__":
    main()
