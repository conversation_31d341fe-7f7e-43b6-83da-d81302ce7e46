#coding:gbk
from datetime import datetime

def init(ContextInfo):
    """初始化函数"""
    print("🚀 精确VWAP验证策略启动")

    # 固定测试的期权代码
    ContextInfo.test_option = '10009356.SHO'

    # 设置要测试的历史日期（可以修改为任何交易日）
    ContextInfo.test_date = '20250704'  # 修改为您想测试的日期

    print(f"🎯 测试合约: {ContextInfo.test_option}")
    print(f"� 测试日期: {ContextInfo.test_date}")

    # 对于历史数据验证，不需要订阅实时数据
    print("💡 历史数据验证模式，无需订阅实时数据")

    # 固定的测试时间点
    ContextInfo.test_times = [
        '10:00:00',  # 开盘后30分钟
        '10:30:00',  # 开盘后1小时
        '11:00:00',  # 上午11点
        '11:30:00',  # 上午收盘前
        '13:30:00',  # 下午开盘后30分钟
        '14:00:00',  # 下午2点
        '14:30:00',  # 下午2点半
        '15:00:00'   # 收盘时
    ]

def handlebar(ContextInfo):
    """主处理函数"""
    if not ContextInfo.is_last_bar():
        return

    print("\n" + "="*80)
    print(f"🧪 精确计算合约 {ContextInfo.test_option} 的VWAP")
    print("="*80)

    # 使用设置的测试日期，而不是当前日期
    test_date = getattr(ContextInfo, 'test_date', '20250103')

    print(f"📅 测试日期: {test_date}")
    print(f"🎯 测试合约: {ContextInfo.test_option}")
    print()
    
    # 存储结果用于最终汇总
    results = []
    
    for time_point in ContextInfo.test_times:
        print(f"⏰ 计算时间点: {time_point}")
        print("-" * 50)
        
        # 构造完整的时间字符串
        target_time_str = test_date + time_point.replace(':', '')

        # 计算该时间点的精确VWAP
        vwap_result = calculate_precise_vwap(ContextInfo, ContextInfo.test_option, test_date, target_time_str)
        
        if vwap_result:
            results.append({
                'time': time_point,
                'vwap': vwap_result['vwap'],
                'method': vwap_result['method'],
                'data_points': vwap_result['data_points']
            })
            print(f"✅ VWAP: {vwap_result['vwap']:.6f} (方法: {vwap_result['method']})")
            print(f"   数据点数: {vwap_result['data_points']}")
        else:
            print("❌ 计算失败")
            results.append({
                'time': time_point,
                'vwap': None,
                'method': 'Failed',
                'data_points': 0
            })
        
        print()
    
    # 输出汇总表格
    print("📊 精确VWAP计算结果汇总表")
    print("="*80)
    print("时间点    | VWAP价格   | 计算方法           | 数据点数")
    print("-"*80)
    
    for result in results:
        if result['vwap']:
            print(f"{result['time']} | {result['vwap']:.6f}   | {result['method']:<18} | {result['data_points']:<8}")
        else:
            print(f"{result['time']} | 计算失败     | {result['method']:<18} | {result['data_points']:<8}")
    
    print("="*80)
    print("💡 对比验证:")
    print("请将上述结果与K线图黄色均价线对比")
    print("如果仍有偏差，说明需要进一步调整计算方法")

def calculate_precise_vwap(C, option_code, today_str, target_time_str):
    """计算精确的VWAP"""
    try:
        # 获取分笔数据，设置subscribe=True确保数据可用
        print(f"📡 正在获取 {option_code} 从 {today_str}093200 到 {target_time_str} 的分笔数据...")

        # 首先检查合约是否存在
        try:
            # 尝试获取合约基本信息
            basic_info = C.get_stock_list_in_sector('期权')
            if basic_info and option_code not in [stock['code'] for stock in basic_info]:
                print(f"⚠️ 合约 {option_code} 可能已到期或不存在")
        except:
            pass

        tick_data = C.get_market_data_ex(
            fields=['lastPrice', 'volume', 'amount'],
            stock_code=[option_code],
            period='tick',
            start_time=today_str + '093200',  # 从9:32开始，避开开盘异常波动
            end_time=target_time_str,         # 到指定时间
            count=-1,
            subscribe=False  # 历史数据不需要订阅
        )

        print(f"📊 数据获取结果: {list(tick_data.keys()) if tick_data else '无数据'}")

        if tick_data and option_code in tick_data:
            df = tick_data[option_code]
            print(f"📋 数据框信息: 形状={df.shape if hasattr(df, 'shape') else 'N/A'}, 空={df.empty if hasattr(df, 'empty') else 'N/A'}")

            if hasattr(df, 'empty') and not df.empty and len(df) > 0:
                print(f"📈 获取到{len(df)}条分笔数据")
                print(f"📊 数据列: {list(df.columns) if hasattr(df, 'columns') else 'N/A'}")
                print(f"📊 价格范围: {df['lastPrice'].min():.4f} - {df['lastPrice'].max():.4f}" if 'lastPrice' in df.columns else "无价格数据")

                # 方法1: 使用真实的成交记录计算VWAP
                vwap1 = calculate_vwap_from_trades(df)
                if vwap1:
                    return vwap1

                # 方法2: 使用价格加权平均
                vwap2 = calculate_vwap_price_weighted(df)
                if vwap2:
                    return vwap2

                # 方法3: 使用简单价格平均
                vwap3 = calculate_simple_average(df)
                if vwap3:
                    return vwap3
            else:
                print("❌ 数据框为空或无有效数据")
                print("💡 可能原因:")
                print("   1. 非交易时间")
                print("   2. 合约已到期")
                print("   3. 网络连接问题")
                print("   4. 权限不足")
        else:
            print("❌ 未获取到指定合约的数据")
            print("💡 可能原因:")
            print("   1. 合约代码错误")
            print("   2. 合约已到期")
            print("   3. 数据源问题")

        return None

    except Exception as e:
        print(f"❌ 计算失败: {e}")
        print(f"❌ 错误类型: {type(e).__name__}")
        return None

def calculate_vwap_from_trades(df):
    """从真实成交记录计算VWAP"""
    try:
        # 只保留有成交的记录
        trades_df = df[(df['volume'] > 0) & (df['lastPrice'] > 0)].copy()
        
        if len(trades_df) == 0:
            return None
        
        # 计算成交量增量
        trades_df['volume_delta'] = trades_df['volume'].diff().fillna(trades_df['volume'])
        
        # 过滤掉负增量（可能的数据重置）
        trades_df = trades_df[trades_df['volume_delta'] > 0]
        
        if len(trades_df) == 0:
            return None
        
        # 计算VWAP = Σ(价格 × 成交量增量) / Σ(成交量增量)
        total_value = (trades_df['lastPrice'] * trades_df['volume_delta']).sum()
        total_volume = trades_df['volume_delta'].sum()
        
        if total_volume > 0:
            vwap = total_value / total_volume
            print(f"📊 成交记录VWAP: {vwap:.6f} (基于{len(trades_df)}笔成交)")
            return {
                'vwap': vwap,
                'method': '成交记录VWAP',
                'data_points': len(trades_df)
            }
        
        return None
        
    except Exception as e:
        print(f"成交记录计算失败: {e}")
        return None

def calculate_vwap_price_weighted(df):
    """计算价格加权VWAP"""
    try:
        # 使用所有有效价格记录
        valid_df = df[(df['lastPrice'] > 0)].copy()
        
        if len(valid_df) == 0:
            return None
        
        # 如果有成交量信息，使用成交量加权
        if 'volume' in valid_df.columns:
            # 使用当前成交量作为权重
            weights = valid_df['volume'].fillna(1)  # 如果没有成交量，权重为1
            weights = weights.replace(0, 1)  # 避免0权重
            
            weighted_sum = (valid_df['lastPrice'] * weights).sum()
            total_weight = weights.sum()
            
            if total_weight > 0:
                vwap = weighted_sum / total_weight
                print(f"📊 价格加权VWAP: {vwap:.6f} (基于{len(valid_df)}个价格点)")
                return {
                    'vwap': vwap,
                    'method': '价格加权VWAP',
                    'data_points': len(valid_df)
                }
        
        return None
        
    except Exception as e:
        print(f"价格加权计算失败: {e}")
        return None

def calculate_simple_average(df):
    """计算简单价格平均"""
    try:
        valid_prices = df[df['lastPrice'] > 0]['lastPrice']
        
        if len(valid_prices) > 0:
            avg_price = valid_prices.mean()
            print(f"📊 简单均价: {avg_price:.6f} (基于{len(valid_prices)}个价格点)")
            return {
                'vwap': avg_price,
                'method': '简单价格平均',
                'data_points': len(valid_prices)
            }
        
        return None
        
    except Exception as e:
        print(f"简单平均计算失败: {e}")
        return None
